﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.file;
using LinCom.Imp;
using LinCom.Model;
using Microsoft.Ajax.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Web;
using System.Web.Script.Serialization;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class reset : System.Web.UI.Page
    {
        private int info;
        string nscno; static string pht;
        TypeOrganisation_Class cat = new TypeOrganisation_Class();
        ITypeOrganisation objcat = new TypeOrganisationImp();
        Organisation_Class co = new Organisation_Class();
        Organisation_Class org = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        Province_Class prov = new Province_Class();
        IProvince objp = new ProvinceImp();
        CommuneClass znc = new CommuneClass();
        ICommune objco = new CommuneImp();
        ICommonCode com = new CommonCode();
        DomaineInterventionOrganisation_Class actco = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objactco = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class domai = new DomaineIntervention_Class();
        IDomaineIntervention objdom = new DomaineInterventionImp();
        Membre_Class mem = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        IMembre obj = new MembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        DataTable dat = new DataTable();
        long index;
        static string token;
        protected void Page_Load(object sender, EventArgs e)
        {
            // nscno = Request.QueryString["IDCOOP"];
            token = Request.QueryString["token"];
            if (!IsPostBack)
            {
                initial_msg();
              
                if (string.IsNullOrEmpty(token))
                {
                    Response.Redirect("~/forgot.aspx");
                    //div_msg_error.Visible = true; msg_error.InnerText = "Lien invalide ou expiré.";

                    //btnreng.Disabled = true;
                    //return;
                }
            }
        }
        void vider()
        {
            txt_pswd.Value = "";
            txt_confirmpswd.Value = "";
           
        }
        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;

        }

        private void ShowAlert(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "alert", $"alert('{message}');", true);
        }
        protected void btn_enreg_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx");
        }

      
        protected void aci_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#hero");
        }

        protected void prop_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#about");
        }

        protected void serv_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#services");
        }

        protected void inst_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#team");
        }

        protected void cont_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#contact");
        }


        void NouveauCoordonnee()
        {
            try
            {
               

                if (txt_pswd.Value == "" || txt_confirmpswd.Value == "" )
                {
                    div_msg_error.Visible = true; msg_error.InnerText = "Renseignez tous les champs";

                    return;

                }
               
                // Vérification de l'égalité des emails
                if (txt_pswd.Value.Trim() != txt_confirmpswd.Value.Trim())
                {
                    div_msg_error.Visible = true; msg_error.InnerText = "Le mot de passe et sa confirmation doivent être identiques.";

                    return;
                }

                if (txt_confirmpswd.ToString().Length < 8)
                {
                    div_msg_error.Visible = true; msg_error.InnerText = "Le mot de passe doit comporter au moins 8 caractères.";

                    return;
                }

                mem.motpasse = BCrypt.Net.BCrypt.HashPassword( txt_confirmpswd.Value);
                mem.ResetToken = null;
                mem.ResetTokenExpiry = null;
               
                info = obj.Modifier(mem,token,-1,2);
                if (info == 1)
                {
                    Response.Redirect("~/login.aspx");
                  vider();
                }
                else
                {
                    div_msg_error.Visible = true; msg_error.InnerText = "Verifiez bien vos Informations.";

                    return;
                   
                }
               
            }
            catch (Exception ex)
            {
                // logguer l'exception pour le debug
                System.Diagnostics.Debug.WriteLine("Erreur : " + ex.Message);
            }
        }

        protected void btnreng_ServerClick(object sender, EventArgs e)
        {
            NouveauCoordonnee();
        }
    }
}