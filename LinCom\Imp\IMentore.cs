﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IMentore
    {
        void AfficherDetails(int mentoreId, Mentore_Class mentore);
        int Ajouter(Mentore_Class mentore);
        int Modifier(Mentore_Class mentore,int id, long idorg,int cd);
        int Supprimer(int mentoreId);
        void ChargerMentoreGridview(GridView gdv, int id, long idprog, long idorg, string name, string status, int cd);
        void ChargerMentoreListview(ListView gdv, int id, long idprog, long idorg, string name, string status, int cd);
        int count(int id, long idprog, long idmem, long idorg, int cd);

        void ChargerMentoresParProgramme(GridView gdv, int programmeMentoratId);
        void chargerMentore(DropDownList lst);
        void ChargerMentoresParMembre(GridView gdv, long membreId);
        int count(long id, long idorg, long idmem, string code, string statut, int cd);


    }
}
