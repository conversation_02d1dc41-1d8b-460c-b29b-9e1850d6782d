﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace LinCom.Classe
{
    public class Organisation_Class
    {
        public long OrganisationId { get; set; }
        public string Nom { get; set; }
        public int TypeOrganisationId { get; set; }
        public int ProvinceId { get; set; }
        public int CommuneId { get; set; }
        public string Email { get; set; }
        public string Telephone { get; set; }
        public string SiteWeb { get; set; }
        public string Logo { get; set; }
        public string RS { get; set; }
        public string sigle { get; set; }
        public string RC { get; set; }
        public string RC_doc { get; set; }
        public string NIF { get; set; }
        public string NIF_doc { get; set; }
        public string facebook { get; set; }
        public string twitter { get; set; }
        public string instagramme { get; set; }
        public string linkedin { get; set; }
        public string youtube { get; set; }
        public string province { get; set; }
        public string commune { get; set; }
        public string Description { get; set; }
        public string Vision { get; set; }
        public string Mission { get; set; }
        public int? NbreHomme { get; set; }
        public int? NbreFemme { get; set; }
        public string Enregistre { get; set; }
        public DateTime? DateCreation { get; set; }
        public string Statut { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string name { get; set; }
        public string Adresse { get; set; }

        public Nullable<double> Latitude { get; set; }
        public Nullable<double> Longitude { get; set; }
    }
}