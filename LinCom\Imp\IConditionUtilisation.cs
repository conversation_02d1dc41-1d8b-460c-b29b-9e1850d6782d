﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LinCom.Imp
{
    internal interface IConditionUtilisation
    {
        void AfficherDetails(int idCondition, ConditionUtilisation_Class conditionClass);
        int Ajouter(ConditionUtilisation_Class conditionClass);
        void AfficherDetails(string code, ConditionUtilisation_Class conditionClass);
        int Modifier(ConditionUtilisation_Class conditionClass, int idCondition);
        int ID();
        int countID();
    }
}
