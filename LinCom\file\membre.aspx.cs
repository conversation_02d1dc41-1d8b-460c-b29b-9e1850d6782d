﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using Microsoft.Ajax.Utilities;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Runtime.Remoting;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class membre : System.Web.UI.Page
    {
        private int info;
        string membreId,nsco;
        Membre_Class membreClass = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objMembre = new MembreImp();
        IProvince objProvince = new ProvinceImp();
        ICommune objCommune = new CommuneImp();
        ICommonCode co = new CommonCode();
        RoleMembre_Class rl= new RoleMembre_Class();    
        IRoleMembre objrl=new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();

        static string imge,nameorg;
        long ide,idorg;
        static int rolid;

        protected void Page_Load(object sender, EventArgs e)
        {
            membreId = Request.QueryString["id"];
            nsco = Request.QueryString["id"];
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin
                ide = Convert.ToInt64(idperso.Value);
                rolid = Convert.ToInt32(role.Value);

            }
            else Response.Redirect("~/login.aspx");

            objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
            idorg = Convert.ToInt64(memorg.OrganisationId);
            objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
            nameorg = org.Nom;

            if (!IsPostBack)
            {
                //if (Session["UserId"] == null || Session["RoleId"] == null)
                //{
                //    Response.Redirect("~/login.aspx");
                //}
                //else
                //{
                //    ide = Convert.ToInt32(Session["UserId"].ToString());
                //    rolid = Convert.ToInt32(Session["RoleId"].ToString());
                //    objrl.AfficherDetails(Convert.ToInt32(rolid), rl);

                //}
                //if (rl.NomRole == "membre" || rl.NomRole == "organisation")
                //    Response.Redirect("~/login.aspx");

                //objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                //idorg = Convert.ToInt64(memorg.OrganisationId);
                //objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                //nameorg = org.Nom;
               
                InitialiserMessages();
                ChargerProvinces();
                
                if (membreId != null)
                {
                    btnEnregistrer.InnerText = "Modifier";
                    AfficherDetails();
                }
                else
                {
                    btnEnregistrer.InnerText = "Enregistrer";
                }
            }
        }

        private void InitialiserMessages()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
            objrl.chargerRole(drpdrole,-1,idorg,0);
        }

        private void ChargerProvinces()
        {
            try
            {
                objProvince.chargerProvince(drpdprov);
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Erreur lors du chargement des provinces: " + ex.Message;
            }
        }

        protected void drpdprov_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (drpdprov.SelectedValue != null && drpdprov.SelectedValue != "-1")
            {
                objCommune.chargerCommune(drpdcom, Convert.ToInt32(drpdprov.SelectedValue));
            }


        }


        protected void btnEnregistrer_Click(object sender, EventArgs e)
        {
           

            if (membreId == null)
            {
                AjouterMembre();
            }
            else
            {
                ModifierMembre();
            }
        }
        private string UploadImage()
        {
            if (fileupd.HasFile)
            {
                string fileName = Path.GetFileName(fileupd.FileName);
                string extension = Path.GetExtension(fileName).ToLower();

                string[] validExtensions = { ".png", ".jpeg", ".gif", ".jpg", ".jfif" };

                if (!validExtensions.Contains(extension))
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "La photo doit avoir ces extensions : .png, .jpg, .gif, .jpeg, .jfif. ";

                    return null;
                }

                if (fileupd.PostedFile.ContentLength > 104857600)
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Votre photo est trop volumineuse. Elle doit faire moins de 100MB.";

                    return null;
                }

                string path = Server.MapPath("~/file/membr") + fileName;
                fileupd.SaveAs(path);
                imge = fileName;

            }
            else
            {
                if (nsco == null)
                {
                    imge = "emptyuser.png";
                }
                else
                {
                    objMembre.AfficherDetails(Convert.ToInt64(nsco), mem);

                    imge = mem.PhotoProfil;

                }
            }
            return imge;

        }

        private void AjouterMembre()
        {
            try
            {
               
                if (string.IsNullOrEmpty(txtNom.Text) || string.IsNullOrEmpty(txtPrenom.Text) ||
                    string.IsNullOrEmpty(txtEmail.Text) || drpdsexe.SelectedValue == "-1" || drpdprov.SelectedValue=="-1" || drpdcom
                    .SelectedValue=="-1" || txtposte.Text=="")
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Veuillez remplir tous les champs obligatoires";
                    return;
                }
                // Vérification de l'égalité des emails
                if (txtEmail.Text.Trim() != txtConfirmEmail.Text.Trim())
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "L’adresse e-mail et sa confirmation doivent être identiques.";
                    return;

                }
                // Vérification de l'égalité des emails
                if (txtpswd.Text.Trim() != txtconfirmpswd.Text.Trim())
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Le mot de passe et sa confirmation doivent être identiques.";
                    return;
                    
                }

                objMembre.AfficherDetails(txtConfirmEmail.Text, memb, 1);
                if (memb.Email == txtConfirmEmail.Text)
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "ce membre existe déjà. Veuillez consulter votre email pour avoir plus d'informations.";
                    return;
                    
                }
                mem.Nom = txtNom.Text;
                mem.Prenom = txtPrenom.Text;
                mem.Email = txtConfirmEmail.Text;
                mem.Telephone = txtTelephone.Text;
                mem.Sexe = drpdsexe.SelectedValue;
                mem.DateNaissance = Convert.ToDateTime(txtDateNaissance.Text); ;
                mem.ProvinceId = Convert.ToInt32(drpdprov.SelectedValue);
                mem.CommuneId = Convert.ToInt32(drpdcom.SelectedValue);
                mem.name = txtConfirmEmail.Text;
                mem.province = drpdprov.SelectedItem.ToString();
                mem.commune = drpdprov.SelectedItem.ToString();
                mem.Adresse = txtAdresse.Text;
                mem.username = txtConfirmEmail.Text;
                mem.motpasse = BCrypt.Net.BCrypt.HashPassword(txtconfirmpswd.Text);
                mem.statut = drpdstatut.SelectedValue;
                mem.IsActive = 1;
                mem.IsVerified = 1;
                mem.LastLogin = DateTime.Now;
                mem.ResetToken = null;
                mem.ResetTokenExpiry = null;
                mem.RoleMembreID = Convert.ToInt32(drpdrole.SelectedValue);

                mem.PhotoProfil = UploadImage();
                mem.facebook = "";
                mem.siteweb = "";
                mem.twitter = "";
                mem.instagramme = "";
                mem.linkedin = "";
                mem.youtube = "";
                mem.Biographie = txtBiographie.Value;
                mem.DateInscription = DateTime.Now;
                mem.LanguePreferee = "fr";
                // Remplir l'objet membre avec les données du formulaire
               
                // Ajouter le membre à la base de données
                info = objMembre.Ajouter(mem);
            
            
                if (info == 1)
                {
                    NouveauMembreOrganisation();

                    SendConfirmationEmail(txtConfirmEmail.Text, txtNom.Text + " " + txtPrenom.Text);

                  

                    div_msg_succes.Visible = true; div_msg_error.Visible = false;
                    msg_succes.Text = "Le membre a été enregistré avec succès";

                    // Réinitialiser le formulaire
                  ReinitialiserFormulaire();
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Erreur lors de l'enregistrement du membre";
                }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Une erreur s'est produite: " + ex.Message;
            }
        }
        private void SendConfirmationEmail(string toEmail, string name)
        {
            SmtpClient smtpClient = new SmtpClient("send.one.com", 587)
            {
                UseDefaultCredentials = true,
                Credentials = new System.Net.NetworkCredential("<EMAIL>", "Newpass1980"),
                DeliveryMethod = SmtpDeliveryMethod.Network,
                EnableSsl = true
            };
            string lienConnection = $"{HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority)}/login.aspx";


            MailMessage mail = new MailMessage
            {
                IsBodyHtml = true,
                From = new MailAddress("<EMAIL>"),
                Subject = "Inscription dans Linked Community Burundi - LinCom Burundi",
                Body = $@"
        Bonjour cher  <strong>{name}</strong>,<br/><br/>
        Bienvenue dans la plateforme <strong>Linked Community Burundi - LinCom Burundi</strong>. Nous sommes heureux de vous avoir comme membre.<br/><br/>
        Si vous avez des questions, vous pouvez nous contacter via <a href='mailto:<EMAIL>'><EMAIL></a>.<br/><br/>
        
        <strong>Voici les coordonnées pour gérer votre espace :</strong><br/>
        Username : {toEmail}<br/>
        Cliquez sur ce lien pour vous connecter :<br/>
        <a href='{lienConnection}'>{lienConnection}</a><br/><br/>
        Merci beaucoup.
    "
            };

            mail.To.Add(new MailAddress(toEmail));
            smtpClient.Send(mail);
        }

     

        void NouveauMembreOrganisation()
        {
            try
            {
                if (string.IsNullOrEmpty(txtNom.Text) || string.IsNullOrEmpty(txtPrenom.Text) ||
                   string.IsNullOrEmpty(txtEmail.Text) || drpdsexe.SelectedValue == "-1" || drpdprov.SelectedValue == "-1" || drpdcom
                   .SelectedValue == "-1" || txtposte.Text == "")
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Veuillez remplir tous les champs obligatoires";
                    return;

                }
               
                objMembre.AfficherDetails(txtConfirmEmail.Text, mem, 1);
                memorg.MembreId = mem.MembreId;
                memorg.OrganisationId = Convert.ToInt64(idorg);
                memorg.Poste = txtposte.Text;
                memorg.DateAdhesion =Convert.ToDateTime( txtDateAdhesion.Text);
                memorg.Name = txtConfirmEmail.Text;
                memorg.Statut = drpdstatut.SelectedValue;

                memorg.RoleMembreID =Convert.ToInt32( drpdrole.SelectedValue);

                objmemorg.Ajouter(memorg);

            }
            catch (Exception ex)
            {
                // logguer ou afficher les erreurs pour le débogage
                System.Diagnostics.Debug.WriteLine("Erreur dans MembreOrganisation : " + ex.Message);
            }
        }

        void ModifierMembreOrganisation()
        {
            try
            {
                if (string.IsNullOrEmpty(txtNom.Text) || string.IsNullOrEmpty(txtPrenom.Text) ||
                   string.IsNullOrEmpty(txtEmail.Text) || drpdsexe.SelectedValue == "-1" || drpdprov.SelectedValue == "-1" || drpdcom
                   .SelectedValue == "-1" || txtposte.Text == "")
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Veuillez remplir tous les champs obligatoires";
                    return;

                }
                // Vérification de l'égalité des emails
                if (txtEmail.Text.Trim() != txtConfirmEmail.Text.Trim())
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "L’adresse e-mail et sa confirmation doivent être identiques.";
                    return;

                }
                // Vérification de l'égalité des emails
                if (txtpswd.Text.Trim() != txtconfirmpswd.Text.Trim())
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Le mot de passe et sa confirmation doivent être identiques.";
                    return;

                }

                objMembre.AfficherDetails(txtConfirmEmail.Text, memb, 1);
                if (memb.Email == txtConfirmEmail.Text)
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "ce membre existe déjà. Veuillez consulter votre email pour avoir plus d'informations.";
                    return;

                }
                objMembre.AfficherDetails(txtConfirmEmail.Text, mem, 1);
                objmemorg.AfficherDetails(txtConfirmEmail.Text,memorga);

                memorg.Poste = txtposte.Text;
                memorg.Name = txtConfirmEmail.Text;
                memorg.Statut = drpdstatut.SelectedValue;
                memorg.RoleMembreID = Convert.ToInt32(drpdrole.SelectedValue);


                objmemorg.Modifier(memorg,Convert.ToInt64(memorga.MembresOrganisationId));

            }
            catch (Exception ex)
            {
                // logguer ou afficher les erreurs pour le débogage
                System.Diagnostics.Debug.WriteLine("Erreur dans MembreOrganisation : " + ex.Message);
            }
        }


        private void ModifierMembre()
        {
            try
            {
                if (string.IsNullOrEmpty(txtNom.Text) || string.IsNullOrEmpty(txtPrenom.Text) ||
                    string.IsNullOrEmpty(txtDateNaissance.Text) || drpdsexe.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Veuillez remplir tous les champs obligatoires";
                    return;
                }

                // Remplir l'objet membre avec les données du formulaire

                mem.Nom = txtNom.Text;
                mem.Prenom = txtPrenom.Text;
                mem.Email = txtConfirmEmail.Text;
                mem.Telephone = txtTelephone.Text;
                mem.Sexe = drpdsexe.SelectedValue;
                mem.DateNaissance = Convert.ToDateTime(txtDateNaissance.Text); ;
                mem.ProvinceId = Convert.ToInt32(drpdprov.SelectedValue);
                mem.CommuneId = Convert.ToInt32(drpdcom.SelectedValue);
                mem.name = txtConfirmEmail.Text;
                mem.province = drpdprov.SelectedItem.ToString();
                mem.commune = drpdprov.SelectedItem.ToString();
                mem.Adresse = txtAdresse.Text;
                mem.username = txtConfirmEmail.Text;
                mem.motpasse = BCrypt.Net.BCrypt.HashPassword(txtconfirmpswd.Text);
                mem.statut = drpdstatut.SelectedValue;
              
                mem.RoleMembreID = Convert.ToInt32(drpdrole.SelectedValue);

                mem.PhotoProfil = UploadImage();
                mem.Biographie = txtBiographie.Value;
                mem.DateInscription = DateTime.Now;
                // Remplir l'objet membre avec les données du formulaire
                

                info = objMembre.Modifier(mem,"", Convert.ToInt64(nsco),0);

                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    msg_succes.Text = "Le membre a été modifié avec succès";
                    ModifierMembreOrganisation();
                   
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Erreur lors de la modification du membre";
                }
                Response.Redirect("~/file/listmembre.aspx");
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Une erreur s'est produite: " + ex.Message;
            }
        }

        private void AfficherDetails()
        {
            try
            {
                if (membreId != null)
                {
                    int id = Convert.ToInt32(membreId);
                    objMembre.AfficherDetails(id, membreClass);

                    if (membreClass.MembreId > 0)
                    {
                        txtNom.Text = membreClass.Nom;
                        txtPrenom.Text = membreClass.Prenom;
                        drpdsexe.SelectedValue = membreClass.Sexe;
                        if (membreClass.DateNaissance.HasValue)
                            txtDateNaissance.Text = membreClass.DateNaissance.Value.ToString("yyyy-MM-dd");
                        else
                            txtDateNaissance.Text = string.Empty;
                        txtEmail.Text = membreClass.Email;
                        txtTelephone.Text = membreClass.Telephone;

                        if (membreClass.ProvinceId > 0)
                        {
                            drpdprov.SelectedValue = membreClass.ProvinceId.ToString();
                            objCommune.chargerCommune(drpdcom, membreClass.ProvinceId.Value);

                            if (membreClass.CommuneId > 0)
                                drpdcom.SelectedValue = membreClass.CommuneId.ToString();
                        }

                        txtAdresse.Text = membreClass.Adresse;

                      

                        if (!string.IsNullOrEmpty(membreClass.statut))
                            drpdstatut.SelectedValue = membreClass.statut;

                        txtBiographie.Value = membreClass.Biographie;
                    }
                    else
                    {
                        div_msg_error.Visible = true;
                        msg_error.Text = "Le membre demandé n'existe pas";
                    }
                }
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Erreur lors du chargement des données: " + ex.Message;
            }
        }
        protected void btnEnregistrer_ServerClick(object sender, EventArgs e)
        {
            // Vérifier la validation côté serveur
            if (!Page.IsValid)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Veuillez corriger les erreurs avant de continuer.";
                return;
            }

            if (nsco == null)
            {
                AjouterMembre();
            }
            else
            {
                ModifierMembre();
            }

           // txtBiographie.Value = ide + "/" + idorg;
        }

        private void ReinitialiserFormulaire()
        {
            txtNom.Text = string.Empty;
            txtPrenom.Text = string.Empty;
            drpdsexe.SelectedValue = "-1";
            txtDateNaissance.Text = string.Empty;
            txtEmail.Text = string.Empty;
            txtTelephone.Text = string.Empty;
            drpdprov.SelectedValue = "-1";
            drpdcom.Items.Clear();
            drpdcom.Items.Add(new ListItem("Selectionner la Commune", "-1"));
            txtAdresse.Text = string.Empty;
            drpdrole.SelectedValue = "-1";
            txtposte.Text = "";
            txtDateAdhesion.Text = string.Empty;
            drpdstatut.SelectedValue = "-1";
            txtBiographie.Value = "";
        }
    }
}