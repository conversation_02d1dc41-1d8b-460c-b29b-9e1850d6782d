﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IFinancement
    {
        void AfficherDetails(long financementId, long idorg, long idpos, string name, int cd, Financement_Class financementClass);
        int Ajouter(Financement_Class financementClass);
        void ChargerFinancements(GridView gdv, long id, long idorg, string name, int cd);
        int Modifier(Financement_Class financementClass, long id, long idorg, string name, int cd);
        int Supprimer(long financementId, long idorg);
    }
}
