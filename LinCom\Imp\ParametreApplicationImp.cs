﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class ParametreApplicationImp : IParametreApplication
    {
        private ParamettreApplication parametre = new ParamettreApplication();

        public void AfficherDetails(int idParametre, ParametreApplication_Class parametreClass)
        {
            using (Connection con = new Connection())
            {
                var p = con.ParamettreApplications.FirstOrDefault(x => x.ParamettreId == idParametre);
                if (p != null)
                {

                    parametreClass.Valeur = p.Valeur;
                    parametreClass.Cle = p.Cle;


                }
            }
        }

        public int Ajouter(ParametreApplication_Class parametreClass)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si le paramètre existe déjà
                if (con.ParamettreApplications.Any(p => p.Valeur == parametreClass.Valeur))
                {
                    return 0;
                }


                parametre.Valeur = parametreClass.Valeur;
                parametre.Cle = parametreClass.Cle;


                try
                {
                    con.ParamettreApplications.Add(parametre);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }



        public void ChargerParametres(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var query = from p in con.ParamettreApplications
                            select new
                            {
                                p.ParamettreId,
                                p.Cle,

                            };

            

                gdv.DataSource = query.OrderBy(x => x.Cle).ToList();
                gdv.DataBind();
            }
        }

        public void chargerParametres(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.ParamettreApplications select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner la Parametre de l'application";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.ParamettreId.ToString();
                        item.Text = data.Valeur;
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }

        public void ChargerParametres(GridView gdv, string categorie = "")
        {
            throw new NotImplementedException();
        }

        public int ModifierValeur(string nomParametre, string nouvelleValeur, string cles)
        {
            using (Connection con = new Connection())
            {
                var p = con.ParamettreApplications.FirstOrDefault(x => x.Valeur == nomParametre);
                if (p != null)
                {
                    p.Valeur = nouvelleValeur;
                    p.Cle = cles;

                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public string ObtenirValeur(string nomParametre)
        {
            using (Connection con = new Connection())
            {
                var p = con.ParamettreApplications.FirstOrDefault(x => x.Valeur == nomParametre);
                return p?.Valeur;
            }
        }

        public int RestaurerParDefaut(int idParametre)
        {
            using (Connection con = new Connection())
            {
                var p = con.ParamettreApplications.FirstOrDefault(x => x.ParamettreId == idParametre);
                if (p != null)
                {
                    p.Valeur = p.Valeur;
                    p.Cle = p.Cle;


                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }
    }
}