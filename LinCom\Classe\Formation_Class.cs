﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace LinCom.Classe
{
    public class Formation_Class
    {
        public long FormationId { get; set; }
        public string Titre { get; set; }
        public string Description { get; set; }
        public Nullable<System.DateTime> DateFormation { get; set; }
        public string name { get; set; }
        public Nullable<System.DateTime> DateCreation { get; set; }
        public string statut { get; set; }
        public string DatePublication { get; set; }
        public Nullable<long> MembreId { get; set; }
        public Nullable<long> OrganisationId { get; set; }
        public string MOIS { get; set; }
        public string ANNEE { get; set; }
    }
}