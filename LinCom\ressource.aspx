﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="ressource.aspx.cs" Inherits="LinCom.ressource" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <main class="main">

        <!-- BANNIÈRE -->
        <section class="hero d-flex align-items-center text-white" runat="server" id="sectblogdetail" style="background: linear-gradient(rgba(0,131,116,0.8), rgba(0,131,116,0.8)), url('assets/img/biblio.png') center/cover no-repeat; height: 60vh;">
            <div class="container text-center">
                <h1 class="display-5 fw-bold" runat="server" id="txttitle">Titre de la ressource</h1>
                <p class="lead mt-3" runat="server" id="txtcategorie">Catégorie : Document Administratif</p>
            </div>
        </section>

        <!-- CONTENU PRINCIPAL -->
        <section class="py-5">
            <div class="container">
                <div class="row g-5">
                    <!-- Détail Ressource -->
                    <div class="col-lg-8">
                        <div class="p-4 shadow rounded-4">
                            <h3 class="text-008374 fw-bold">Résumé</h3>
                            <p class="fs-5" runat="server" id="txtdescription">Ce document contient les directives nationales pour la gestion des ONG.</p>
                            <hr>
                            <h5 class="text-008374 fw-bold">Téléchargement</h5>
                            <a runat="server" id="linkfichier" target="_blank" class="btn btn-outline-success rounded-pill mt-2">
                                <i class="bi bi-download me-2"></i>Télécharger
                            </a>
                        </div>
                    </div>

                    <!-- Infos -->
                    <div class="col-lg-4">
                        <div class="bg-light p-4 shadow-sm rounded-4">
                            <h5 class="text-008374 fw-bold mb-3">Informations</h5>
                            <p><strong>Date de publication :</strong> <span runat="server" id="txtdate"></span></p>
                            <p><strong>Auteur :</strong> <span runat="server" id="txtauteur"></span></p>
                            <p>
                                <strong>Domaine :</strong>
                                <asp:ListView ID="listdompost" runat="server">
                                    <ItemTemplate><span><%# Eval("PostDom") %></span></ItemTemplate>
                                </asp:ListView>
                            </p>
                            <p><strong>Type :</strong> <span runat="server" id="txttype"></span></p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- BLOC COMMENTAIRES -->
        <section class="bg-light py-5 border-top">
            <div class="container">
                <h4 class="text-008374 fw-bold mb-4"><span runat="server" id="txtcommentpost">0</span></h4>

                <!-- Liste des commentaires -->
                <div class="mb-5">
                    <asp:ListView ID="listCommentaires" runat="server">
                        <EmptyDataTemplate>
                            <p class="text-muted fst-italic">Soyez le premier à réagir !</p>
                        </EmptyDataTemplate>
                        <ItemTemplate>
                            <div class="border-start border-4 border-008374 ps-3 mb-4">
                                <h6 class="fw-semibold mb-1"><%# Eval("Commentateur") %> <small class="text-muted">– publié <%# GetRelativeDate(Convert.ToDateTime(Eval("dateAvis"))) %></small></h6>
                                <p class="text-dark"><%# Eval("comment") %></p>
                            </div>
                        </ItemTemplate>
                    </asp:ListView>
                </div>

                <!-- Formulaire commentaire -->
                <div class="p-4 bg-white rounded-4 shadow-sm">
                    <h5 class="fw-bold text-008374 mb-3">Laisser un commentaire</h5>
                    <div class="row g-3">
                        <div class="col-12">
                            <asp:TextBox ID="txtmessage" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="4" placeholder="Votre commentaire..."></asp:TextBox>
                        </div>
                        <div class="col-12 text-end">
                            <button runat="server" id="btnEnvoyer" class="mt-auto btn btn-008374 btn-sm rounded-pill px-3" onserverclick="btnenreg_ServerClick">Envoyer</button>

                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- PARTAGE -->
       
<section class="pb-5 text-center">
  <div class="container">
       <h6><span>Publié le </span><span runat="server" id="txtdatepublication"></span></h6>

    <h5 class="text-008374 fw-bold mb-3">Partager ce programme sur </h5>
   
    <div class="d-flex justify-content-center flex-wrap gap-3 mt-4">
        <asp:HiddenField ID="hfUrlPage" runat="server" />

      <!-- Facebook -->
      <a href='https://www.facebook.com/sharer/sharer.php?u=<%= hfUrlPage.Value %>'
         class="btn btn-lg rounded-circle shadow-sm text-white bg-primary"
         target="_blank" title="Partager sur Facebook">
        <i class="bi bi-facebook fs-4"></i>
      </a>

      <!-- Twitter/X -->
      <a href='https://twitter.com/intent/tweet?url=<%= hfUrlPage.Value %>&text=Découvrez ce programme !'
         class="btn btn-lg rounded-circle shadow-sm text-white"
         style="background-color: #000000;" target="_blank"
         title="Partager sur X (Twitter)">
        <i class="bi bi-twitter-x fs-4"></i>
      </a>

      <!-- WhatsApp -->
      <a href='https://wa.me/?text=<%= hfUrlPage.Value %>'
         class="btn btn-lg rounded-circle shadow-sm text-white"
         style="background-color: #25D366;" target="_blank"
         title="Partager sur WhatsApp">
        <i class="bi bi-whatsapp fs-4"></i>
      </a>

      <!-- LinkedIn -->
      <a href='https://www.linkedin.com/sharing/share-offsite/?url=<%= hfUrlPage.Value %>'
         class="btn btn-lg rounded-circle shadow-sm text-white"
         style="background-color: #0077b5;" target="_blank"
         title="Partager sur LinkedIn">
        <i class="bi bi-linkedin fs-4"></i>
      </a>

      <!-- Copier le lien -->
      <button type="button" class="btn btn-lg rounded-circle shadow-sm text-white bg-secondary"
              title="Copier le lien" onclick="copyToClipboard()">
        <i class="bi bi-link-45deg fs-4"></i>
      </button>

    </div>
  </div>
</section>


        <!-- AUTRES RESSOURCES SIMILAIRES -->
        <section class="bg-light py-5 border-top">
            <div class="container">
                <h4 class="text-008374 fw-bold mb-4">Ressources similaires</h4>
                <div class="row g-4">
                    <asp:ListView ID="listRessourcesSimilaires" runat="server" OnItemCommand="listRessourcesSimilaires_ItemCommand">
                        <ItemTemplate>
                            <div class="col-md-4">
                                <div class="card h-100 shadow-sm border-0 rounded-4 overflow-hidden">
                                    <!-- Image miniature -->
                                    <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/ressourc/", Eval("photocouverture"))) %>' class="card-img-top" alt="Ressource" style="height: 180px; object-fit: cover;" />

                                    <!-- Détail -->
                                    <div class="card-body d-flex flex-column">
                                        <h6 class="fw-bold text-008374 mb-1">

                                            <asp:LinkButton runat="server" CommandName="viewress" CommandArgument='<%# Eval("name") %>'> <%# Eval("Titre") %>

                                            </asp:LinkButton>
                                        </h6>
                                        <asp:LinkButton runat="server" class="mt-auto btn btn-008374 btn-sm rounded-pill px-3" CommandName="viewressour" CommandArgument='<%# Eval("name") %>'> Voir Plus

                                        </asp:LinkButton>
                                       
                                    </div>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:ListView>
                </div>
            </div>
        </section>

    </main>

    <style>
        .text-008374 {
            color: #008374;
        }

        .bg-008374 {
            background-color: #008374;
        }

        .btn-008374 {
            background-color: #008374;
            color: white;
            border: none;
        }

            .btn-008374:hover {
                background-color: #006e64;
            }

        .rounded-circle {
            width: 40px;
            height: 40px;
            padding: 0;
            line-height: 40px;
            text-align: center;
            border-radius: 50% !important;
        }
    </style>
      <script>
function copyToClipboard() {
  const url = document.getElementById('<%= hfUrlPage.ClientID %>').value;
  navigator.clipboard.writeText(url).then(() => {
    alert("Lien copié dans le presse-papiers !");
  });
}
      </script>
</asp:Content>

