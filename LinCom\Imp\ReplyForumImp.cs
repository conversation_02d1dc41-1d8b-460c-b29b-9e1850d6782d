﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Diagnostics.Eventing.Reader;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
   
    public class ReplyForumImp : IReplyForum
    {
        int msg;
        RepliesForum p = new RepliesForum();
        public int Ajout(ReplyForum_Class add)
        {
            using (Connection con = new Connection())
            {

                p.SujetForumId = add.SujetForumId;
                p.MembreId = add.MembreId;
                p.Contenu = add.Contenu;
                p.DateReply = add.DateReply;
                p.name = add.name;
                p.EstSolution = add.EstSolution;
                p.NombreLikes = add.NombreLikes;
                p.NombreDislikes = add.NombreDislikes;
                p.Nombre<PERSON>ues = add.NombreVues;
                p.statut = add.statut;
                p.etat = add.etat;


               
                try
                {
                    con.RepliesForums.Add(p);

                    if (con.SaveChanges() == 1)
                    {
                        con.RepliesForums.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }

        public void afficherDetails(long id, long idfor,string name, string statut,int cd, ReplyForum_Class add)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    p = con.RepliesForums.Where(x => x.RepliesForumId ==id && x.statut=="publié").FirstOrDefault();

                    if (p != null)
                    {
                        add.RepliesForumId = p.RepliesForumId;
                        add.SujetForumId = p.SujetForumId;
                        add.MembreId = p.MembreId;
                        add.Contenu = p.Contenu;
                        add.DateReply = p.DateReply;
                        add.name = p.name;
                        add.EstSolution = p.EstSolution;
                        add.NombreLikes = p.NombreLikes;
                        add.NombreDislikes = p.NombreDislikes;
                        add.NombreVues = p.NombreVues;
                        add.statut = p.statut;
                        add.etat = p.etat;

                    }

                }
               

            }
        }

        public void Chargement_GDV(GridView GV_apv)
        {
            using (Connection con = new Connection())
            {

                var obj = (from p in con.RepliesForums
                           join a in con.SujetForums on p.SujetForumId equals a.SujetForumId
                           join e in con.Membres on p.MembreId equals e.MembreId
                           select new
                           {
                               id = p.RepliesForumId,
                               SujetForumId = p.SujetForumId,
                               MembreId = p.MembreId,
                               Contenu = p.Contenu,
                               DateReply = p.DateReply,
                               name = p.name,
                               EstSolution = p.EstSolution,
                               NombreLikes = p.NombreLikes,
                               NombreDislikes = p.NombreDislikes,
                               NombreVues = p.NombreVues,
                               statut = p.statut,
                               etat = p.etat,
            }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }
        public void ChargementListview(ListView GV_apv,long id, long idmem, string name, string statut, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var obj = (from p in con.RepliesForums
                               join a in con.SujetForums on p.SujetForumId equals a.SujetForumId
                               join m in con.Membres on p.MembreId equals m.MembreId
                               where p.SujetForumId==id && p.statut==statut
                               select new
                               {
                                   id = p.RepliesForumId,
                                   SujetForumId = p.SujetForumId,
                                   MembreId = p.MembreId,
                                   Contenu = p.Contenu,
                                   DateReply = p.DateReply,
                                   name = p.name,
                                   EstSolution = p.EstSolution,
                                   NombreLikes = p.NombreLikes,
                                   NombreDislikes = p.NombreDislikes,
                                   NombreVues = p.NombreVues,
                                   statut = p.statut,
                                   etat = p.etat,
                                   Nommembre = m.Nom + " " + m.Prenom,
                                   Photomembre = m.PhotoProfil,
                                 
                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();
                }
               

            }
        }

        public void chargerReplyFroum(DropDownList lst)
        {
            throw new NotImplementedException();
        }

        public int count(long id, long idmem,int cd, string statut)
        {
            int n = 0;
            using (Connection con = new Connection())
            {

                if (cd==0) {
                    var b = (from ep in con.RepliesForums
                             join a in con.SujetForums on ep.SujetForumId equals a.SujetForumId
                             join e in con.Membres on ep.MembreId equals e.MembreId
                             where ep.SujetForumId == id && ep.statut == statut
                             select ep).Count();
                    n = b;
                }
               
            }
            return n;
        }

        public int edit(ReplyForum_Class cl, long id)
        {
            using (Connection con = new Connection())
            {
                p = con.RepliesForums.Where(x => x.RepliesForumId == id).FirstOrDefault();

                try
                {
                    p.SujetForumId = cl.SujetForumId;
                    p.MembreId = cl.MembreId;
                    p.Contenu = cl.Contenu;
                    p.DateReply = cl.DateReply;
                    p.name = cl.name;


                    if (con.SaveChanges() == 1)
                    {
                        con.RepliesForums.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }
                return msg;
            }
        }

        public void search(GridView GV_apv, string code)
        {
            using (Connection con = new Connection())
            {



                var obj = (from ep in con.RepliesForums
                           join a in con.SujetForums on ep.SujetForumId equals a.SujetForumId
                           join e in con.Membres on ep.MembreId equals e.MembreId
                           where (ep.MembreId.ToString().Contains(code) && ep.SujetForumId.ToString().Contains(code))
                           select new
                           {

                               id = p.RepliesForumId,
                               SujetForumId = p.SujetForumId,
                               MembreId = p.MembreId,
                               Contenu = p.Contenu,
                               DateReply = p.DateReply,
                               name = p.name,
                               EstSolution = p.EstSolution,
                               NombreLikes = p.NombreLikes,
                               NombreDislikes = p.NombreDislikes,
                               NombreVues = p.NombreVues,
                               statut = p.statut,
                               etat = p.etat,

                           }).ToList();


                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public int supprimer(int id)
        {
            using (Connection con = new Connection())
            {

                p = con.RepliesForums.Where(x => x.RepliesForumId == id).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.RepliesForums.Attach(p);

                con.RepliesForums.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }

     
    }
}