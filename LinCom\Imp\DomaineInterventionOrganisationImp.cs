﻿using LinCom.Classe;
using LinCom.file;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class DomaineInterventionOrganisationImp : IDomaineInterventionOrganisation
    {
        private DomaineInterventionOrganisation domaineInterventionOrganisation = new DomaineInterventionOrganisation();
        int msg;

        public void AfficherDetails(int dom, DomaineInterventionOrganisation_Class domaineInterventionOrganisationClass, long org,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var dio = con.DomaineInterventionOrganisations.FirstOrDefault(x => x.DomaineInterventionOrganisationId == dom && x.OrganisationId == org);
                    if (dio != null)
                    {
                        domaineInterventionOrganisationClass.DomaineInterventionOrganisationId = dio.DomaineInterventionOrganisationId;
                        domaineInterventionOrganisationClass.DomaineInterventionId = dio.DomaineInterventionId;
                        domaineInterventionOrganisationClass.OrganisationId = dio.OrganisationId;
                        domaineInterventionOrganisationClass.name = dio.name;
                        domaineInterventionOrganisationClass.Description = dio.Description;
                        domaineInterventionOrganisationClass.statut = dio.statut;

                    }
                }
                else if (cd==1)
                {
                    var dio = con.DomaineInterventionOrganisations.FirstOrDefault(x => x.DomaineInterventionId == dom && x.OrganisationId == org);
                    if (dio != null)
                    {
                        domaineInterventionOrganisationClass.DomaineInterventionOrganisationId = dio.DomaineInterventionOrganisationId;
                        domaineInterventionOrganisationClass.DomaineInterventionId = dio.DomaineInterventionId;
                        domaineInterventionOrganisationClass.OrganisationId = dio.OrganisationId;
                        domaineInterventionOrganisationClass.name = dio.name;
                        domaineInterventionOrganisationClass.Description = dio.Description;
                        domaineInterventionOrganisationClass.statut = dio.statut;

                    }

                }
                  
            }
        }
        public void AfficherDetails(string code, DomaineInterventionOrganisation_Class domaineInterventionOrganisationClass)
        {
            using (Connection con = new Connection())
            {
                var dio = con.DomaineInterventionOrganisations.FirstOrDefault(x => x.name == code);
                if (dio != null)
                {
                    domaineInterventionOrganisationClass.DomaineInterventionOrganisationId = dio.DomaineInterventionOrganisationId;
                    domaineInterventionOrganisationClass.DomaineInterventionId = dio.DomaineInterventionId;
                    domaineInterventionOrganisationClass.OrganisationId = dio.OrganisationId;
                    domaineInterventionOrganisationClass.name = dio.name;
                    domaineInterventionOrganisationClass.Description = dio.Description;
                    domaineInterventionOrganisationClass.statut = dio.statut;
                }
            }
        }
        public void chargerDomaineInterventionOrganisation(DropDownList ddw, long idorg)
        {
            ddw.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = from dio in con.DomaineInterventionOrganisations
                            join di in con.DomaineInterventions on dio.DomaineInterventionId equals di.DomaineInterventionId
                            join org in con.Organisations on dio.OrganisationId equals org.OrganisationId

                            where dio.OrganisationId == idorg
                            select new
                            {
                                DomaineInterventionOrganisationId = dio.DomaineInterventionOrganisationId,
                                libelle = di.Libelle,
                                name = dio.name,
                                descrpipt = dio.Description,
                                nameorg = org.Nom,
                            };
                //var objj = (from p in con.DomaineInterventionOrganisations where p.OrganisationId==idorg select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    ddw.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner le domaine";
                    ddw.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.DomaineInterventionOrganisationId.ToString();
                        item.Text = data.libelle;
                        ddw.Items.Add(item);
                    }

                }
                else
                {
                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    ddw.Items.Add(item0);
                }

            }
        }

        public int Ajouter(DomaineInterventionOrganisation_Class domaineInterventionOrganisationClass)
        {
            using (Connection con = new Connection())
            {
                domaineInterventionOrganisation.DomaineInterventionId = domaineInterventionOrganisationClass.DomaineInterventionId;
                domaineInterventionOrganisation.OrganisationId = domaineInterventionOrganisationClass.OrganisationId;
                domaineInterventionOrganisation.name = domaineInterventionOrganisationClass.name;
                domaineInterventionOrganisation.Description = domaineInterventionOrganisationClass.Description;
                domaineInterventionOrganisation.statut = domaineInterventionOrganisationClass.statut;
                try
                {
                    con.DomaineInterventionOrganisations.Add(domaineInterventionOrganisation);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerDomainesInterventionOrganisation(GridView gdv, long organisationId)
        {
            using (Connection con = new Connection())
            {
                var query = from dio in con.DomaineInterventionOrganisations
                            join di in con.DomaineInterventions on dio.DomaineInterventionId equals di.DomaineInterventionId
                            join org in con.Organisations on dio.OrganisationId equals org.OrganisationId
                            
                            where dio.OrganisationId == organisationId
                            select new
                            {
                                id=dio.DomaineInterventionOrganisationId,
                                libelle=di.Libelle,
                                name =dio.name,
                                descrpipt=dio.Description,
                                nameorg=org.Nom,
                            };

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }
        public void ChargerListviewDomainesInterventionOrganisation(ListView gdv, long organisationId)
        {
            using (Connection con = new Connection())
            {
                var query = from dio in con.DomaineInterventionOrganisations
                            join di in con.DomaineInterventions on dio.DomaineInterventionId equals di.DomaineInterventionId
                            join org in con.Organisations on dio.OrganisationId equals org.OrganisationId

                            where dio.OrganisationId == organisationId
                            select new
                            {
                                id = dio.DomaineInterventionOrganisationId,
                                libelle = di.Libelle,
                                name = dio.name,
                                descrpipt = dio.Description,
                                nameorg = org.Nom,
                            };

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }
        public void ChargerRepeaterDomainesInterventionOrganisation(Repeater gdv, long organisationId)
        {
            using (Connection con = new Connection())
            {
                var query = from dio in con.DomaineInterventionOrganisations
                            join di in con.DomaineInterventions on dio.DomaineInterventionId equals di.DomaineInterventionId
                            join org in con.Organisations on dio.OrganisationId equals org.OrganisationId

                            where dio.OrganisationId == organisationId
                            select new
                            {
                                id = dio.DomaineInterventionOrganisationId,
                                libelle = di.Libelle,
                                name = dio.name,
                                descrpipt = dio.Description,
                                nameorg = org.Nom,
                            };

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }

        public int Modifier(int id,DomaineInterventionOrganisation_Class domaineInterventionOrganisationClass)
        {
            using (Connection con = new Connection())
            {
                var dio = con.DomaineInterventionOrganisations.FirstOrDefault(x => x.DomaineInterventionOrganisationId == id);
                if (dio != null)
                {
                    dio.DomaineInterventionId = domaineInterventionOrganisationClass.DomaineInterventionId;
                    dio.OrganisationId = domaineInterventionOrganisationClass.OrganisationId;
                    dio.name = domaineInterventionOrganisationClass.name;
                    dio.Description = domaineInterventionOrganisationClass.Description;
                    dio.statut = domaineInterventionOrganisationClass.statut;
                    try
                    {

                        if (con.SaveChanges() == 1)
                        {
                            con.DomaineInterventionOrganisations.Add(dio);
                            con.Entry(dio).State = EntityState.Modified;
                            return msg = 1;
                        } else return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg;
            }
        }

        public int Supprimer(int domaineInterventionOrganisationId)
        {
            using (Connection con = new Connection())
            {
                var dio = con.DomaineInterventionOrganisations.FirstOrDefault(x => x.DomaineInterventionOrganisationId == domaineInterventionOrganisationId);
                if (dio != null)
                {
                    con.DomaineInterventionOrganisations.Remove(dio);
                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }
    }
}