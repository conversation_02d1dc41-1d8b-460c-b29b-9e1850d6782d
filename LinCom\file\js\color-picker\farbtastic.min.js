jQuery.fn.farbtastic=function(e){$.farbtastic(this,e);return this};jQuery.farbtastic=function(e,t){var e=$(e).get(0);return e.farbtastic||(e.farbtastic=new jQuery._farbtastic(e,t))};jQuery._farbtastic=function(e,t){var n=this;$(e).html('<div class="farbtastic"><div class="color"></div><div class="wheel"></div><div class="overlay"></div><div class="h-marker marker"></div><div class="sl-marker marker"></div></div>');var r=$(".farbtastic",e);n.wheel=$(".wheel",e).get(0);n.radius=84;n.square=100;n.width=194;if(navigator.appVersion.match(/MSIE [0-6]\./)){$("*",r).each(function(){if(this.currentStyle.backgroundImage!="none"){var e=this.currentStyle.backgroundImage;e=this.currentStyle.backgroundImage.substring(5,e.length-2);$(this).css({backgroundImage:"none",filter:"progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled=true, sizingMethod=crop, src='"+e+"')"})}})}n.linkTo=function(e){if(typeof n.callback=="object"){$(n.callback).unbind("keyup",n.updateValue)}n.color=null;if(typeof e=="function"){n.callback=e}else if(typeof e=="object"||typeof e=="string"){n.callback=$(e);n.callback.bind("keyup",n.updateValue);if(n.callback.get(0).value){n.setColor(n.callback.get(0).value)}}return this};n.updateValue=function(e){if(this.value&&this.value!=n.color){n.setColor(this.value)}};n.setColor=function(e){var t=n.unpack(e);if(n.color!=e&&t){n.color=e;n.rgb=t;n.hsl=n.RGBToHSL(n.rgb);n.updateDisplay()}return this};n.setHSL=function(e){n.hsl=e;n.rgb=n.HSLToRGB(e);n.color=n.pack(n.rgb);n.updateDisplay();return this};n.widgetCoords=function(e){var t,r;var i=e.target||e.srcElement;var s=n.wheel;if(typeof e.offsetX!="undefined"){var o={x:e.offsetX,y:e.offsetY};var u=i;while(u){u.mouseX=o.x;u.mouseY=o.y;o.x+=u.offsetLeft;o.y+=u.offsetTop;u=u.offsetParent}var u=s;var a={x:0,y:0};while(u){if(typeof u.mouseX!="undefined"){t=u.mouseX-a.x;r=u.mouseY-a.y;break}a.x+=u.offsetLeft;a.y+=u.offsetTop;u=u.offsetParent}u=i;while(u){u.mouseX=undefined;u.mouseY=undefined;u=u.offsetParent}}else{var o=n.absolutePosition(s);t=(e.pageX||0*(e.clientX+$("html").get(0).scrollLeft))-o.x;r=(e.pageY||0*(e.clientY+$("html").get(0).scrollTop))-o.y}return{x:t-n.width/2,y:r-n.width/2}};n.mousedown=function(e){if(!document.dragging){$(document).bind("mousemove",n.mousemove).bind("mouseup",n.mouseup);document.dragging=true}var t=n.widgetCoords(e);n.circleDrag=Math.max(Math.abs(t.x),Math.abs(t.y))*2>n.square;n.mousemove(e);return false};n.mousemove=function(e){var t=n.widgetCoords(e);if(n.circleDrag){var r=Math.atan2(t.x,-t.y)/6.28;if(r<0)r+=1;n.setHSL([r,n.hsl[1],n.hsl[2]])}else{var i=Math.max(0,Math.min(1,-(t.x/n.square)+.5));var s=Math.max(0,Math.min(1,-(t.y/n.square)+.5));n.setHSL([n.hsl[0],i,s])}return false};n.mouseup=function(){$(document).unbind("mousemove",n.mousemove);$(document).unbind("mouseup",n.mouseup);document.dragging=false};n.updateDisplay=function(){var e=n.hsl[0]*6.28;$(".h-marker",r).css({left:Math.round(Math.sin(e)*n.radius+n.width/2)+"px",top:Math.round(-Math.cos(e)*n.radius+n.width/2)+"px"});$(".sl-marker",r).css({left:Math.round(n.square*(.5-n.hsl[1])+n.width/2)+"px",top:Math.round(n.square*(.5-n.hsl[2])+n.width/2)+"px"});$(".color",r).css("backgroundColor",n.pack(n.HSLToRGB([n.hsl[0],1,.5])));if(typeof n.callback=="object"){$(n.callback).css({backgroundColor:n.color,color:n.hsl[2]>.5?"#000":"#fff"});$(n.callback).each(function(){if(this.value&&this.value!=n.color){this.value=n.color}})}else if(typeof n.callback=="function"){n.callback.call(n,n.color)}};n.absolutePosition=function(e){var t={x:e.offsetLeft,y:e.offsetTop};if(e.offsetParent){var r=n.absolutePosition(e.offsetParent);t.x+=r.x;t.y+=r.y}return t};n.pack=function(e){var t=Math.round(e[0]*255);var n=Math.round(e[1]*255);var r=Math.round(e[2]*255);return"#"+(t<16?"0":"")+t.toString(16)+(n<16?"0":"")+n.toString(16)+(r<16?"0":"")+r.toString(16)};n.unpack=function(e){if(e.length==7){return[parseInt("0x"+e.substring(1,3))/255,parseInt("0x"+e.substring(3,5))/255,parseInt("0x"+e.substring(5,7))/255]}else if(e.length==4){return[parseInt("0x"+e.substring(1,2))/15,parseInt("0x"+e.substring(2,3))/15,parseInt("0x"+e.substring(3,4))/15]}};n.HSLToRGB=function(e){var t,n,r,i,s;var o=e[0],u=e[1],a=e[2];n=a<=.5?a*(u+1):a+u-a*u;t=a*2-n;return[this.hueToRGB(t,n,o+.33333),this.hueToRGB(t,n,o),this.hueToRGB(t,n,o-.33333)]};n.hueToRGB=function(e,t,n){n=n<0?n+1:n>1?n-1:n;if(n*6<1)return e+(t-e)*n*6;if(n*2<1)return t;if(n*3<2)return e+(t-e)*(.66666-n)*6;return e};n.RGBToHSL=function(e){var t,n,r,i,s,o;var u=e[0],a=e[1],f=e[2];t=Math.min(u,Math.min(a,f));n=Math.max(u,Math.max(a,f));r=n-t;o=(t+n)/2;s=0;if(o>0&&o<1){s=r/(o<.5?2*o:2-2*o)}i=0;if(r>0){if(n==u&&n!=a)i+=(a-f)/r;if(n==a&&n!=f)i+=2+(f-u)/r;if(n==f&&n!=u)i+=4+(u-a)/r;i/=6}return[i,s,o]};$("*",r).mousedown(n.mousedown);n.setColor("#000000");if(t){n.linkTo(t)}}