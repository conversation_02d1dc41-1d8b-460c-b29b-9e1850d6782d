﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class StatutUtilisateurImp : IStatutUtilisateur
    {
        private StatutUtilisateur statut = new StatutUtilisateur();
        int msg;

        public void AfficherDetails(int statutId, StatutUtilisateur_Class statutUtilisateurClass)
        {
            using (Connection con = new Connection())
            {
                var s = con.StatutUtilisateurs.FirstOrDefault(x => x.StatutId == statutId);
                if (s != null)
                {
                    statutUtilisateurClass.StatutId = s.StatutId;
                    statutUtilisateurClass.MembreId = s.MembreId;
                    statutUtilisateurClass.Libelle = s.<PERSON>belle;
                }
            }
        }

        public int Ajouter(StatutUtilisateur_Class statutUtilisateurClass)
        {
            using (Connection con = new Connection())
            {
                statut.MembreId = statutUtilisateurClass.MembreId;
                statut.Libelle = statutUtilisateurClass.Libelle;

                try
                {
                    con.StatutUtilisateurs.Add(statut);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerStatuts(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var query = from s in con.StatutUtilisateurs
                            join m in con.Membres on s.MembreId equals m.MembreId into membres
                            from membre in membres.DefaultIfEmpty()
                            select new
                            {
                                s.StatutId,
                                Membre = membre != null ? membre.Nom + " " + membre.Prenom : "N/A",
                                s.Libelle
                            };

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }

        public void chargerStatutUtilisateur(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.StatutUtilisateurs select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner le statut de l'utilisateur";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.StatutId.ToString();
                        item.Text = data.MembreId.ToString() + " - " + data.Libelle;
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }


        public void ChargerStatutsParMembre(GridView gdv, long membreId)
        {
            using (Connection con = new Connection())
            {
                var query = from s in con.StatutUtilisateurs
                            where s.MembreId == membreId
                            select new
                            {
                                s.StatutId,
                                s.Libelle
                            };

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }

        public int Modifier(StatutUtilisateur_Class statutUtilisateurClass)
        {
            using (Connection con = new Connection())
            {
                var s = con.StatutUtilisateurs.FirstOrDefault(x => x.StatutId == statutUtilisateurClass.StatutId);
                if (s != null)
                {
                    s.MembreId = statutUtilisateurClass.MembreId;
                    s.Libelle = statutUtilisateurClass.Libelle;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(int statutId)
        {
            using (Connection con = new Connection())
            {
                var s = con.StatutUtilisateurs.FirstOrDefault(x => x.StatutId == statutId);
                if (s != null)
                {
                    con.StatutUtilisateurs.Remove(s);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}