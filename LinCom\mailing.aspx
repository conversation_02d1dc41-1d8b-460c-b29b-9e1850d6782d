﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="mailing.aspx.cs" Inherits="LinCom.mailing" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
     <link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,700,900" rel="stylesheet">
<!-- Bootstrap CSS
	============================================ -->
<link rel="stylesheet" href="file/css/bootstrap.min.css">
<!-- font awesome CSS
	============================================ -->
<link rel="stylesheet" href="file/css/font-awesome.min.css">
<!-- owl.carousel CSS
	============================================ -->
<link rel="stylesheet" href="file/css/owl.carousel.css">
<link rel="stylesheet" href="file/css/owl.theme.css">
<link rel="stylesheet" href="file/css/owl.transitions.css">
<!-- meanmenu CSS
	============================================ -->
<link rel="stylesheet" href="file/css/meanmenu/meanmenu.min.css">
<!-- animate CSS
	============================================ -->
<link rel="stylesheet" href="file/css/animate.css">
<!-- normalize CSS
	============================================ -->
<link rel="stylesheet" href="file/css/normalize.css">
<!-- mCustomScrollbar CSS
	============================================ -->
<link rel="stylesheet" href="file/css/scrollbar/jquery.mCustomScrollbar.min.css">
<!-- Notika icon CSS
	============================================ -->
<link rel="stylesheet" href="file/css/notika-custom-icon.css">
<!-- dropzone CSS
	============================================ -->
<link rel="stylesheet" href="file/css/dropzone/dropzone.css">
<!-- summernote CSS
	============================================ -->
<link rel="stylesheet" href="file/css/summernote/summernote.css">
<!-- wave CSS
	============================================ -->
<link rel="stylesheet" href="file/css/wave/waves.min.css">
<link rel="stylesheet" href="file/css/wave/button.css">
<!-- main CSS
	============================================ -->
<link rel="stylesheet" href="file/css/main.css">
<!-- style CSS
	============================================ -->
<link rel="stylesheet" href="file/style.css">
<!-- responsive CSS
	============================================ -->
<link rel="stylesheet" href="file/css/responsive.css">
<!-- modernizr JS
	============================================ -->
<script src="js/vendor/modernizr-2.8.3.min.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
     <!-- Page Title -->
 <div class="page-title">
     <div class="heading">
         <div class="container">
             <div class="row d-flex justify-content-center text-center">
                 <div class="col-lg-8">
                     <h2>Connexion</h2>
                     <%--<p class="mb-0">Odio et unde deleniti. Deserunt numquam exercitationem. Officiis quo odio sint voluptas consequatur ut a odio voluptatem. Sit dolorum debitis veritatis natus dolores. Quasi ratione sint. Sit quaerat ipsum dolorem.</p>
               --%>
                     </div>
             </div>
         </div>
     </div>
     <nav class="breadcrumbs">
         <div class="container">
             <ol>
                 <li><a href="home.aspx">Home</a></li>
                 <li><a href="#">Connection</a></li>
                 <li class="current"><a href="login.aspx">Connection</a></li>
             </ol>
         </div>
     </nav>
 </div>
 <!-- End Page Title -->
      <main class="main">
    	<!-- Breadcomb area Start-->
	<div class="breadcomb-area">
		<div class="container">
			<div class="row">
				<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
					<div class="breadcomb-list">
						<div class="row">
							<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
								<div class="breadcomb-wp">
									<div class="breadcomb-icon">
										<i class="notika-icon notika-mail"></i>
									</div>
									<div class="breadcomb-ctn">
										<h2>Compose Email</h2>
										<p>Welcome to Notika <span class="bread-ntd">Admin Template</span></p>
									</div>
								</div>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
								<div class="breadcomb-report">
									<button data-toggle="tooltip" data-placement="left" title="Download Report" class="btn"><i class="notika-icon notika-sent"></i></button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- Breadcomb area End-->
    <!-- Compose email area Start-->
    <div class="inbox-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-12">
                    <div class="inbox-left-sd">
                        <div class="compose-ml">
                            <a class="btn" href="#">Compose</a>
                        </div>
                        <div class="inbox-status">
                            <ul class="inbox-st-nav inbox-ft">
                                <li><a href="#"><i class="notika-icon notika-mail"></i> Inbox<span class="pull-right">12</span></a></li>
                                <li><a href="#"><i class="notika-icon notika-sent"></i> Sent</a></li>
                                <li><a href="#"><i class="notika-icon notika-draft"></i> Draft</a></li>
                                <li><a href="#"><i class="notika-icon notika-trash"></i> Trash</a></li>
                            </ul>
                        </div>
                        <hr>
                        <div class="inbox-status">
                            <ul class="inbox-st-nav">
                                <li><a href="#"><i class="notika-icon notika-travel"></i> Travel</a></li>
                                <li><a href="#"><i class="notika-icon notika-finance"></i> Finance</a></li>
                                <li><a href="#"><i class="notika-icon notika-social"></i> Social</a></li>
                                <li><a href="#"><i class="notika-icon notika-promos"></i> Promos</a></li>
                                <li><a href="#"><i class="notika-icon notika-flag"></i> Updates</a></li>
                            </ul>
                        </div>
                        <hr>
                        <div class="inbox-status">
                            <ul class="inbox-st-nav inbox-nav-mg">
                                <li><a href="#"><i class="notika-icon notika-success"></i> Forum</a></li>
                                <li><a href="#"><i class="notika-icon notika-chat"></i> Chat</a></li>
                                <li><a href="#"><i class="notika-icon notika-star"></i> Work</a></li>
                                <li><a href="#"><i class="notika-icon notika-settings"></i> Settings</a></li>
                                <li><a href="#"><i class="notika-icon notika-support"></i> Support</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-9 col-md-9 col-sm-8 col-xs-12">
                    <div class="view-mail-list sm-res-mg-t-30">
                        <div class="view-mail-hd">
                            <div class="view-mail-hrd">
                                <h2>New Message</h2>
                            </div>
                        </div>
                        <div class="cmp-int mg-t-20">
                            <div class="row">
                                <div class="col-lg-1 col-md-2 col-sm-2 col-xs-12">
                                    <div class="cmp-int-lb cmp-int-lb1 text-right">
                                        <span>To: </span>
                                    </div>
                                </div>
                                <div class="col-lg-11 col-md-10 col-sm-10 col-xs-12">
                                    <div class="form-group">
                                        <div class="nk-int-st cmp-int-in cmp-email-over">
                                            <input type="email" class="form-control" placeholder="<EMAIL>" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-1 col-md-2 col-sm-2 col-xs-12">
                                    <div class="cmp-int-lb cmp-int-lb1 text-right">
                                        <span>Cc: </span>
                                    </div>
                                </div>
                                <div class="col-lg-11 col-md-10 col-sm-10 col-xs-12">
                                    <div class="form-group">
                                        <div class="nk-int-st cmp-int-in cmp-email-over">
                                            <input type="email" class="form-control" placeholder="" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-1 col-md-2 col-sm-2 col-xs-12">
                                    <div class="cmp-int-lb text-right">
                                        <span>Subject: </span>
                                    </div>
                                </div>
                                <div class="col-lg-11 col-md-10 col-sm-10 col-xs-12">
                                    <div class="form-group cmp-em-mg">
                                        <div class="nk-int-st cmp-int-in cmp-email-over">
                                            <input type="text" class="form-control" placeholder="" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="cmp-int-box mg-t-20">
                            <div class="html-editor-cm">
                                <h2>Hello Mamunur Roshid!</h2>
                                <p>Dummy text of the printing and typesetting industry. Lorem Ipsum has been the <b>dustrys standard dummy text</b> ever since the 1500s, when an unknown printer took a galley of types and scrambleded it to make a type specimenen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages <a href="#">Read more</a>.</p>
                                <p>All the Lorem Ipsum generators on the Internet tend to repeat the predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence crisity structures, to generate Lorem Ipsum which looks reasonable. recently with.Dummy text of the printing and typesetting industryunknown printer took a galley of type.</p>
                                <span class="vw-tr">Thanks and Regards</span>
                                <span>Mark Smith</span>
                            </div>
                        </div>
                        <div class="multiupload-sys">
                            <div id="dropzone" class="dropmail">
                                <form action="/upload" class="dropzone dropzone-custom needsclick" id="demo-upload">
                                    <div class="dz-message needsclick download-custom">
                                        <i class="notika-icon notika-cloud" aria-hidden="true"></i>
                                        <h2>Drop files here or click to upload.</h2>
                                        <p><span class="note needsclick">(This is just a demo dropzone. Selected files are <strong>not</strong> actually uploaded.)</span>
                                        </p>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="vw-ml-action-ls text-right mg-t-20">
                            <div class="btn-group ib-btn-gp active-hook nk-email-inbox">
                                <button class="btn btn-default btn-sm waves-effect"><i class="notika-icon notika-next"></i> Reply</button>
                                <button class="btn btn-default btn-sm waves-effect"><i class="notika-icon notika-right-arrow"></i> Forward</button>
                                <button class="btn btn-default btn-sm waves-effect"><i class="notika-icon notika-down-arrow"></i> Print</button>
                                <button class="btn btn-default btn-sm waves-effect"><i class="notika-icon notika-trash"></i> Remove</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Compose email area End-->
</main>

    <!-- jquery
	============================================ -->
<script src="file/js/vendor/jquery-1.12.4.min.js"></script>
<!-- bootstrap JS
	============================================ -->
<script src="file/js/bootstrap.min.js"></script>
<!-- wow JS
	============================================ -->
<script src="file/js/wow.min.js"></script>
<!-- price-slider JS
	============================================ -->
<script src="file/js/jquery-price-slider.js"></script>
<!-- owl.carousel JS
	============================================ -->
<script src="js/owl.carousel.min.js"></script>
<!-- scrollUp JS
	============================================ -->
<script src="file/js/jquery.scrollUp.min.js"></script>
<!-- meanmenu JS
	============================================ -->
<script src="file/js/meanmenu/jquery.meanmenu.js"></script>
<!-- counterup JS
	============================================ -->
<script src="file/js/counterup/jquery.counterup.min.js"></script>
<script src="file/js/counterup/waypoints.min.js"></script>
<script src="file/js/counterup/counterup-active.js"></script>
<!-- mCustomScrollbar JS
	============================================ -->
<script src="file/js/scrollbar/jquery.mCustomScrollbar.concat.min.js"></script>
<!-- sparkline JS
	============================================ -->
<script src="file/js/sparkline/jquery.sparkline.min.js"></script>
<script src="file/js/sparkline/sparkline-active.js"></script>
<!-- flot JS
	============================================ -->
<script src="file/js/flot/jquery.flot.js"></script>
<script src="file/js/flot/jquery.flot.resize.js"></script>
<script src="file/js/flot/flot-active.js"></script>
<!-- knob JS
	============================================ -->
<script src="file/js/knob/jquery.knob.js"></script>
<script src="file/js/knob/jquery.appear.js"></script>
<script src="file/js/knob/knob-active.js"></script>
<!-- summernote JS
	============================================ -->
<script src="file/js/summernote/summernote-updated.min.js"></script>
<script src="file/js/summernote/summernote-active.js"></script>
<!-- dropzone JS
	============================================ -->
<script src="file/js/dropzone/dropzone.js"></script>
<!--  wave JS
	============================================ -->
<script src="file/js/wave/waves.min.js"></script>
<script src="file/js/wave/wave-active.js"></script>
<!-- icheck JS
	============================================ -->
<script src="file/js/icheck/icheck.min.js"></script>
<script src="file/js/icheck/icheck-active.js"></script>
<!--  Chat JS
	============================================ -->
<script src="file/js/chat/jquery.chat.js"></script>
<!--  todo JS
	============================================ -->
<script src="file/js/todo/jquery.todo.js"></script>
<!-- plugins JS
	============================================ -->
<script src="file/js/plugins.js"></script>
<!-- main JS
	============================================ -->
<script src="file/js/main.js"></script>
<!-- tawk chat JS
	============================================ -->
<script src="file/js/tawk-chat.js"></script>
</asp:Content>
