<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">Espace FNUAP</a></li>
                    <li><a href="#">Bibliothèque Digitale de FNUAP</a></li>
                    <li class="current"><a href="ressources-document.aspx">Documents officiels</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">
        <asp:HiddenField ID="hdnConversationId" runat="server" />
<asp:HiddenField ID="hdnIsGroup" runat="server" />
<asp:HiddenField ID="hdnCurrentUserId" runat="server" />

        <div class="container py-4">
            <h2 class="mb-4">💬 Espace Messagerie</h2>

            <div class="chat-wrapper">

                <!-- Sidebar -->
                <div class="contacts-panel">
                    <div class="contacts-header">
                        <div class="header-content">
                            <div class="header-title">
                                <i class="fas fa-users"></i>
                                <span>Messagerie</span>
                            </div>
                            <div class="header-actions">
                                <button type="button" class="action-btn" title="Rechercher dans les messages">
                                    <i class="fas fa-search"></i>
                                </button>
                                <button type="button" class="action-btn" title="Plus d'options">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                                <button type="button" class="action-btn" title="Nouveau message">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                        <div class="search-container">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <asp:TextBox ID="txtRechercheContact" runat="server" placeholder="Rechercher dans les messages" AutoPostBack="true" OnTextChanged="txtRechercheContact_TextChanged" CssClass="modern-search-input"></asp:TextBox>
                            </div>
                        </div>
                    </div>

                    <!-- Zone de défilement pour les contacts -->
                    <div class="contacts-list-container">
                        <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                        <EmptyDataTemplate>
                            <div class="empty-contacts">
                                <i class="fas fa-users" style="font-size: 48px; color: #ccc; margin-bottom: 16px;"></i>
                                <p style="color: #666; text-align: center;">Aucun contact disponible</p>
                            </div>
                        </EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectMembre" runat="server"
                                CommandName="viewmem"
                                CommandArgument='<%# Eval("id") %>'
                                CssClass="contact-item">
        <div class="contact-avatar-container">
            <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/", Eval("PhotoProfil"))) %>' alt="Nom du Membre" class="contact-avatar" />
            <%# DataBinder.Eval(Container.DataItem, "EstEnLigne") != null && Convert.ToBoolean(DataBinder.Eval(Container.DataItem, "EstEnLigne")) ? "<div class=\"online-indicator\"></div>" : "" %>
        </div>
        <div class="contact-info">
            <div class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Membre")) %></div>
            <div class="contact-last-message">
                <%# DataBinder.Eval(Container.DataItem, "DernierMessage") != null ?
                    HttpUtility.HtmlEncode(DataBinder.Eval(Container.DataItem, "DernierMessage").ToString()) : "Dernier message..." %>
            </div>
        </div>
        <div class="contact-meta">
            <span class="contact-time">
                <%# DataBinder.Eval(Container.DataItem, "DateDernierMessage") != null ?
                    Convert.ToDateTime(DataBinder.Eval(Container.DataItem, "DateDernierMessage")).ToString("HH:mm") : "12:30" %>
            </span>
            <%# DataBinder.Eval(Container.DataItem, "MessagesNonLus") != null && Convert.ToInt32(DataBinder.Eval(Container.DataItem, "MessagesNonLus")) > 0 ?
                "<span class=\"unread-badge\">" + DataBinder.Eval(Container.DataItem, "MessagesNonLus") + "</span>" : "" %>
        </div>
                            </asp:LinkButton>
                                <!-- 🔽 Champ caché pour dire si c’est un groupe -->
 
                        </ItemTemplate>
                    </asp:ListView>
                    </div>

                </div>


                <!-- Main Chat Area -->
                <div class="chat-panel">

                    <!-- Header de chat (visible seulement quand un contact est sélectionné) -->
                    <div class="chat-header" id="chatHeader">
                        <asp:Label ID="lblHeader" runat="server" Text=""></asp:Label>
                        <asp:Label ID="lblId" Visible="false" runat="server" Text="0"></asp:Label>
                    </div>



                    <!-- Zone de messages (visible seulement quand un contact est sélectionné) -->
                    <div class="chat-body" id="chatBody">
                      <asp:Repeater ID="rptMessages" runat="server">
    <ItemTemplate>
        <div class='message-container <%# Eval("Expediteur").ToString() == "VotreNomComplet" ? "sent" : "received" %>'>
            <div class="message-header">
                <img class="avatar" src='<%# ResolveUrl("~/file/membr/") + Eval("Photomembre") %>' alt="Photo" />
                <strong><%# Eval("Expediteur") %></strong>
                <span class="date"><%# Eval("DateEnvoi", "{0:dd MMM yyyy HH:mm}") %></span>
            </div>

            <div class="message-body">
                <p><%# Server.HtmlDecode(LinCom.Classe.EmojiManager.ConvertirEmojis(Eval("Contenu").ToString())) %></p>

                <%-- Si le message contient une pièce jointe --%>
                <asp:Panel runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                    <div class="message-attachment">
                        <span class="attachment-icon"><%# GetFileIcon(Eval("AttachmentUrl").ToString()) %></span>
                        <div class="attachment-details">
                            <div class="attachment-name"><%# Eval("name") %></div>
                            <div class="attachment-size"><%# GetFileSize(Eval("AttachmentUrl").ToString()) %></div>
                        </div>
                        <a href='<%# Eval("AttachmentUrl") %>' class="attachment-download" target="_blank">Télécharger</a>
                    </div>
                </asp:Panel>

                <%-- Affichage du statut de lecture pour les messages envoyés --%>
                <div class="message-footer">
                    <span class="message-status status-read">✓✓</span>
                </div>
            </div>
        </div>
    </ItemTemplate>
</asp:Repeater>

                    </div>

                    <!-- Zone de saisie (visible seulement quand un contact est sélectionné) -->
                    <div class="chat-footer" id="chatFooter">
                        <div class="modern-input-container">
                            <div class="input-wrapper">
                                <div class="input-actions-left">
                                    <button type="button" class="input-action-btn" title="Ajouter une image">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button type="button" id="btnAttachment" class="input-action-btn" title="Joindre un fichier">
                                        <i class="fas fa-paperclip"></i>
                                    </button>
                                    <input type="file" id="fileAttachment" style="display:none;" accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.zip,.rar,.mp3,.mp4,.avi" />
                                </div>

                                <div class="message-input-wrapper">
                                    <textarea rows="1" runat="server" id="txtMessage" placeholder="Rédigez un message..." maxlength="1000" class="modern-message-input"></textarea>
                                    <div class="input-actions-right">
                                        <button type="button" class="input-action-btn" title="GIF">
                                            <span class="gif-text">GIF</span>
                                        </button>
                                        <button type="button" id="btnEmoji" class="input-action-btn" title="Émojis">
                                            <i class="fas fa-smile"></i>
                                        </button>
                                        <button type="button" class="input-action-btn" title="Plus d'options">
                                            <i class="fas fa-ellipsis-h"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="send-button-container">
                                <button type="button" runat="server" id="btnenvoie" onserverclick="btnenvoie_ServerClick" class="modern-send-btn" title="Envoyer">
                                    <span class="send-text">Envoyer</span>
                                </button>
                            </div>
                        </div>

                        <!-- Sélecteur d'émojis -->
                        <div id="emojiPicker" class="emoji-picker" style="display:none;">
                            <div class="emoji-header">
                                <span class="emoji-tab active" data-category="populaires">😊</span>
                                <span class="emoji-tab" data-category="visages">😀</span>
                                <span class="emoji-tab" data-category="gestes">👍</span>
                                <span class="emoji-tab" data-category="objets">❤️</span>
                                <span class="emoji-tab" data-category="nature">🌳</span>
                            </div>
                            <div class="emoji-content" id="emojiContent">
                                <!-- Émojis populaires par défaut -->
                                <div class="emoji-grid">
                                    <span class="emoji-item" data-emoji="😊">😊</span>
                                    <span class="emoji-item" data-emoji="😂">😂</span>
                                    <span class="emoji-item" data-emoji="❤️">❤️</span>
                                    <span class="emoji-item" data-emoji="👍">👍</span>
                                    <span class="emoji-item" data-emoji="😢">😢</span>
                                    <span class="emoji-item" data-emoji="😉">😉</span>
                                    <span class="emoji-item" data-emoji="🔥">🔥</span>
                                    <span class="emoji-item" data-emoji="🎉">🎉</span>
                                    <span class="emoji-item" data-emoji="😍">😍</span>
                                    <span class="emoji-item" data-emoji="👏">👏</span>
                                </div>
                            </div>
                        </div>

                        <!-- Prévisualisation de la pièce jointe -->
                        <div id="attachmentPreview" class="attachment-preview" style="display:none;">
                            <div class="attachment-info">
                                <span id="attachmentName"></span>
                                <span id="attachmentSize"></span>
                                <button type="button" id="btnRemoveAttachment" class="remove-btn">×</button>
                            </div>
                        </div>
                    </div>
                    <div class="message-counter">
                        <small id="charCount">0/1000 caractères</small>
                    </div>

                    <!-- Champ caché pour la pièce jointe -->
                    <asp:HiddenField ID="hdnAttachmentPath" runat="server" />
                </div>
            </div>
        </div>

    </main>

    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
        }

        .chat-wrapper {
            display: flex;
            height: 80vh;
            border-radius: 12px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            overflow: hidden;
            background: #fff;
        }

        .contacts-panel {
            width: 280px;
            background: #f4f4f4;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .contacts-header {
            background: #008374;
            color: #fff;
            padding: 0;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 100;
            flex-shrink: 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 4px;
        }

        .action-btn {
            background: transparent;
            border: none;
            color: #fff;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.1);
        }

        .search-container {
            padding: 12px 16px;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            color: #666;
            font-size: 14px;
            z-index: 1;
        }

        .modern-search-input {
            width: 100%;
            padding: 10px 12px 10px 36px;
            border: none;
            border-radius: 20px;
            background: #f0f2f5;
            font-size: 14px;
            outline: none;
            transition: background-color 0.2s ease;
        }

        .modern-search-input:focus {
            background: #fff;
            box-shadow: 0 0 0 2px rgba(0,131,116,0.2);
        }

        /* Zone de défilement des contacts */
        .contacts-list-container {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            background: #fff;
        }

        .contacts-list-container::-webkit-scrollbar {
            width: 6px;
        }

        .contacts-list-container::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .contacts-list-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .contacts-list-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* État vide des contacts */
        .empty-contacts {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            text-align: center;
        }

        .contact-item {
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border-bottom: 1px solid #f0f2f5;
            text-decoration: none;
            color: inherit;
            position: relative;
        }

        .contact-item:hover {
            background: #f0f2f5;
            text-decoration: none;
            color: inherit;
        }

        .contact-item.active {
            background: #e3f2fd;
            border-left: 3px solid #008374;
        }

        .contact-avatar-container {
            position: relative;
            flex-shrink: 0;
        }

        .contact-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .online-indicator {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background-color: #4CAF50;
            border: 2px solid white;
            border-radius: 50%;
            box-shadow: 0 0 0 1px rgba(0,0,0,0.1);
        }

        .contact-info {
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .contact-name {
            font-weight: 600;
            font-size: 14px;
            color: #1d2129;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .contact-last-message {
            font-size: 13px;
            color: #65676b;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .contact-meta {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 4px;
            flex-shrink: 0;
        }

        .contact-time {
            font-size: 12px;
            color: #65676b;
            font-weight: 400;
        }

        .unread-badge {
            background: #008374;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 11px;
            font-weight: 600;
            min-width: 18px;
            text-align: center;
            line-height: 1.2;
        }

        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fafafa;
            position: relative;
            min-height: 100%;
            overflow: hidden;
        }





        .chat-header {
            background: #fff;
            padding: 15px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
        }

        .chat-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .bubble {
            max-width: 60%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            position: relative;
        }

            .bubble.received {
                background: #eee;
                align-self: flex-start;
            }

            .bubble.sent {
                background: #008374;
                color: white;
                align-self: flex-end;
            }

        .chat-footer {
            padding: 16px;
            background: #fff;
            border-top: 1px solid #e4e6ea;
        }

        .modern-input-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .input-wrapper {
            display: flex;
            align-items: flex-end;
            background: #f0f2f5;
            border-radius: 20px;
            padding: 8px 12px;
            gap: 8px;
        }

        .input-actions-left {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .input-action-btn {
            background: transparent;
            border: none;
            color: #65676b;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            font-size: 16px;
        }

        .input-action-btn:hover {
            background: rgba(0,0,0,0.05);
        }

        .gif-text {
            font-size: 12px;
            font-weight: 600;
            color: #65676b;
        }

        .message-input-wrapper {
            flex: 1;
            display: flex;
            align-items: flex-end;
            gap: 8px;
        }

        .modern-message-input {
            flex: 1;
            border: none;
            background: transparent;
            resize: none;
            outline: none;
            font-size: 15px;
            line-height: 20px;
            max-height: 100px;
            min-height: 20px;
            padding: 6px 0;
            font-family: inherit;
        }

        .modern-message-input::placeholder {
            color: #65676b;
        }

        .input-actions-right {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .send-button-container {
            display: flex;
            justify-content: flex-end;
        }

        .modern-send-btn {
            background: #008374;
            color: #fff;
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .modern-send-btn:hover {
            background: #006b5d;
        }

        .modern-send-btn:disabled {
            background: #e4e6ea;
            color: #bcc0c4;
            cursor: not-allowed;
        }

        .send-text {
            font-size: 14px;
        }

        .online-dot {
            height: 10px;
            width: 10px;
            background-color: #28a745;
            border-radius: 50%;
            display: inline-block;
        }



        .contact-item {
            display: flex;
            align-items: center;
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            position: relative;
        }

            .contact-item:hover {
                background-color: #e2f3f1;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 10px;
            }

        .notification-badge {
            position: absolute;
            right: 10px;
            top: 15px;
            background-color: #ff4b4b;
            color: white;
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 10px;
        }
        .message-container {
    margin-bottom: 15px;
    max-width: 75%;
    padding: 10px;
    border-radius: 10px;
    background-color: #f1f1f1;
}

.sent {
    background-color: #d1f5e0;
    align-self: flex-end;
    margin-left: auto;
}

.received {
    background-color: #fff;
    border: 1px solid #ddd;
    margin-right: auto;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.message-header .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.message-body p {
    margin: 0;
    font-size: 14px;
}

.attachment-link {
    display: inline-block;
    margin-top: 5px;
    color: #008374;
    font-weight: bold;
}

        .message-counter {
            padding: 5px 15px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            text-align: right;
        }



        .typing-indicator {
            font-style: italic;
            color: #666;
            padding: 10px;
            display: none;
        }

        /* Styles pour les nouvelles fonctionnalités */
        .message-input-container {
            display: flex;
            align-items: flex-end;
            gap: 10px;
            position: relative;
        }

        .input-toolbar {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .toolbar-btn {
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .toolbar-btn:hover {
            background: #e0e0e0;
            transform: scale(1.1);
        }

        .send-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            background: #0056b3;
            transform: scale(1.05);
        }

        /* Sélecteur d'émojis */
        .emoji-picker {
            position: absolute;
            bottom: 100%;
            left: 0;
            width: 300px;
            height: 250px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
        }

        .emoji-header {
            display: flex;
            border-bottom: 1px solid #eee;
            padding: 10px;
        }

        .emoji-tab {
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 5px;
            margin-right: 5px;
            transition: background 0.3s ease;
        }

        .emoji-tab:hover, .emoji-tab.active {
            background: #f0f0f0;
        }

        .emoji-content {
            padding: 10px;
            height: 180px;
            overflow-y: auto;
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 5px;
        }

        .emoji-item {
            padding: 8px;
            text-align: center;
            cursor: pointer;
            border-radius: 5px;
            font-size: 18px;
            transition: background 0.3s ease;
        }

        .emoji-item:hover {
            background: #f0f0f0;
            transform: scale(1.2);
        }

        /* Prévisualisation des pièces jointes */
        .attachment-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
            margin-top: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .attachment-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 16px;
            line-height: 1;
        }

        .remove-btn:hover {
            background: #c82333;
        }

        /* Statuts de messages */
        .message-status {
            font-size: 12px;
            color: #666;
            margin-left: 10px;
        }

        .status-sent { color: #6c757d; }
        .status-delivered { color: #007bff; }
        .status-read { color: #28a745; }

        /* Notifications */
        .notification-badge {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 11px;
            position: absolute;
            top: -5px;
            right: -5px;
            min-width: 18px;
            text-align: center;
        }

        /* Pièces jointes dans les messages */
        .message-attachment {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .attachment-icon {
            font-size: 24px;
        }

        .attachment-details {
            flex: 1;
        }

        .attachment-name {
            font-weight: bold;
            color: #333;
        }

        .attachment-size {
            font-size: 12px;
            color: #666;
        }

        .attachment-download {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px 10px;
            cursor: pointer;
            text-decoration: none;
            font-size: 12px;
        }

        .attachment-download:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
        }

        /* Images dans les messages */
        .message-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .message-image:hover {
            transform: scale(1.05);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message-container {
            animation: fadeIn 0.3s ease-out;
        }

        .input-action-btn {
            transition: all 0.2s ease;
        }

        .input-action-btn:active {
            transform: scale(0.95);
        }

        /* Indicateur de statut en ligne */
        .contact-item::before {
            content: '';
            position: absolute;
            left: 52px;
            top: 48px;
            width: 12px;
            height: 12px;
            background: #42b883;
            border: 2px solid #fff;
            border-radius: 50%;
            display: none;
        }

        .contact-item.online::before {
            display: block;
        }

        /* Animation de chargement pour la recherche */
        .search-loading {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #008374;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: none;
        }

        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .chat-wrapper {
                height: 90vh;
                flex-direction: column;
            }

            .contacts-panel {
                width: 100%;
                height: 250px;
                border-right: none;
                border-bottom: 1px solid #ddd;
            }

            .contacts-list-container {
                height: calc(100% - 120px);
            }



            .header-content {
                padding: 10px 12px;
            }

            .header-title {
                font-size: 14px;
            }

            .action-btn {
                width: 28px;
                height: 28px;
                font-size: 12px;
            }

            .search-container {
                padding: 8px 12px;
            }

            .modern-search-input {
                padding: 8px 10px 8px 32px;
                font-size: 13px;
            }

            .chat-footer {
                padding: 12px;
            }

            .input-wrapper {
                padding: 6px 10px;
            }

            .input-action-btn {
                width: 28px;
                height: 28px;
                font-size: 14px;
            }

            .modern-message-input {
                font-size: 14px;
            }

            .modern-send-btn {
                padding: 6px 12px;
                font-size: 13px;
            }

            .emoji-picker {
                width: 280px;
                height: 200px;
                left: 50%;
                transform: translateX(-50%);
            }

            .emoji-grid {
                grid-template-columns: repeat(6, 1fr);
            }

            .message-image {
                max-width: 150px;
                max-height: 150px;
            }
        }

        /* États de focus améliorés */
        .modern-message-input:focus {
            outline: none;
        }

        .input-wrapper:focus-within {
            background: #e8f4fd;
            box-shadow: 0 0 0 2px rgba(0,131,116,0.2);
        }

        /* Amélioration de l'accessibilité */
        .input-action-btn:focus,
        .action-btn:focus,
        .modern-send-btn:focus {
            outline: 2px solid #008374;
            outline-offset: 2px;
        }

        /* Indicateur de frappe */
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            font-style: italic;
            color: #65676b;
            font-size: 13px;
        }

        .typing-dots {
            display: flex;
            gap: 2px;
        }

        .typing-dot {
            width: 4px;
            height: 4px;
            background: #65676b;
            border-radius: 50%;
            animation: typingAnimation 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typingAnimation {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
    </style>

    <script type="text/javascript">
        // Compteur de caractères
        function updateCharCount() {
            var textarea = document.getElementById('<%= txtMessage.ClientID %>');
            var counter = document.getElementById('charCount');
            if (textarea && counter) {
                var length = textarea.value.length;
                counter.textContent = length + '/1000 caractères';

                if (length > 900) {
                    counter.style.color = 'red';
                } else if (length > 800) {
                    counter.style.color = 'orange';
                } else {
                    counter.style.color = '#666';
                }
            }
        }

        // Auto-scroll vers le bas des messages
        function scrollToBottom() {
            var chatBody = document.querySelector('.chat-body');
            if (chatBody) {
                chatBody.scrollTop = chatBody.scrollHeight;
            }
        }

        // Envoyer message avec Enter (Shift+Enter pour nouvelle ligne)
        function handleKeyPress(event) {
            if (event.keyCode === 13 && !event.shiftKey) {
                event.preventDefault();
                var btnEnvoie = document.getElementById('<%= btnenvoie.ClientID %>');
                if (btnEnvoie) {
                    btnEnvoie.click();
                }
            }
        }

        // Auto-resize du textarea
        function autoResizeTextarea() {
            var textarea = document.getElementById('<%= txtMessage.ClientID %>');
            if (textarea) {
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';

                // Activer/désactiver le bouton d'envoi
                var sendBtn = document.getElementById('<%= btnenvoie.ClientID %>');
                if (sendBtn) {
                    if (textarea.value.trim().length > 0) {
                        sendBtn.disabled = false;
                        sendBtn.style.opacity = '1';
                    } else {
                        sendBtn.disabled = true;
                        sendBtn.style.opacity = '0.5';
                    }
                }
            }
        }

        // Gestion des émojis
        function toggleEmojiPicker() {
            var picker = document.getElementById('emojiPicker');
            picker.style.display = picker.style.display === 'none' ? 'block' : 'none';
        }

        function insertEmoji(emoji) {
            var textarea = document.getElementById('<%= txtMessage.ClientID %>');
            var cursorPos = textarea.selectionStart;
            var textBefore = textarea.value.substring(0, cursorPos);
            var textAfter = textarea.value.substring(cursorPos);

            textarea.value = textBefore + emoji + textAfter;
            textarea.focus();
            textarea.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);

            updateCharCount();
            document.getElementById('emojiPicker').style.display = 'none';
        }

        // Gestion des pièces jointes
        function handleFileSelect() {
            var fileInput = document.getElementById('fileAttachment');
            var file = fileInput.files[0];

            if (file) {
                // Validation de la taille (10MB max)
                if (file.size > 10 * 1024 * 1024) {
                    alert('Le fichier est trop volumineux (maximum 10 Mo)');
                    fileInput.value = '';
                    return;
                }

                // Validation de l'extension
                var allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.txt', '.zip', '.rar', '.mp3', '.mp4', '.avi'];
                var fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

                if (allowedExtensions.indexOf(fileExtension) === -1) {
                    alert('Type de fichier non autorisé. Extensions autorisées: ' + allowedExtensions.join(', '));
                    fileInput.value = '';
                    return;
                }

                // Afficher la prévisualisation
                showAttachmentPreview(file);

                // Uploader le fichier immédiatement
                uploadFile(file);
            }
        }

        // Fonction pour uploader le fichier (version simplifiée)
        function uploadFile(file) {
            // Pour l'instant, on stocke juste les informations du fichier
            // L'upload réel se fera lors de l'envoi du message

            var nameSpan = document.getElementById('attachmentName');
            var sizeSpan = document.getElementById('attachmentSize');

            // Afficher les informations du fichier
            nameSpan.textContent = file.name;
            sizeSpan.textContent = formatFileSize(file.size);

            // Marquer qu'un fichier est prêt à être uploadé
            document.getElementById('<%= hdnAttachmentPath.ClientID %>').value = 'READY_TO_UPLOAD';

            console.log('Fichier prêt pour upload: ' + file.name);
        }

        function showAttachmentPreview(file) {
            var preview = document.getElementById('attachmentPreview');
            var nameSpan = document.getElementById('attachmentName');
            var sizeSpan = document.getElementById('attachmentSize');

            nameSpan.textContent = file.name;
            sizeSpan.textContent = formatFileSize(file.size);
            preview.style.display = 'block';
        }

        function removeAttachment() {
            document.getElementById('fileAttachment').value = '';
            document.getElementById('attachmentPreview').style.display = 'none';
            document.getElementById('<%= hdnAttachmentPath.ClientID %>').value = '';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // Gestion de la sélection des contacts
        function selectContact(contactElement) {
            // Retirer la classe active de tous les contacts
            document.querySelectorAll('.contact-item').forEach(function(item) {
                item.classList.remove('active');
            });

            // Ajouter la classe active au contact sélectionné
            contactElement.classList.add('active');

            // Marquer les messages comme lus (simulation)
            var unreadBadge = contactElement.querySelector('.unread-badge');
            if (unreadBadge) {
                unreadBadge.style.display = 'none';
            }


        }





        // Simulation du statut en ligne
        function simulateOnlineStatus() {
            var contacts = document.querySelectorAll('.contact-item');
            contacts.forEach(function(contact, index) {
                // Simuler aléatoirement le statut en ligne
                if (Math.random() > 0.5) {
                    contact.classList.add('online');
                }
            });
        }



        // Initialisation au chargement de la page
        window.onload = function() {
            var textarea = document.getElementById('<%= txtMessage.ClientID %>');
            if (textarea) {
                textarea.addEventListener('input', function() {
                    updateCharCount();
                    autoResizeTextarea();
                });
                textarea.addEventListener('keypress', handleKeyPress);

                // Initialiser l'état du bouton d'envoi
                autoResizeTextarea();
            }

            // Ajouter les gestionnaires d'événements pour les contacts
            document.querySelectorAll('.contact-item').forEach(function(contact) {
                contact.addEventListener('click', function() {
                    selectContact(this);
                });
            });

            // Simuler le statut en ligne
            simulateOnlineStatus();



            // Gestion du bouton émoji
            var btnEmoji = document.getElementById('btnEmoji');
            if (btnEmoji) {
                btnEmoji.addEventListener('click', toggleEmojiPicker);
            }

            // Gestion du bouton pièce jointe
            var btnAttachment = document.getElementById('btnAttachment');
            if (btnAttachment) {
                btnAttachment.addEventListener('click', function() {
                    document.getElementById('fileAttachment').click();
                });
            }

            // Gestion de la sélection de fichier
            var fileAttachment = document.getElementById('fileAttachment');
            if (fileAttachment) {
                fileAttachment.addEventListener('change', handleFileSelect);
            }

            // Gestion de la suppression de pièce jointe
            var btnRemoveAttachment = document.getElementById('btnRemoveAttachment');
            if (btnRemoveAttachment) {
                btnRemoveAttachment.addEventListener('click', removeAttachment);
            }

            // Gestion des clics sur les émojis
            document.querySelectorAll('.emoji-item').forEach(function(item) {
                item.addEventListener('click', function() {
                    insertEmoji(this.dataset.emoji);
                });
            });

            // Fermer le sélecteur d'émojis en cliquant ailleurs
            document.addEventListener('click', function(e) {
                var picker = document.getElementById('emojiPicker');
                var btnEmoji = document.getElementById('btnEmoji');

                if (picker && btnEmoji && !picker.contains(e.target) && e.target !== btnEmoji) {
                    picker.style.display = 'none';
                }
            });

            scrollToBottom();
            updateCharCount();
        };

        // Actualiser les messages toutes les 30 secondes
        setInterval(function() {
            if (document.getElementById('<%= lblId.ClientID %>').textContent !== '0') {
                __doPostBack('<%= Page.ClientID %>', 'RefreshMessages');
            }
        }, 30000);
    </script>

</asp:Content>
