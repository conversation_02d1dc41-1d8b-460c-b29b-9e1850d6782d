using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;

namespace LinCom.Classe
{
    /// <summary>
    /// Service pour les fonctionnalités avancées de messagerie
    /// </summary>
    public class MessagerieService
    {
        private readonly IMessage _messageService;
        private readonly IConversation _conversationService;

        public MessagerieService()
        {
            _messageService = new MessageImp();
            _conversationService = new ConversationImp();
        }

        /// <summary>
        /// Obtient les statistiques de messagerie pour un utilisateur
        /// </summary>
        public StatistiquesMessagerie ObtenirStatistiques(long membreId)
        {
            using (var con = new Connection())
            {
                try
                {
                    var stats = new StatistiquesMessagerie();

                    // Nombre total de messages envoyés
                    stats.MessagesEnvoyes = con.Messages.Count(m => m.SenderId == membreId);

                    // Nombre total de messages reçus
                    stats.MessagesRecus = (from m in con.Messages
                                          join pc in con.ParticipantConversations on m.ConversationId equals pc.ConversationId
                                          where pc.MembreId == membreId && m.SenderId != membreId
                                          select m).Count();

                    // Nombre de conversations actives
                    stats.ConversationsActives = con.ParticipantConversations.Count(pc => pc.MembreId == membreId);

                    // Nombre de messages non lus
                    stats.MessagesNonLus = (from ms in con.MessageStatus
                                           where ms.UserId == membreId && ms.IsRead == 0
                                           select ms).Count();

                    // Dernière activité
                    var dernierMessage = (from m in con.Messages
                                         join pc in con.ParticipantConversations on m.ConversationId equals pc.ConversationId
                                         where pc.MembreId == membreId
                                         orderby m.DateEnvoi descending
                                         select m.DateEnvoi).FirstOrDefault();

                    stats.DerniereActivite = dernierMessage;

                    return stats;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur lors du calcul des statistiques: {ex.Message}");
                    return new StatistiquesMessagerie();
                }
            }
        }

        /// <summary>
        /// Obtient les conversations récentes d'un utilisateur
        /// </summary>
        public List<ConversationResume> ObtenirConversationsRecentes(long membreId, int limite = 10)
        {
            using (var con = new Connection())
            {
                try
                {
                    var conversations = (from c in con.Conversations
                                        join pc in con.ParticipantConversations on c.ConversationId equals pc.ConversationId
                                        where pc.MembreId == membreId
                                        let dernierMessage = con.Messages
                                            .Where(m => m.ConversationId == c.ConversationId)
                                            .OrderByDescending(m => m.DateEnvoi)
                                            .FirstOrDefault()
                                        let autreParticipant = con.ParticipantConversations
                                            .Where(p => p.ConversationId == c.ConversationId && p.MembreId != membreId)
                                            .Join(con.Membres, p => p.MembreId, m => m.MembreId, (p, m) => m)
                                            .FirstOrDefault()
                                        where dernierMessage != null
                                        orderby dernierMessage.DateEnvoi descending
                                        select new ConversationResume
                                        {
                                            ConversationId = c.ConversationId,
                                            Sujet = c.Sujet,
                                            EstGroupe = c.IsGroup == 1,
                                            NomAutreParticipant = autreParticipant != null ? 
                                                (autreParticipant.Nom + " " + autreParticipant.Prenom) : "Inconnu",
                                            PhotoAutreParticipant = autreParticipant != null ? autreParticipant.PhotoProfil ?? "default-avatar.png" : "default-avatar.png",
                                            DernierMessage = dernierMessage.Contenu,
                                            DateDernierMessage = dernierMessage.DateEnvoi,
                                            MessagesNonLus = con.MessageStatus.Count(ms => 
                                                con.Messages.Any(m => m.MessageId == ms.MessageId && 
                                                                     m.ConversationId == c.ConversationId) &&
                                                ms.UserId == membreId && ms.IsRead == 0)
                                        }).Take(limite).ToList();

                    return conversations;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur lors de la récupération des conversations: {ex.Message}");
                    return new List<ConversationResume>();
                }
            }
        }

        /// <summary>
        /// Nettoie les anciennes conversations et messages
        /// </summary>
        public int NettoyerAnciennesConversations()
        {
            using (var con = new Connection())
            {
                using (var transaction = con.Database.BeginTransaction())
                {
                    try
                    {
                        int elementsSupprimes = 0;
                        var dateLimit = DateTime.Now.AddDays(-MessagerieConfig.JoursConservationMessages);

                        // Supprimer les anciens messages
                        var ancienMessages = con.Messages.Where(m => m.DateEnvoi < dateLimit).ToList();
                        
                        foreach (var message in ancienMessages)
                        {
                            // Supprimer d'abord les statuts associés
                            var statuts = con.MessageStatus.Where(ms => ms.MessageId == message.MessageId);
                            con.MessageStatus.RemoveRange(statuts);
                            
                            // Puis supprimer le message
                            con.Messages.Remove(message);
                            elementsSupprimes++;
                        }

                        // Supprimer les conversations vides
                        var dateLimitConversations = DateTime.Now.AddDays(-MessagerieConfig.JoursConservationConversationsVides);
                        var conversationsVides = con.Conversations
                            .Where(c => c.CreatedAt < dateLimitConversations && 
                                       !con.Messages.Any(m => m.ConversationId == c.ConversationId))
                            .ToList();

                        foreach (var conversation in conversationsVides)
                        {
                            // Supprimer les participants
                            var participants = con.ParticipantConversations
                                .Where(pc => pc.ConversationId == conversation.ConversationId);
                            con.ParticipantConversations.RemoveRange(participants);
                            
                            // Supprimer la conversation
                            con.Conversations.Remove(conversation);
                            elementsSupprimes++;
                        }

                        con.SaveChanges();
                        transaction.Commit();

                        return elementsSupprimes;
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        System.Diagnostics.Debug.WriteLine($"Erreur lors du nettoyage: {ex.Message}");
                        return 0;
                    }
                }
            }
        }

        /// <summary>
        /// Marque toutes les notifications comme lues pour un utilisateur
        /// </summary>
        public int MarquerToutesNotificationsLues(long membreId)
        {
            try
            {
                return _messageService.MarquerConversationCommeLue(0, membreId); // 0 = toutes les conversations
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du marquage des notifications: {ex.Message}");
                return 0;
            }
        }
    }

    /// <summary>
    /// Statistiques de messagerie pour un utilisateur
    /// </summary>
    public class StatistiquesMessagerie
    {
        public int MessagesEnvoyes { get; set; }
        public int MessagesRecus { get; set; }
        public int ConversationsActives { get; set; }
        public int MessagesNonLus { get; set; }
        public DateTime? DerniereActivite { get; set; }
    }

    /// <summary>
    /// Résumé d'une conversation
    /// </summary>
    public class ConversationResume
    {
        public long ConversationId { get; set; }
        public string Sujet { get; set; }
        public bool EstGroupe { get; set; }
        public string NomAutreParticipant { get; set; }
        public string PhotoAutreParticipant { get; set; }
        public string DernierMessage { get; set; }
        public DateTime? DateDernierMessage { get; set; }
        public int MessagesNonLus { get; set; }
    }
}
