﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IPermission
    {
        void AfficherDetails(int idPermission, Permission_Class permission);
        int Ajouter(Permission_Class permission);
        int Modifier(Permission_Class permission);
        int Supprimer(int idPermission);
        void ChargerPermissions(GridView gdv);
        void chargerPermission(DropDownList lst);
        void ChargerPermissionsParCode(GridView gdv, string code);
    }
}
