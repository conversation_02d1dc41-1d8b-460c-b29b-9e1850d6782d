﻿using LinCom.Class;
using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IDomainePost
    {
        void AfficherDetails(DomainePost_Class domainePostClass, long domainePostId, long idorg, long iddom, int cd);
        int Ajouter(DomainePost_Class domainePostClass);
        int Modifier(DomainePost_Class domainePostClass, long id, long idorg, int cd);
        int Supprimer(long id, long idorg);
        void ChargerDomainesParPost(GridView gdv, long postId);
        void ChargerDomainesParOrganisation(GridView gdv, long organisationId);
        void ChargerListView(ListView gdv, long idpost, long idorg, int cd, string statut);
        void ChargerRepeater(Repeater gdv, long idpost, long idorg, int cd, string statut);
        void ChargerGridView(GridView gdv, long idpost, long idorg, int cd, string statut);
    }
}
