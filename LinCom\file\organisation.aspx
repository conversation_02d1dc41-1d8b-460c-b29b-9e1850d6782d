﻿<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" ValidateRequest="false" AutoEventWireup="true" CodeBehind="organisation.aspx.cs" Inherits="LinCom.file.organisation" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <style>
        #map {
            height: 400px;
            width: 100%;
            margin-bottom: 15px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">

                                    <div class="breadcomb-ctn">
                                        <h2>Organisations</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">

                                    <a data-toggle="tooltip" data-placement="left" href="listorganisation.aspx" title="Clique sur ce button pour visualiser la liste des organisations" class="btn">Liste des organisations</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcomb area End-->
    <!-- Form Element area Start-->
    <div class="form-element-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">

                        <div class="cmp-tb-hd bcs-hd">
                            <p style="color: red">Remplissez les champs obligatoires avec asterix</p>
                        </div>
                        <div class="alert-list">
                            <asp:Panel ID="div_msg_succes" runat="server" CssClass="alert alert-success alert-dismissible" Visible="false">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                                <asp:Label ID="msg_succes" runat="server" Text="Enregistrement réussi"></asp:Label>
                            </asp:Panel>
                            <asp:Panel ID="div_msg_error" runat="server" CssClass="alert alert-danger alert-dismissible" Visible="false">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                                <asp:Label ID="msg_error" runat="server" Text="Enregistrement échoué"></asp:Label>
                            </asp:Panel>
                        </div>
                        <div class="row">

                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Type d'organisation *</label>
                                        <asp:DropDownList class="form-control" ID="drpdtype" runat="server">
                                            <asp:ListItem Value="-1">Selectionner le Type d'organisation</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-house"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Nom d'organisation *</label>
                                        <input type="text" runat="server" id="txtNomOrganisation" class="form-control input-sm" placeholder="Nom Complet de l'organisation *">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-house"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Sigle d'organisation *</label>
                                        <input type="text" runat="server" id="txtSigle" class="form-control input-sm" placeholder="Sigle de l'organisation">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-mail"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Email correcte d'organisation *</label>
                                        <input type="email" runat="server" id="txtEmail" class="form-control input-sm" placeholder="Adresse E-mail *">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-mail"></i>
                                    </div>
                                    <div class="nk-int-st">
                                          <label>Confirmez l'Email correcte d'organisation *</label>
                                        <input type="email" runat="server" id="txtConfirmEmail" class="form-control input-sm" placeholder="Confimer l'Adresse E-mail *">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-phone"></i>
                                    </div>
                                    <div class="nk-int-st">
                                          <label>Numero de Téléphone d'organisation *</label>
                                        <input type="text" runat="server" id="txtTelephone" class="form-control input-sm" placeholder="Numéro de Téléphone *">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-map"></i>
                                    </div>
                                    <div class="nk-int-st">
                                          <label>Est ce que l'organisation est enregistrée? *</label>
                                        <asp:DropDownList class="form-control" ID="drpdexiste" runat="server">
                                            <asp:ListItem Value="-1">Est ce que l'organisation est enregistrée?</asp:ListItem>
                                            <asp:ListItem Value="formel">Oui</asp:ListItem>
                                            <asp:ListItem Value="informel">Non</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-map"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Province *</label>
                                        <asp:DropDownList class="form-control" ID="drpdprov" runat="server" AutoPostBack="true" OnSelectedIndexChanged="drpdprov_SelectedIndexChanged">
                                            <asp:ListItem Value="-1">Selectionner la Province</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-map"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Commune *</label>
                                        <asp:DropDownList class="form-control" ID="drpdcom" runat="server">
                                            <asp:ListItem Value="-1">Selectionner la Commune</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-map"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Adresse Complete *</label>
                                        <input type="text" runat="server" id="txtAdresse" class="form-control input-sm" placeholder="Ex : Kabondo, Muha,Bujumbura Mairie, Burundi *">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-calendar"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Date de Création *</label>
                                        <input type="date" runat="server" id="txtDateCreation" class="form-control input-sm" placeholder="Adresse complète de l'organisation *">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12" runat="server" id="statu" visible="false">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Statut *</label>
                                        <asp:DropDownList class="form-control" ID="drpdstatut" runat="server">

                                            <asp:ListItem Value="-1">Selectionner le statut de l'association</asp:ListItem>
                                            <asp:ListItem Value="actif">Actif</asp:ListItem>
                                            <asp:ListItem Value="inactif">Inactif</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 ">
                                <h6>Information sur les domaines d'intervention</h6>
                                <div class="col-md-12">
                                    <i>Vous pouvez choisir un ou plusieurs domaines d'intervention de votre organisation en selectionnant dans la liste. Si votre domaine n'existe pas, veuillez cliquer sur <b>Mon domaine n'existe pas, je veux creer</b>.</i>
                                    <asp:DropDownList ID="drpddomai" runat="server" data-parsley-trigger="change" autocomplete="off" class="form-control" AutoPostBack="true" OnSelectedIndexChanged="drpddomai_SelectedIndexChanged">
                                        <asp:ListItem Value="-1">Selectionner le domaine d'Intervention</asp:ListItem>
                                    </asp:DropDownList>
                                    <a href="srdikis.aspx" runat="server" id="doma" onserverclick="doma_ServerClick" visible="false">Ajouter d'autres domaines</a> <b>|</b>
                                    <a href="srdikis.aspx" runat="server" id="ajoudom" onserverclick="ajoudom_ServerClick">Mon domaine n'existe pas, je veux creer</a>
                                    <div id="dom" runat="server" visible="false">
                                        <h6 runat="server" visible="false">Veuillez selectionner le domaine et cliquer sur le button Ajouter un domaine, et ajoutez d'autres domaines</h6>
                                        <button type="submit" visible="false" runat="server" id="btnajourdom" onserverclick="btnajourdom_ServerClick">Ajouter à la un domaine</button>
                                        <button type="submit" runat="server" id="btnannuldom" onserverclick="btnannuldom_ServerClick" class="btn btn-danger notika-gp-danger">Recommencer</button>

                                        <asp:GridView ID="GridView1" runat="server"
                                            ShowHeaderWhenEmpty="True"
                                            AutoGenerateColumns="True"
                                            EmptyDataText="Aucune Donnée Trouvée pour votre Rercherche"
                                            ShowFooter="true" FooterStyle-Font-Bold="true"
                                            EditRowStyle-Font-Bold="true"
                                            EmptyDataRowStyle-Font-Names="century"
                                            EmptyDataRowStyle-Font-Size="X-Large"
                                            EmptyDataRowStyle-HorizontalAlign="Center"
                                            GridLines="None" AllowPaging="True"
                                            CellSpacing="0" Width="100%">
                                            <AlternatingRowStyle BackColor="#DCDCDC" />

                                        </asp:GridView>
                                    </div>

                                    <div runat="server" id="ajourdom" visible="false">
                                        <div class="card">
                                            <h5 class="card-header">Informations sur Les Domaines d'Intervention</h5>

                                            <div class="col-md-12 ">
                                                <label>Nom du Domaine d'activité</label>
                                                <input id="txtnmdom" runat="server" type="text" data-parsley-trigger="change" placeholder="Nom du Domaine d'activité" autocomplete="off" class="form-control" />
                                            </div>
                                            <div class="col-md-12 ">
                                                <div class="col-sm-6 pl-0">
                                                    <p class="text-right">
                                                        <button type="submit" class="btn btn-success notika-gp-success" runat="server" id="btnajoutnouvdom" onserverclick="btnajoutnouvdom_ServerClick">Enregistrer</button>

                                                    </p>
                                                </div>
                                            </div>

                                        </div>

                                    </div>
                                </div>
                            </div>


                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-picture"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Logo </label>
                                        <asp:FileUpload ID="fileupd" runat="server" class="form-control" />

                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-form"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Description de l'organisation </label>
                                        <div>
                                            <textarea class="html-editor" runat="server" id="txtdescription" rows="10"></textarea>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                  <button type="button" onclick="getCoordinates()" class="btn btn-success notika-gp-success">Obtenir les coordonnées</button><br /><br />

                              
                                <asp:HiddenField ID="hdnLatitude" runat="server" />
<asp:HiddenField ID="hdnLongitude" runat="server" />

        <label for="txtLatitude">Latitude :</label><br />
        <asp:TextBox ID="txtLatitude" class="form-control" runat="server" ReadOnly="true" /><br /><br />

        <label for="txtLongitude">Longitude :</label><br />
        <asp:TextBox ID="txtLongitude" class="form-control" runat="server" ReadOnly="true" /><br /><br />

      <div id="map"></div>
                            </div>

                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <button type="button" runat="server" id="btnEnregistrer" onserverclick="btnEnregistrer_ServerClick" class="btn btn-success notika-gp-success">Ajouter</button>
                            </div>

                        </div>

                    </div>
                </div>
            </div>

        </div>
    </div>
    <!-- Form Element area End-->

    <!-- Leaflet JS -->
    <!-- JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>

 <script>
     var map = L.map('map').setView([-3.38, 29.37], 8);
     L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
         attribution: '&copy; Linked Community Burundi'
     }).addTo(map);

     var marker;

     function updateLatLngInputs(lat, lng) {
         document.getElementById('<%= txtLatitude.ClientID %>').value = lat.toFixed(6);
        document.getElementById('<%= txtLongitude.ClientID %>').value = lng.toFixed(6);
        document.getElementById('<%= hdnLatitude.ClientID %>').value = lat.toFixed(6);
        document.getElementById('<%= hdnLongitude.ClientID %>').value = lng.toFixed(6);
    }

    function getCoordinates() {
        var adresse = document.getElementById('<%= txtAdresse.ClientID %>').value;

        $.get("https://nominatim.openstreetmap.org/search", {
            q: adresse,
            format: "json"
        }, function (data) {
            if (data.length > 0) {
                let lat = parseFloat(data[0].lat);
                let lon = parseFloat(data[0].lon);

                updateLatLngInputs(lat, lon);

                map.setView([lat, lon], 13);

                if (marker) {
                    marker.setLatLng([lat, lon])
                          .bindPopup("Position trouvée").openPopup();
                } else {
                    marker = L.marker([lat, lon], { draggable: true }).addTo(map)
                              .bindPopup("Position trouvée").openPopup();
                    marker.on('dragend', function (e) {
                        var pos = e.target.getLatLng();
                        updateLatLngInputs(pos.lat, pos.lng);
                    });
                }
            } else {
                alert("Adresse introuvable.");
            }
        });
    }

    window.onload = function () {
        var latVal = parseFloat(document.getElementById('<%= txtLatitude.ClientID %>').value);
        var lngVal = parseFloat(document.getElementById('<%= txtLongitude.ClientID %>').value);
         if (!isNaN(latVal) && !isNaN(lngVal)) {
             map.setView([latVal, lngVal], 14);
             marker = L.marker([latVal, lngVal], { draggable: true }).addTo(map)
                 .bindPopup("Position existante").openPopup();
             marker.on('dragend', function (e) {
                 var pos = e.target.getLatLng();
                 updateLatLngInputs(pos.lat, pos.lng);
             });
         }
     };

     map.on('click', function (e) {
         var lat = e.latlng.lat;
         var lng = e.latlng.lng;

         updateLatLngInputs(lat, lng);

         if (marker) {
             marker.setLatLng(e.latlng)
                 .bindPopup("Position choisie").openPopup();
         } else {
             marker = L.marker(e.latlng, { draggable: true }).addTo(map)
                 .bindPopup("Position choisie").openPopup();
             marker.on('dragend', function (e) {
                 var pos = e.target.getLatLng();
                 updateLatLngInputs(pos.lat, pos.lng);
             });
         }
     });
 </script>

</asp:Content>
