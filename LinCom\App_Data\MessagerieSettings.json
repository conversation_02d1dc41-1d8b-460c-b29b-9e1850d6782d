{"MessagerieConfiguration": {"Securite": {"MaxLongueurMessage": 1000, "DelaiAntiSpamSecondes": 5, "MaxPieceJointeTailleMo": 10, "ExtensionsAutorisees": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".txt", ".zip", ".rar", ".mp3", ".mp4", ".avi"]}, "Performance": {"NombreMessagesParPage": 50, "DelaiActualisationSecondes": 30, "MaxResultatsRecherche": 20, "TimeoutRequeteSecondes": 30}, "Nettoyage": {"JoursConservationMessages": 365, "JoursConservationConversationsVides": 30, "FrequenceNettoyageHeures": 24, "ActiverNettoyageAutomatique": true}, "Interface": {"ThemeParDefaut": "light", "LangueParDefaut": "fr", "AfficherAvatars": true, "AfficherHeuresMessages": true, "AfficherStatutLecture": true, "ActiverNotificationsSonores": false}, "Fonctionnalites": {"ActiverRecherche": true, "ActiverPiecesJointes": true, "ActiverEmojis": false, "ActiverMessageriGroupe": false, "ActiverNotificationsTempsReel": false, "ActiverChiffrementMessages": false}, "Logging": {"NiveauLog": "Info", "LoggerErreurs": true, "LoggerPerformances": false, "LoggerActivitesUtilisateur": true, "CheminFichierLog": "~/App_Data/Logs/messagerie.log"}, "Messages": {"MessageTropLong": "Le message est trop long (maximum {0} caractères)", "DestinataireInvalide": "Destinataire invalide", "MessageVide": "Veuillez saisir un message", "AntiSpam": "Veuillez attendre avant d'envoyer le même message", "ErreurEnvoi": "Erreur lors de l'envoi du message", "MessageEnvoye": "Message envoy<PERSON> avec succès", "ConversationIntrouvable": "Conversation introuvable", "MembreIntrouvable": "Membre introuvable", "AccesRefuse": "<PERSON><PERSON>ès refusé", "FichierTropGros": "Le fichier est trop volumineux (maximum {0} Mo)", "ExtensionNonAutorisee": "Type de fichier non autorisé", "ErreurConnexion": "Erreur de connexion à la base de données", "ErreurInterne": "Une erreur interne s'est produite", "SessionExpiree": "Votre session a expiré, veuillez vous reconnecter"}, "Validation": {"MinLongueurRecherche": 2, "MaxLongueurRecherche": 50, "RegexEmail": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "CaracteresInterdits": ["<script>", "</script>", "javascript:", "vbscript:", "onload=", "onerror="], "MaxTentativesConnexion": 5, "DureeBlocageMinutes": 15}, "Cache": {"ActiverCache": true, "DureeCacheMinutes": 10, "CacherListeContacts": true, "CacherStatistiques": true, "CacherConversationsRecentes": true}, "BaseDonnees": {"TimeoutCommandeSecondes": 30, "NombreMaxConnexions": 100, "ActiverPoolingConnexions": true, "LoggerRequetes": false}, "Notifications": {"ActiverNotificationsEmail": false, "ActiverNotificationsPush": false, "TemplateEmailNotification": "~/Templates/NotificationEmail.html", "DelaiGroupageNotificationsMinutes": 5}, "Maintenance": {"ModeMaintenanceActif": false, "MessageMaintenance": "Le système de messagerie est temporairement indisponible pour maintenance.", "HeureDebutMaintenance": "02:00", "HeureFinMaintenance": "04:00", "JoursMaintenance": ["dimanche"]}, "Monitoring": {"ActiverMonitoring": true, "SeuillAlerteTempReponseMs": 5000, "SeuillAlerteErreursPourcentage": 5, "IntervalleMonitoringMinutes": 5, "EmailAlertes": ["<EMAIL>"]}, "Backup": {"ActiverSauvegardeAutomatique": true, "FrequenceSauvegardeHeures": 6, "NombreSauvegardesConserver": 7, "CheminSauvegarde": "~/App_Data/Backups/", "CompresserSauvegardes": true}}, "EnvironnementConfiguration": {"Environnement": "Development", "VersionApplication": "2.0.0", "DateMiseAJour": "2024-01-01", "ActiverModeDebug": true, "ActiverProfilage": false, "UrlBaseApplication": "https://localhost:44319/", "NomApplication": "LinCom - Messagerie"}}