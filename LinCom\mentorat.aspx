﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="mentorat.aspx.cs" Inherits="LinCom.mentorat" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
     <!-- Titre de la page -->
 <div class="page-title">
     <div class="heading">
         <div class="container">
             <div class="row d-flex justify-content-center text-center">
                 <div class="col-lg-8">
                      <h1 class="fw-bold display-5" runat="server" id="txttitle">Programme de Mentorat Jeunes Leaders</h1>
 <p class="lead">Rejoignez un réseau dynamique pour apprendre, guider et grandir.</p>
                 </div>
             </div>
         </div>
     </div>
     <nav class="breadcrumbs">
         <div class="container">
             <ol>
                 <li><a href="home.aspx">Home</a></li>
                 <li><a href="home.aspx">Programmes de Mentorat</a></li>
                 <li class="current" runat="server" id="txttitle2">Mentorat</li>
                
             </ol>
         </div>
     </nav>
 </div>
  

    <main class="main py-5">
        <div class="container">
            <div class="row g-5">
                <!-- Contenu principal -->
                <div class="col-lg-8">
                    <div class="bg-white rounded-4 shadow-sm p-4">

                        <!-- Infos principales -->
                        <h2 class="fw-bold mb-3" runat="server" id="txttitle1">Présentation du Programme</h2>
                        <p class="text-muted" runat="server" id="txtdescription">Le programme vise à accompagner les jeunes dans leur développement professionnel grâce à l'encadrement de mentors expérimentés.</p>

                        <ul class="list-unstyled mb-4">
                            <li><strong>Organisation Hôte :</strong> <span runat="server" id="txtauteur">ONG Élan Jeunesse</span></li>
                            <li><strong>Date limite d’inscription :</strong> <span runat="server" id="txtdateinscrption">15 juillet 2025</span></li>
                            <li><strong>Statut :</strong> <span class="badge bg-success" runat="server" id="spanStatut"><span runat="server" id="txtstatut">Ouvert</span></span></li>
                            <li><strong>Durée :</strong> <span runat="server" id="txtduree">6 mois (août 2025 - janvier 2026)</span></li>
                            <li><strong>Cible :</strong> <span runat="server" id="txtcible">6 mois (août 2025 - janvier 2026)</span></li>
                            <li><strong>Nombre de mentors :</strong> <span runat="server" id="txtnbrementor">6 mois (août 2025 - janvier 2026)</span></li>
                            <li><strong>Nombre de mentorés :</strong> <span runat="server" id="txtnbrementore">6 mois (août 2025 - janvier 2026)</span></li>

                        </ul>

                        <!-- Appels à action -->
                        <div class="d-flex gap-3 mb-4">
                            <a href="" class="btn btn-lg text-white fw-semibold rounded-pill" style="background-color: #008374;" runat="server" id="linkmentor" onserverclick="linkmentor_ServerClick">
                                <i class="bi bi-person-plus me-1"></i>Devenir Mentor
                            </a>
                            <a href="inscriptionMentore.aspx" class="btn btn-lg text-white fw-semibold rounded-pill" style="background-color: #008374;" runat="server" id="linkmentore" onserverclick="linkmentore_ServerClick">
                                <i class="bi bi-person-plus-fill me-1"></i>Devenir Mentoré
                            </a>
                        </div>
                           <!-- Formulaire de commentaire -->
   <div class="card shadow-sm rounded-4 p-4" runat="server" id="envoiementor" visible="false">
       <h5 class="fw-bold mb-3 text-008374"> Vos qualifications pour participer en tant mentor dans ce programme</h5>

         <div class="mb-3">
      <label class="form-label">Titre de votre qualification (ex. Expert en VBG)</label>
      <input class="form-control" runat="server" id="txttitre" placeholder="Votre "/>
  </div>
       <div class="mb-3">
           <label class="form-label">Decrivez vos qualifications et vos competences</label>
           <textarea class="form-control" runat="server" id="txtqualification" rows="4" placeholder="Votre message..."></textarea>
       </div>
       <button class="btn text-white fw-semibold" style="background-color: #008374;" type="button" runat="server" id="btninscriptionmentor" onserverclick="btninscriptionmentor_ServerClick">
           <i class="bi bi-chat-left-text me-1"></i>Envoyer
       </button>
   </div>

                        <!-- Témoignages (exemples statiques) -->
                        <section class="py-5 bg-light" id="temoignages">
                            <div class="container">
                                <h2 class="text-center fw-bold mb-5">Témoignages inspirants des Mentors</h2>
                                <div class="row g-4">

                                    <asp:ListView ID="listcommentmentor" runat="server">
                                        <EmptyDataTemplate>Aucune Donnée</EmptyDataTemplate>
                                        <ItemTemplate>
                                            <!-- Témoignage 1 -->
                                            <div class="col-md-4">
                                                <div class="bg-white p-4 rounded shadow-sm h-100 d-flex flex-column align-items-center text-center">
                                                    <img src='<%# HttpUtility.HtmlEncode( string.Concat("../file/membr/",Eval("Photomembre"))) %>' class="rounded-circle mb-3" width="80" height="80" alt="Photo témoignage">
                                                    <p class="fst-italic text-muted"><%# Eval("contenu") %></p>
                                                    <h6 class="fw-bold text-dark mb-0"><%# Eval("contenu") %></h6>
                                                    <small class="text-muted">Entrepreneure sociale - Goma</small>
                                                </div>
                                            </div>
                                        </ItemTemplate>
                                    </asp:ListView>
                                    <!-- Témoignage 2 -->
                                    <div class="col-md-4">
                                        <div class="bg-white p-4 rounded shadow-sm h-100 d-flex flex-column align-items-center text-center">
                                            <img src="img/skills.png" class="rounded-circle mb-3" width="80" height="80" alt="Photo témoignage">
                                            <p class="fst-italic text-muted">"Grâce au mentorat, j’ai lancé mon association et trouvé des financements pour mes actions locales."</p>
                                            <h6 class="fw-bold text-dark mb-0">Jean-Paul K.</h6>
                                            <small class="text-muted">Activiste jeunesse - Bukavu</small>
                                        </div>
                                    </div>

                                    <!-- Témoignage 3 -->
                                    <div class="col-md-4">
                                        <div class="bg-white p-4 rounded shadow-sm h-100 d-flex flex-column align-items-center text-center">
                                            <img src="ing/skills.png" class="rounded-circle mb-3" width="80" height="80" alt="Photo témoignage">
                                            <p class="fst-italic text-muted">"Le suivi régulier, les outils partagés et les ateliers m’ont redonné confiance en moi."</p>
                                            <h6 class="fw-bold text-dark mb-0">Sylvie T.</h6>
                                            <small class="text-muted">Étudiante en droit - Kinshasa</small>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </section>
                        <!-- Section Commentaires -->
                        <div class="mt-5">
                            <h4 class="comments-count"><span runat="server" id="txtcommentpost">8</span></h4>

                            <!-- Liste des commentaires (exemples statiques ici) -->
                            <div class="mb-4">
                                <asp:ListView ID="listcommentairementore" runat="server">
                                    <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                                    <ItemTemplate>
                                        <div class="border-start border-3 ps-3 mb-3">
                                            <h6 class="mb-1 fw-semibold text-dark"><%#  HttpUtility.HtmlEncode(Eval("commentateur")) %></h6>
                                            <p class="mb-1 fst-italic text-muted"><%# Eval("contenu") %></p>
                                            <small class="text-muted">Posté <%# GetRelativeDate(Convert.ToDateTime(Eval("dateComm"))) %></small>
                                        </div>
                                    </ItemTemplate>
                                </asp:ListView>

                            </div>

                            <!-- Formulaire de commentaire -->
                            <div class="card shadow-sm rounded-4 p-4">
                                <h5 class="fw-bold mb-3 text-008374">Laisser un commentaire</h5>

                                <div class="mb-3">
                                    <label class="form-label">Commentaire</label>
                                    <textarea class="form-control" runat="server" id="txtmessage" rows="4" placeholder="Votre message..."></textarea>
                                </div>
                                <button class="btn text-white fw-semibold" style="background-color: #008374;" type="button" runat="server" id="btncomment" onserverclick="bntcomment_Click">
                                    <i class="bi bi-chat-left-text me-1"></i>Publier
                                </button>
                            </div>
                        </div>
                        <!-- Liste des Mentorés -->
                        <section class="py-5 bg-light">
                            <div class="container">
                                <h4 class="text-008374 fw-bold text-center mb-4">Mentorés du Programme</h4>

                                <div class="d-flex flex-wrap justify-content-center gap-3">

                                    <asp:ListView ID="listmentore" runat="server" OnItemCommand="listpost_ItemCommand">
                                        <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                                        <ItemTemplate>
                                            <!-- Mentoré 1 -->
                                            <div class="text-center">
                                                <img src='<%# HttpUtility.HtmlEncode( string.Concat("../file/membr/",Eval("Photomembre"))) %>' alt="Mentoré 1"
                                                    class="rounded-circle shadow-sm mb-1"
                                                    style="width: 80px; height: 80px; object-fit: cover;">
                                               <div class="small fw-semibold text-dark"><%# HttpUtility.HtmlEncode( Eval("Membre")) %></div>
                                            </div>
                                        </ItemTemplate>
                                    </asp:ListView>
                                    <!-- Mentoré 2 -->
                                    <div class="text-center">
                                        <img src="assets/img/avatar2.jpg" alt="Mentoré 2"
                                            class="rounded-circle shadow-sm mb-1"
                                            style="width: 80px; height: 80px; object-fit: cover;">
                                        <div class="small fw-semibold text-dark">Carlos N.</div>
                                    </div>

                                    <!-- Mentoré 3 -->
                                    <div class="text-center">
                                        <img src="assets/img/avatar3.jpg" alt="Mentoré 3"
                                            class="rounded-circle shadow-sm mb-1"
                                            style="width: 80px; height: 80px; object-fit: cover;">
                                        <div class="small fw-semibold text-dark">Fatima M.</div>
                                    </div>

                                    <!-- Mentoré 4 -->
                                    <div class="text-center">
                                        <img src="assets/img/avatar4.jpg" alt="Mentoré 4"
                                            class="rounded-circle shadow-sm mb-1"
                                            style="width: 80px; height: 80px; object-fit: cover;">
                                        <div class="small fw-semibold text-dark">Jacques T.</div>
                                    </div>

                                    <!-- Ajoute autant de mentorés que tu veux en copiant ce bloc -->

                                </div>
                            </div>
                        </section>

                    </div>
                </div>

                <!-- Colonne droite -->
                <div class="col-lg-4">

                    <!-- Programme similaires -->
                    <div class="card shadow-sm rounded-4 mb-4">
                        <div class="card-body">
                            <h5 class="fw-bold mb-3">Programmes Similaires</h5>
                            <ul class="list-unstyled">
                                <asp:ListView ID="listprogram" runat="server" OnItemCommand="listpost_ItemCommand">
                                    <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                                    <ItemTemplate>
                                        <li class="mb-3">
                                            <asp:LinkButton runat="server" class="text-decoration-none fw-semibold" CommandName="view" CommandArgument='<%# Eval("name") %>'><%# HttpUtility.HtmlEncode( Eval("Titre")) %></asp:LinkButton>

                                            <p class="text-muted small mb-0"><%# HttpUtility.HtmlEncode( Eval("statut")) %> : <%# HttpUtility.HtmlEncode( Eval("DatePublication")) %></p>
                                        </li>
                                    </ItemTemplate>
                                </asp:ListView>
                               
                            </ul>
                        </div>
                    </div>

                    <!-- Liste des Mentors -->
                    <section class="pb-5">
                        <div class="container">
                            <h4 class="text-008374 fw-bold text-center mb-4">Mentors du Programme</h4>
                            <div class="row justify-content-center g-2">

                                <asp:ListView ID="listmentor" runat="server" OnItemCommand="listpost_ItemCommand">
                                    <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                                    <ItemTemplate>
                                        <div class="col-6 col-md-4 col-lg-2 text-center">
                                            <img src='<%# HttpUtility.HtmlEncode( string.Concat("../file/actualit/",Eval("photo"))) %>' alt="Photo Mentor"
                                                class="rounded-circle shadow-sm mb-2"
                                                style="width: 100px; height: 100px; object-fit: cover;">
                                            <h6 class="fw-semibold mb-0 text-dark"><%# HttpUtility.HtmlEncode( Eval("Titre")) %></h6>
                                        </div>
                                    </ItemTemplate>
                                </asp:ListView>

                                <div class="col-6 col-md-4 col-lg-2 text-center">
                                    <img src="assets/img/skills.png" alt="Photo Mentor 2"
                                        class="rounded-circle shadow-sm mb-2"
                                        style="width: 100px; height: 100px; object-fit: cover;">
                                    <h6 class="fw-semibold mb-0 text-dark">Albert Mboyo</h6>
                                </div>

                                <div class="col-6 col-md-4 col-lg-2 text-center">
                                    <img src="assets/img/skills.png" alt="Photo Mentor 3"
                                        class="rounded-circle shadow-sm mb-2"
                                        style="width: 100px; height: 100px; object-fit: cover;">
                                    <h6 class="fw-semibold mb-0 text-dark">Fatou Diouf</h6>
                                </div>

                                <div class="col-6 col-md-4 col-lg-2 text-center">
                                    <img src="assets/img/skills.png" alt="Photo Mentor 4"
                                        class="rounded-circle shadow-sm mb-2"
                                        style="width: 100px; height: 100px; object-fit: cover;">
                                    <h6 class="fw-semibold mb-0 text-dark">Jean Bisimwa</h6>
                                </div>

                            </div>
                        </div>
                    </section>


                    <!-- Partage -->

                    <section class="pb-5 text-center">
                        <div class="container">
                            <h6><span>Publié le </span><span runat="server" id="txtdatepublication"></span></h6>

                            <h5 class="text-008374 fw-bold mb-3">Partager ce programme sur </h5>

                            <div class="d-flex justify-content-center flex-wrap gap-3 mt-4">
                                <asp:HiddenField ID="hfUrlPage" runat="server" />

                                <!-- Facebook -->
                                <a href='https://www.facebook.com/sharer/sharer.php?u=<%= hfUrlPage.Value %>'
                                    class="btn btn-lg rounded-circle shadow-sm text-white bg-primary"
                                    target="_blank" title="Partager sur Facebook">
                                    <i class="bi bi-facebook fs-4"></i>
                                </a>

                                <!-- Twitter/X -->
                                <a href='https://twitter.com/intent/tweet?url=<%= hfUrlPage.Value %>&text=Découvrez ce programme !'
                                    class="btn btn-lg rounded-circle shadow-sm text-white"
                                    style="background-color: #000000;" target="_blank"
                                    title="Partager sur X (Twitter)">
                                    <i class="bi bi-twitter-x fs-4"></i>
                                </a>

                                <!-- WhatsApp -->
                                <a href='https://wa.me/?text=<%= hfUrlPage.Value %>'
                                    class="btn btn-lg rounded-circle shadow-sm text-white"
                                    style="background-color: #25D366;" target="_blank"
                                    title="Partager sur WhatsApp">
                                    <i class="bi bi-whatsapp fs-4"></i>
                                </a>

                                <!-- LinkedIn -->
                                <a href='https://www.linkedin.com/sharing/share-offsite/?url=<%= hfUrlPage.Value %>'
                                    class="btn btn-lg rounded-circle shadow-sm text-white"
                                    style="background-color: #0077b5;" target="_blank"
                                    title="Partager sur LinkedIn">
                                    <i class="bi bi-linkedin fs-4"></i>
                                </a>

                                <!-- Copier le lien -->
                                <button type="button" class="btn btn-lg rounded-circle shadow-sm text-white bg-secondary"
                                    title="Copier le lien" onclick="copyToClipboard()">
                                    <i class="bi bi-link-45deg fs-4"></i>
                                </button>

                            </div>
                        </div>
                    </section>


                </div>
            </div>
        </div>
    </main>

    <script>
        function copyToClipboard() {
            const url = document.getElementById('<%= hfUrlPage.ClientID %>').value;
            navigator.clipboard.writeText(url).then(() => {
                alert("Lien copié dans le presse-papiers !");
            });
        }
    </script>
</asp:Content>
