﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class listprojet : System.Web.UI.Page
    {
        IPoste obj = new PosteImp();
        Post_Class pos = new Post_Class();
        Post_Class p = new Post_Class();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();

        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;

        int info;
        static string typenm; static int id, idpers, rol;
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
                    rolid = Convert.ToInt32(role.Value);//roleconnecte

                }
                else Response.Redirect("~/login.aspx");

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;
                getDataGDV();

            }
        }
        public void getDataGDV()
        {
            obj.Chargement_GDV(gdv, -1, idorg, "projet", 0);
            //  nbr.Text = obj.count().ToString();

        }
        protected void gdv_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            long index = Convert.ToInt32(e.CommandArgument);
            if (e.CommandName == "view")
            {
                Response.Redirect("~/file/projet.aspx?id=" + index);

            }
            if (e.CommandName == "delete")
            {
                try
                {
                    info = obj.supprimer(Convert.ToInt32(index), idorg);
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listprojet.aspx");
                    }
                    else
                    {
                        //  msg.Text = "Modification echoue";
                        //msg.Text = id.ToString();
                    }


                }
                catch (SqlException ex)
                {
                    // msg.Text = "Cette Province existe deja";
                }
            }
        }

    }
}