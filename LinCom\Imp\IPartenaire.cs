﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public interface IPartenaire
    {
        int Ajout(Partenaire_Class add);
        void Chargement_GDV(GridView GV_apv,string statu);
        void search(GridView GV_apv, string code,string statu);
        void afficherDetails(int code, Partenaire_Class pr);
        int edit(Partenaire_Class cl, int id);
        int supprimer(int id);
        int count(string statu);
        void Chargement_GDV(ListView GV_apv,string statu);
    }
}
