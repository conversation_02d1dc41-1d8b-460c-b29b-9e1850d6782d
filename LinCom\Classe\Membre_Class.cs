﻿using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace LinCom.Classe
{
    public class Membre_Class
    {
       

        public long MembreId { get; set; }
        public string Nom { get; set; }
        public string Prenom { get; set; }
        public string Email { get; set; }
        public string Telephone { get; set; }
        public string Sexe { get; set; }
        public Nullable<System.DateTime> DateNaissance { get; set; }
        public Nullable<int> ProvinceId { get; set; }
        public Nullable<int> CommuneId { get; set; }
        public Nullable<System.DateTime> CreatedAt { get; set; }
        public string name { get; set; }
        public string province { get; set; }
        public string commune { get; set; }
        public string username { get; set; }
        public string motpasse { get; set; }
        public string statut { get; set; }
        public Nullable<int> IsActive { get; set; }
        public Nullable<int> IsVerified { get; set; }
        public Nullable<System.DateTime> LastLogin { get; set; }
        public Nullable<int> RoleMembreID { get; set; }
        public string PhotoProfil { get; set; }
        public string facebook { get; set; }
        public string siteweb { get; set; }
        public string twitter { get; set; }
        public string instagramme { get; set; }
        public string linkedin { get; set; }
        public string youtube { get; set; }
        public string Biographie { get; set; }
        public Nullable<System.DateTime> DateInscription { get; set; }
        public string ResetToken { get; set; }
        public Nullable<System.DateTime> ResetTokenExpiry { get; set; }
        public string Adresse { get; set; }
        public string LanguePreferee { get; set; }
        public Nullable<int> AccepteNotification { get; set; }

        public virtual ICollection<ParticipantConversation> Conversations { get; set; }

        public virtual ICollection<Message> Messages { get; set; }
    }
}