using System;

namespace LinCom.Class
{
    public class Feedback_Class
    {
        public int IdFeedback { get; set; }
        public int? IdMembre { get; set; }
        public string TypeFeedback { get; set; }
        public int? IdReference { get; set; }
        public string Contenu { get; set; }
        public DateTime? DateFeedback { get; set; }
        public int? Note { get; set; }
        public string Status { get; set; }
        public bool? EstAnonyme { get; set; }
        public string CategoriesFeedback { get; set; }
        public string PointsForts { get; set; }
        public string PointsAmelioration { get; set; }
        public bool? EstUtile { get; set; }
        public int? NombreReponses { get; set; }
    }
}
