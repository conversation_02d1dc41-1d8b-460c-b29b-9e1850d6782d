﻿using LinCom.Class;
using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IAvisRessource
    {
        int Ajout(AvisRessource_Class add);
        void Chargement_GDV_Membre(GridView GV_apv, int code);
        void search(GridView GV_apv, string code);
        void Chargement_GDV(GridView gv, long id, long idorg, string name, string statut, int cd);
        void ChargementListview(ListView gv, long id, long idorg, string name, string statut, int cd);
        void afficherDetails(int code, AvisRessource_Class pr);
        int edit(AvisRessource_Class cl);
        int supprimer(int id);
        void chargerAvis_Membre(DropDownList lst, int code);
        void chargerAvis(DropDownList ddw);
        int count(long idme, long id, string statut, int cd);
    }
}
