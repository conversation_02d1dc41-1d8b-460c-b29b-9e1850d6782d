using LinCom.Model;
using System;
using System.Collections.Generic;

namespace LinCom.Class
{
    public class Conversation_Class
    {
        public long ConversationId { get; set; }
        public string Sujet { get; set; }
        public Nullable<int> IsGroup { get; set; }
        public Nullable<System.DateTime> CreatedAt { get; set; }
        // Navigation properties
        public virtual ICollection<ParticipantConversation> Participants { get; set; }

        public virtual ICollection<Message> Messages { get; set; }
    }
}
