﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Reflection;
using System.Runtime.Remoting;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using static System.Net.WebRequestMethods;

namespace LinCom.file
{
    public partial class posts : System.Web.UI.Page
    {
        private int info;
        string membreId, nsco;
        Membre_Class membreClass = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objMembre = new MembreImp();
        IProvince objProvince = new ProvinceImp();
        ICommune objCommune = new CommuneImp();
        ICommonCode co = new CommonCode();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        IPoste obj = new PosteImp();
        Post_Class pos=new Post_Class();
        Post_Class p=new Post_Class();
        DomainePost_Class actco = new DomainePost_Class();
        IDomainePost objactco = new DomainePostImp();
        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        CategoriePost_Class catpost=new CategoriePost_Class();
        ICategoryPost objcatpost=new CategoriePostImp();

        INotification objnotif=new NotificationImp();
        Notification_Class notif=new Notification_Class();

        DataTable dat = new DataTable();
        static string imge,imge1,pdfe, nameorg;
         long ide; 
         long idorg;
        static int rolid;
        long index;

        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            //if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            //{//admin

            //    long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
            //    rolid = Convert.ToInt32(role.Value);//roleconnecte

            //}
            //else Response.Redirect("~/login.aspx");
            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin

                long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
                rolid = Convert.ToInt32(role.Value);//roleconnecte

            }
            else Response.Redirect("~/login.aspx");

            objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
            idorg = Convert.ToInt64(memorg.OrganisationId);
            objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
            nameorg = org.Nom;
            if (!IsPostBack)
            {
                initial_msg();
                objdom.chargerDomaineInterventionOrganisation(drpddomai,idorg);
             
                objcatpost.chargerCategoriePost(drpdcateg);

                objcatpost.chargerCategoriePost(drpdcateg);
                objcatpost.afficherDetails("evenement", catpost);
                drpdcateg.SelectedValue = catpost.CategoriePostId.ToString();


                if (ViewState["Record"] == null)
                {//depense
                    dat.Columns.Add("#Code");
                    dat.Columns.Add("Domaine d'Intervention");

                    ViewState["Record"] = dat;
                }


                if (nsco == null)
                {
                    btnEnregistrer.InnerText = "Enregistrer";

                }
                else

                {
                    btnEnregistrer.InnerText = "Modifier";
                    AfficherArticle();
                }

            }
        }
        void EnvoieNotification()
        {
            // Après avoir sauvé la nouvelle question dans la base
            objMembre.AfficherDetails(ide, mem);
            long auteur = ide;
            string titreSujet = txttitre.Value;
            string contenu = txtresume.Value;
            string name = drpdtype.SelectedValue + ": " + nameorg + " a un nouveau post, titre: " + txttitre.Value;

            string statut = "envoyé";
            string liensujet = $"{HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority)}/events.aspx?name=" + co.GenerateSlug(txttitre.Value);

            Task.Run(() => objnotif.EnvoyerNotificationAction(auteur, titreSujet, contenu, liensujet, name, statut));


        }
        private void EnvoieEmailNotification()
        {
            string liensujet = $"{HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority)}/events.aspx?name=" + co.GenerateSlug(txttitre.Value);

            string imageUrl = Path.GetFileName(fileupd.FileName);
            string pathimageurl = "~/file/post/";
            string dateevent = txtdatepub.Value;
            string lieuevent = txtplace+","+ txtadresse.Value;
            string auteur = txtauteur.Value;
         
            //            string messageenvoye = $@"
            //<p><strong>Nouvel évènement publié par {nameorg}</strong></p>

            //<p><strong>Titre :</strong> {txttitre.Value}</p>

            //<p>
            //Nous avons le plaisir de vous informer qu’un nouvel évènement vient d’être publié par <strong>{nameorg}</strong> sur la plateforme Lincom.
            //</p>

            //<p>
            //👉 <a href='{liensujet}' style='color:#0d6efd;text-decoration:none;'>Cliquez ici pour consulter les détails de l’évènement</a>
            //</p>

            //<p>
            //Merci de rester connecté à la plateforme pour ne manquer aucune actualité importante.
            //</p>

            //<p style='margin-top:30px;'>— L’équipe Lincom</p>
            //";
            string messageenvoye = $@"
<div style='font-family:Arial,sans-serif; max-width:600px; margin:auto; background:#fff; border-radius:10px; overflow:hidden; box-shadow:0 0 10px rgba(0,0,0,0.1);'>
  <img src='{pathimageurl}{imageUrl}' alt='Événement' style='width:100%; height:auto; display:block;' />
  <div style='padding:20px;'>

    <h2 style='color:#0d6efd; font-size:22px; margin-top:0;'>{txttitre.Value}</h2>

    <p style='font-size:15px; color:#333; margin:10px 0 20px;'>Un nouvel évènement vient d’être publié par <strong>{nameorg}</strong> sur la plateforme Lincom.</p>

    <table style='width:100%; font-size:14px; color:#555; margin-bottom:20px;'>
      <tr>
        <td><strong>📅 Date :</strong></td>
        <td>{Convert.ToDateTime(dateevent).ToString("dd/MM/yyyy")}</td>
      </tr>
      <tr>
        <td><strong>📍 Lieu :</strong></td>
        <td>{lieuevent}</td>
      </tr>
      <tr>
        <td><strong>👤 Auteur :</strong></td>
        <td>{auteur}</td>
      </tr>
    </table>

    <div style='margin-bottom:20px;'>
      <a href='{liensujet}' style='background:#0d6efd; color:white; padding:10px 20px; text-decoration:none; border-radius:5px; font-weight:bold;'>Voir plus</a>
    </div>

    <p style='font-size:13px; color:#888;'>Merci de rester connecté à la plateforme Lincom pour ne manquer aucun événement.</p>
  </div>
</div>
";

            string sujet = txttitre.Value;
            long idenvoyeur = ide;

            Task.Run(() => co.EnvoyerEmailTousMembres(idenvoyeur, messageenvoye, sujet,0));
            //EmailService.EnvoyerEmail(user.Email, "Notification Lincom", "<p>Votre message...</p>", fournisseur);

        }
        private string Uploadoc(FileUpload fil)
        {
            if (fil.HasFile)
            {
                string fileName = Path.GetFileName(fil.FileName);
                string extension = Path.GetExtension(fileName).ToLower();

                string[] validExtensions = { ".pdf"};

                if (!validExtensions.Contains(extension))
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Le document doit avoir ces extensions : .pdf ";

                    return null;
                }

                if (fil.PostedFile.ContentLength > 104857600)
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Votre document est trop volumineux. Il doit faire moins de 100MB.";

                    return null;
                }

                string path = Server.MapPath("~/file/post/") + fileName;
                fil.SaveAs(path);
                imge = fileName;

            }
            else
            {
                if (nsco == null)
                {
                    imge = "No Doc";
                }
                else
                {
                    obj.AfficherDetails(Convert.ToInt64(nsco), idorg,0,pos);

                    imge = pos.pdf;


                }
            }
            return imge;

        }
        protected void doma_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = true;
            ajourdom.Visible = false;
        }
        void ajourdomainepost()
        {
            try
            {
                int dc = GridView1.Rows.Count;
                if (dc > 0)
                {
                    foreach (GridViewRow row in this.GridView1.Rows)
                    {
                        if (row.RowType == DataControlRowType.DataRow)
                        {
                            obj.AfficherDetailsname(co.GenerateSlug(txttitre.Value), idorg,0,pos);
                            index = (long)pos.PostId;

                            actco.PostId = index;
                            objdom.AfficherDetails(Convert.ToInt32(row.Cells[0].Text), domai, idorg,0);
                            actco.DomaineInterventionOrganisationId = Convert.ToInt32(domai.DomaineInterventionOrganisationId);
                            actco.DateCreation = DateTime.Now;
                            actco.statut = "actif";
                            actco.MembreId = ide;
                            actco.OrganisationId = idorg;


                            objactco.Ajouter(actco);

                        }
                    }

                }

            }
            catch (Exception ex)
            { // logguer ou afficher les erreurs pour le débogage
                System.Diagnostics.Debug.WriteLine("Erreur dans Domaines d'Intervention de l'organisation : " + ex.Message);

            }

        }
        protected void btnajoutdom_Click(object sender, EventArgs e)
        {
            try
            {
                if (drpddomai.SelectedValue == "-1" || drpddomai.SelectedValue == "0")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez votre domaine ')</script>");
                }
                else
                {
                    dom.Visible = true;
                    int i = GridView1.Rows.Count;
                    dat = (DataTable)ViewState["Record"];
                    dat.Rows.Add(drpddomai.SelectedValue, drpddomai.SelectedItem);
                    GridView1.DataSource = dat;
                    GridView1.DataBind();

                }

            }
            catch (Exception ec)
            {

            }
        }

        protected void btnvider_Click(object sender, EventArgs e)
        {
            dom.Visible = true;
            dat = (DataTable)ViewState["Record"];
            dat.Rows.Clear();
            GridView1.DataSource = dat;
            GridView1.DataBind();
        }
        public void remplissagedomainepost()
        {
            try
            {
                if (drpddomai.SelectedValue == "-1" || drpddomai.SelectedValue == "0")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez votre domaine ')</script>");
                }
                else
                {
                    dom.Visible = true;
                    int i = GridView1.Rows.Count;
                    dat = (DataTable)ViewState["Record"];
                    dat.Rows.Add(drpddomai.SelectedValue, drpddomai.SelectedItem);
                    GridView1.DataSource = dat;
                    GridView1.DataBind();

                }

            }
            catch (Exception ec)
            {

            }
        }
        protected void btnannuldom_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = true;
            dat = (DataTable)ViewState["Record"];
            dat.Rows.Clear();
            GridView1.DataSource = dat;
            GridView1.DataBind();
        }
        protected void ajoudom_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = false;
            ajourdom.Visible = true;
        }

        protected void btnajoutnouvdom_ServerClick(object sender, EventArgs e)
        {
          //  ad();
        }

        protected void drpddomai_SelectedIndexChanged(object sender, EventArgs e)
        {
            dom.Visible = true;
            remplissagedomainepost();
        }
        protected void btnajourdom_ServerClick(object sender, EventArgs e)
        {
            remplissagedomainepost();
        }
       
        public void ViderChamps()
        {
            // Réinitialiser les champs texte
            txttitre.Value = string.Empty;
            txtdescription.Value = string.Empty;
            txtresume.Value = string.Empty;
            txtauteur.Value = string.Empty;
            txtdatepub.Value = string.Empty;
            txttempsdebut.Value = string.Empty;
            txtduree.Value = string.Empty;
            txtplace.Value = string.Empty;
            txtpersparticipe.Value = string.Empty;
            txtlangue.Value = string.Empty;
            txtlien.Value = string.Empty;
            
            drpdstatut.SelectedValue = "-1";
            drpdetat.SelectedValue = "-1";
            drpdtype.SelectedValue="-1";
            dom.Visible = false;

            // Réinitialiser les FileUpload (pas possible directement, nécessiterait un rechargement de la page)
            // fileupd et fileupd1 : rechargement automatique du formulaire requis pour les vider.
            // fileupdoc : même chose.

            // Cacher les messages

        }
         
       

        private string UploadImage(FileUpload fil,int cd)
        {
            if (fil.HasFile)
            {
                string fileName = Path.GetFileName(fil.FileName);
                string extension = Path.GetExtension(fileName).ToLower();

                string[] validExtensions = { ".png", ".jpeg", ".gif", ".jpg", ".jfif" };

                if (!validExtensions.Contains(extension))
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "La photo doit avoir ces extensions : .png, .jpg, .gif, .jpeg, .jfif. ";

                    return null;
                }

                if (fil.PostedFile.ContentLength > 104857600)
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Votre photo est trop volumineuse. Elle doit faire moins de 100MB.";

                    return null;
                }

                string path = Server.MapPath("~/file/post/") + fileName;
                fil.SaveAs(path);
                imge = fileName;

            }
            else
            {
                if (nsco == null)
                {
                    imge = "No Image";
                }
                else
                {

                    obj.AfficherDetails(Convert.ToInt64(nsco), idorg, 0, pos);

                    if (cd == 0)
                    { imge = pos.photo; }
                    else if (cd == 1)
                    { imge = pos.video; }

                }
            }
            return imge;

        }

        public void AjoutPost()
        {
            try
            { // Vérification des champs requis
                if (string.IsNullOrWhiteSpace(txttitre.Value) ||
                    string.IsNullOrWhiteSpace(txtdescription.Value) ||
                    string.IsNullOrWhiteSpace(txtresume.Value) ||
                    string.IsNullOrWhiteSpace(txtauteur.Value) ||
                    string.IsNullOrWhiteSpace(txtdatepub.Value) ||
                    drpdcateg.SelectedValue == "-1" ||
                    drpdstatut.SelectedValue == "-1" ||
                    drpdetat.SelectedValue == "-1")
                {
                    div_msg_succes.Visible = false;
                    div_msg_error.Visible = true;
                    msg_error.Text = "Veuillez renseigner tous les champs obligatoires.";
                    return;
                }

               
                        p.Titre = txttitre.Value;
                        p.Contenu = txtdescription.Value;
                        p.CategoriePostId =Convert.ToInt32(drpdcateg.SelectedValue);
                        p.MembreId = ide;
                        p.DatePublication =txtdatepub.Value;
                        p.summery = txtresume.Value;

                        p.author = txtauteur.Value;
                        p.photo = UploadImage(fileupd,0);
                        p.OrganisationId = idorg;

                        p.video = UploadImage(fileupd1,1);
                        p.number_of_view =0;
                        p.like = 0;
                        p.dislike = 0;
                        p.starttime = txttempsdebut.Value;
                        p.eventduration = txtduree.Value;
                        p.eventplace = txtplace.Value;
                        p.whoattend = txtpersparticipe.Value;
                        p.qualificationattend = "";
                        p.langueevent = txtlangue.Value;
                        p.externevent =drpdtype.SelectedItem.ToString();
                        p.MOIS = Convert.ToDateTime(txtdatepub.Value).Month.ToString();
                        p.ANNEE = Convert.ToDateTime(txtdatepub.Value).Year.ToString();
                        p.lien_isncription = txtlien.Value;
                        p.pdf = Uploadoc(fileupdoc);
                        p.name = co.GenerateSlug(txttitre.Value);
                        p.DateCreation = DateTime.Now;
                        p.DateModification = DateTime.Now;
                        p.EstPublie = drpdstatut.SelectedValue;
                        p.EstPublieEvent = drpdetat.SelectedValue;
                        p.statut = co.GenerateSlug(drpdcateg.SelectedItem.ToString());
                        p.etat = drpdtype.SelectedValue;

                        info = obj.Ajout(p);
                        if (info == 1)
                        {
                    ajourdomainepost();
                    EnvoieNotification();
                    EnvoieEmailNotification();
                    div_msg_succes.Visible = true;
                    div_msg_error.Visible = false;
                    msg_succes.Text = "Post enregistré avec succès.";

                    ViderChamps();

                        }
                        else
                        {
                            div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.Text = "Ce Village Existe deja";

                        }
                   

            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.Text = "Cette Province Existe deja";

            }

            //LabelMsg.Text = obj.ajouterProvince(prov).ToString();
        }

        public void upte()
        {
            try
            { // Vérification des champs requis
                if (string.IsNullOrWhiteSpace(txttitre.Value) ||
                    string.IsNullOrWhiteSpace(txtdescription.Value) ||
                    string.IsNullOrWhiteSpace(txtresume.Value) ||
                    string.IsNullOrWhiteSpace(txtauteur.Value) ||
                    string.IsNullOrWhiteSpace(txtdatepub.Value) ||
                    drpdcateg.SelectedValue == "-1" ||
                    drpdstatut.SelectedValue == "-1" ||
                    drpdetat.SelectedValue == "-1")
                {
                    div_msg_succes.Visible = false;
                    div_msg_error.Visible = true;
                    msg_error.Text = "Veuillez renseigner tous les champs obligatoires.";
                    return;
                }


                p.Titre = txttitre.Value;
                p.Contenu = txtdescription.Value;
                p.CategoriePostId = Convert.ToInt32(drpdcateg.SelectedValue);
              
                p.DatePublication = txtdatepub.Value;
                p.summery = txtresume.Value;
                p.author = txtauteur.Value;
                p.photo = UploadImage(fileupd, 0);

                p.video = UploadImage(fileupd1, 1);
               
                p.starttime = txttempsdebut.Value;
                p.eventduration = txtduree.Value;
                p.eventplace = txtplace.Value;
                p.whoattend = txtpersparticipe.Value;
                p.externevent = drpdtype.SelectedItem.ToString();
                p.langueevent = txtlangue.Value;
               
                p.MOIS = Convert.ToDateTime(txtdatepub.Value).Month.ToString();
                p.ANNEE = Convert.ToDateTime(txtdatepub.Value).Year.ToString();
                p.lien_isncription = txtlien.Value;
                p.pdf = Uploadoc(fileupdoc);
                p.name = co.GenerateSlug(txttitre.Value);
               
                p.DateModification = DateTime.Now;
                p.EstPublie = drpdstatut.SelectedValue;
                p.EstPublieEvent = drpdetat.SelectedValue;
                p.statut = co.GenerateSlug(drpdcateg.SelectedItem.ToString());
                p.etat = drpdtype.SelectedValue;

                obj.AfficherDetails(Convert.ToInt64(nsco), idorg, 0, pos);

                info = obj.edit(p,Convert.ToInt64(pos.PostId),idorg);
                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    div_msg_error.Visible = false;
                    msg_succes.Text = "Post modifié avec succès.";

                    ViderChamps();

                }
                else
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.Text = "Ce Post Existe deja";

                }


            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.Text = "Ce Post Existe deja";

            }

        }
        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
           
        }
        public void AfficherArticle()
        {
          if (nsco!=null)
            {
                obj.AfficherDetails(Convert.ToInt64(nsco), idorg, 0, p);


            txttitre.Value = p.Titre;
            txtdescription.Value = p.Contenu;
            drpdcateg.SelectedValue = p.CategoriePostId.ToString();

            txtdatepub.Value = p.DatePublication.ToString();
            txtresume.Value = p.summery;
            txtauteur.Value = p.author;

            // Les fichiers (photo, vidéo, PDF) ne peuvent pas être rechargés automatiquement pour des raisons de sécurité
            // Si tu veux afficher des aperçus, utilise des balises <img>, <video> ou <a> côté client avec les URL sauvegardées

            txttempsdebut.Value = p.starttime;
            txtduree.Value = p.eventduration;
            txtplace.Value = p.eventplace;
            txtpersparticipe.Value = p.whoattend;

            txtlangue.Value = p.langueevent;
                drpdtype.SelectedValue = p.etat;
            txtlien.Value = p.lien_isncription;

            // Définir les valeurs des DropDownList si présentes
            if (drpdstatut.Items.FindByValue(p.EstPublie) != null)
                drpdstatut.SelectedValue = p.EstPublie;

            if (drpdetat.Items.FindByValue(p.EstPublieEvent) != null)
                drpdetat.SelectedValue = p.EstPublieEvent;

           
            }
        }

        protected void btnEnregistrer_ServerClick(object sender, EventArgs e)
        {
            // Vérifier la validation côté serveur
            if (!Page.IsValid)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Veuillez corriger les erreurs avant de continuer.";
                return;
            }

            if (nsco == null)
            {
                AjoutPost();
            }
            else
            {
                upte();
            }
        }
        protected void btn_enreg_Click(object sender, EventArgs e)
        {
            if (nsco == null)
            {
                AjoutPost();

            }
            else
                upte();

        }
    }
}