{"version": 3, "sources": ["../../js/src/dom/selector-engine.js", "../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/base-component.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js"], "names": ["NODE_TEXT", "SelectorEngine", "find", "selector", "element", "document", "documentElement", "concat", "Element", "prototype", "querySelectorAll", "call", "findOne", "querySelector", "children", "filter", "child", "matches", "parents", "ancestor", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "toString", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "getElementById", "getSelector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "getElementFromSelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "j<PERSON>y", "getElement", "length", "emulateTransitionEnd", "duration", "called", "emulatedDuration", "addEventListener", "listener", "removeEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "style", "elementStyle", "parentNodeStyle", "display", "visibility", "isDisabled", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "onDOMContentLoaded", "callback", "readyState", "isRTL", "dir", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "execute", "elementMap", "Map", "Data", "set", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "remove", "delete", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "customEventsRegex", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "handler", "event", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "target", "this", "i", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "getTypeEvent", "add<PERSON><PERSON><PERSON>", "wrapFn", "relatedTarget", "handlers", "previousFn", "replace", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "on", "one", "inNamespace", "isNamespace", "elementEvent", "slice", "keyHandlers", "trigger", "args", "isNative", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "VERSION", "BaseComponent", "constructor", "_element", "DATA_KEY", "dispose", "EVENT_KEY", "getOwnPropertyNames", "propertyName", "_queueCallback", "isAnimated", "[object Object]", "Error", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASS_NAME_ALERT", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "<PERSON><PERSON>", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "closest", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "each", "data", "alertInstance", "handle<PERSON><PERSON><PERSON>", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_END", "CLASS_NAME_START", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_INDICATOR", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "POINTER_TYPE_TOUCH", "POINTER_TYPE_PEN", "Carousel", "super", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "order", "_handleSwipe", "absDeltax", "abs", "direction", "_keydown", "_addTouchEventListeners", "start", "pointerType", "touches", "clientX", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "indexOf", "_getItemByOrder", "activeElement", "isNext", "isPrev", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "eventDirectionName", "targetIndex", "fromIndex", "_setActiveIndicatorElement", "activeIndicator", "indicators", "parseInt", "elementInterval", "defaultInterval", "directionOrOrder", "_directionToOrder", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "_orderToDirection", "triggerSlidEvent", "completeCallBack", "action", "ride", "carouselInterface", "slideIndex", "dataApiClickHandler", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "id", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPEND", "CLASS_NAME_DROPSTART", "CLASS_NAME_NAVBAR", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "boundary", "reference", "popperConfig", "autoClose", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "getParentFromElement", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "isDisplayStatic", "modifiers", "modifier", "enabled", "createPopper", "focus", "_completeHide", "destroy", "update", "_getPlacement", "parentDropdown", "isEnd", "getPropertyValue", "_getOffset", "map", "popperData", "defaultBsPopperConfig", "placement", "options", "_selectMenuItem", "items", "dropdownInterface", "toggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "clickEvent", "isActive", "stopPropagation", "getToggleButton", "clearMenus", "getInstance", "click", "dataApiKeydownHandler", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "getWidth", "documentWidth", "clientWidth", "innerWidth", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "actualValue", "overflow", "styleProp", "scrollbarWidth", "reset", "_resetElementAttributes", "removeProperty", "clickCallback", "CLASS_NAME_BACKDROP", "EVENT_MOUSEDOWN", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "className", "append<PERSON><PERSON><PERSON>", "backdropTransitionDuration", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_isShown", "_ignoreBackdropClick", "_isAnimated", "showEvent", "scrollBarHide", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "_enforceFocus", "_triggerBackdropTransition", "_resetAdjustments", "scrollBarReset", "currentTarget", "isModalOverflowing", "scrollHeight", "clientHeight", "overflowY", "modalTransitionDuration", "getScrollBarWidth", "isBodyOverflowing", "paddingLeft", "paddingRight", "scroll", "OPEN_SELECTOR", "<PERSON><PERSON><PERSON>", "_enforceFocusOnElement", "blur", "undefined", "allReadyOpen", "el", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "DefaultAllowlist", "*", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacements", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "updateAttachment", "dataKey", "_getDelegateConfig", "phase", "_handlePopperPlacementChange", "onFirstUpdate", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "tabClass", "token", "tClass", "state", "popper", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "hideEvent", "complete", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "EVENT_FOCUSOUT", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "_onInteraction", "isInteracting"], "mappings": ";;;;;sCAaA,MAAMA,UAAY,EAEZC,eAAiB,CACrBC,KAAI,CAACC,EAAUC,EAAUC,SAASC,kBACzB,GAAGC,UAAUC,QAAQC,UAAUC,iBAAiBC,KAAKP,EAASD,IAGvES,QAAO,CAACT,EAAUC,EAAUC,SAASC,kBAC5BE,QAAQC,UAAUI,cAAcF,KAAKP,EAASD,GAGvDW,SAAQ,CAACV,EAASD,IACT,GAAGI,UAAUH,EAAQU,UACzBC,OAAOC,GAASA,EAAMC,QAAQd,IAGnCe,QAAQd,EAASD,GACf,MAAMe,EAAU,GAEhB,IAAIC,EAAWf,EAAQgB,WAEvB,KAAOD,GAAYA,EAASE,WAAaC,KAAKC,cArBhC,IAqBgDJ,EAASE,UACjEF,EAASF,QAAQd,IACnBe,EAAQM,KAAKL,GAGfA,EAAWA,EAASC,WAGtB,OAAOF,GAGTO,KAAKrB,EAASD,GACZ,IAAIuB,EAAWtB,EAAQuB,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAAST,QAAQd,GACnB,MAAO,CAACuB,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAAKxB,EAASD,GACZ,IAAIyB,EAAOxB,EAAQyB,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKX,QAAQd,GACf,MAAO,CAACyB,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KC7DLC,QAAU,IACVC,wBAA0B,IAC1BC,eAAiB,gBAGjBC,OAASC,GACTA,MAAAA,EACM,GAAEA,EAGL,GAAGC,SAASxB,KAAKuB,GAAKE,MAAM,eAAe,GAAGC,cASjDC,OAASC,IACb,GACEA,GAAUC,KAAKC,MArBH,IAqBSD,KAAKE,gBACnBrC,SAASsC,eAAeJ,IAEjC,OAAOA,GAGHK,YAAcxC,IAClB,IAAID,EAAWC,EAAQyC,aAAa,kBAEpC,IAAK1C,GAAyB,MAAbA,EAAkB,CACjC,IAAI2C,EAAW1C,EAAQyC,aAAa,QAMpC,IAAKC,IAAcA,EAASC,SAAS,OAASD,EAASE,WAAW,KAChE,OAAO,KAILF,EAASC,SAAS,OAASD,EAASE,WAAW,OACjDF,EAAY,IAAGA,EAASG,MAAM,KAAK,IAGrC9C,EAAW2C,GAAyB,MAAbA,EAAmBA,EAASI,OAAS,KAG9D,OAAO/C,GAGHgD,uBAAyB/C,IAC7B,MAAMD,EAAWyC,YAAYxC,GAE7B,OAAID,GACKE,SAASQ,cAAcV,GAAYA,EAGrC,MAGHiD,uBAAyBhD,IAC7B,MAAMD,EAAWyC,YAAYxC,GAE7B,OAAOD,EAAWE,SAASQ,cAAcV,GAAY,MAGjDkD,iCAAmCjD,IACvC,IAAKA,EACH,OAAO,EAIT,IAAIkD,mBAAEA,EAAFC,gBAAsBA,GAAoBC,OAAOC,iBAAiBrD,GAEtE,MAAMsD,EAA0BC,OAAOC,WAAWN,GAC5CO,EAAuBF,OAAOC,WAAWL,GAG/C,OAAKG,GAA4BG,GAKjCP,EAAqBA,EAAmBL,MAAM,KAAK,GACnDM,EAAkBA,EAAgBN,MAAM,KAAK,GArFf,KAuFtBU,OAAOC,WAAWN,GAAsBK,OAAOC,WAAWL,KAPzD,GAULO,qBAAuB1D,IAC3BA,EAAQ2D,cAAc,IAAIC,MAAMhC,kBAG5BiC,UAAY/B,MACXA,GAAsB,iBAARA,UAIO,IAAfA,EAAIgC,SACbhC,EAAMA,EAAI,SAGmB,IAAjBA,EAAIb,UAGd8C,WAAajC,GACb+B,UAAU/B,GACLA,EAAIgC,OAAShC,EAAI,GAAKA,EAGZ,iBAARA,GAAoBA,EAAIkC,OAAS,EACnCnE,eAAeW,QAAQsB,GAGzB,KAGHmC,qBAAuB,CAACjE,EAASkE,KACrC,IAAIC,GAAS,EACb,MACMC,EAAmBF,EADD,EAQxBlE,EAAQqE,iBAAiBzC,gBALzB,SAAS0C,IACPH,GAAS,EACTnE,EAAQuE,oBAAoB3C,eAAgB0C,MAI9CE,WAAW,KACJL,GACHT,qBAAqB1D,IAEtBoE,IAGCK,gBAAkB,CAACC,EAAeC,EAAQC,KAC9CC,OAAOC,KAAKF,GAAaG,QAAQC,IAC/B,MAAMC,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASrB,UAAUqB,GAAS,UAvI5CpD,OADSA,EAwIsDoD,GAtIzD,GAAEpD,EAGL,GAAGC,SAASxB,KAAKuB,GAAKE,MAAM,eAAe,GAAGC,cALxCH,IAAAA,EA0IX,IAAK,IAAIsD,OAAOH,GAAeI,KAAKF,GAClC,MAAM,IAAIG,UACP,GAAEZ,EAAca,0BAA0BP,qBAA4BG,yBAAiCF,UAM1GO,UAAYxF,IAChB,IAAKA,EACH,OAAO,EAGT,GAAIA,EAAQyF,OAASzF,EAAQgB,YAAchB,EAAQgB,WAAWyE,MAAO,CACnE,MAAMC,EAAerC,iBAAiBrD,GAChC2F,EAAkBtC,iBAAiBrD,EAAQgB,YAEjD,MAAgC,SAAzB0E,EAAaE,SACU,SAA5BD,EAAgBC,SACY,WAA5BF,EAAaG,WAGjB,OAAO,GAGHC,WAAa9F,IACZA,GAAWA,EAAQiB,WAAaC,KAAKC,gBAItCnB,EAAQ+F,UAAUC,SAAS,mBAIC,IAArBhG,EAAQiG,SACVjG,EAAQiG,SAGVjG,EAAQkG,aAAa,aAAoD,UAArClG,EAAQyC,aAAa,aAG5D0D,eAAiBnG,IACrB,IAAKC,SAASC,gBAAgBkG,aAC5B,OAAO,KAIT,GAAmC,mBAAxBpG,EAAQqG,YAA4B,CAC7C,MAAMC,EAAOtG,EAAQqG,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAItG,aAAmBuG,WACdvG,EAIJA,EAAQgB,WAINmF,eAAenG,EAAQgB,YAHrB,MAMLwF,KAAO,OAEPC,OAASzG,GAAWA,EAAQ0G,aAE5BC,UAAY,KAChB,MAAMC,OAAEA,GAAWxD,OAEnB,OAAIwD,IAAW3G,SAAS4G,KAAKX,aAAa,qBACjCU,EAGF,MAGHE,mBAAqBC,IACG,YAAxB9G,SAAS+G,WACX/G,SAASoE,iBAAiB,mBAAoB0C,GAE9CA,KAIEE,MAAQ,IAAuC,QAAjChH,SAASC,gBAAgBgH,IAEvCC,mBAAqBC,IAVAL,IAAAA,EAAAA,EAWN,KACjB,MAAMM,EAAIV,YAEV,GAAIU,EAAG,CACL,MAAMC,EAAOF,EAAOG,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQF,EAAOM,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcP,EACzBC,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNJ,EAAOM,mBApBQ,YAAxBzH,SAAS+G,WACX/G,SAASoE,iBAAiB,mBAAoB0C,GAE9CA,KAuBEc,QAAUd,IACU,mBAAbA,GACTA,KCtPEe,WAAa,IAAIC,IAEvB,IAAAC,KAAe,CACbC,IAAIjI,EAASkI,EAAKC,GACXL,WAAWM,IAAIpI,IAClB8H,WAAWG,IAAIjI,EAAS,IAAI+H,KAG9B,MAAMM,EAAcP,WAAWQ,IAAItI,GAI9BqI,EAAYD,IAAIF,IAA6B,IAArBG,EAAYE,KAMzCF,EAAYJ,IAAIC,EAAKC,GAJnBK,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKN,EAAYvD,QAAQ,QAOhIwD,IAAG,CAACtI,EAASkI,IACPJ,WAAWM,IAAIpI,IACV8H,WAAWQ,IAAItI,GAASsI,IAAIJ,IAG9B,KAGTU,OAAO5I,EAASkI,GACd,IAAKJ,WAAWM,IAAIpI,GAClB,OAGF,MAAMqI,EAAcP,WAAWQ,IAAItI,GAEnCqI,EAAYQ,OAAOX,GAGM,IAArBG,EAAYE,MACdT,WAAWe,OAAO7I,KCtCxB,MAAM8I,eAAiB,qBACjBC,eAAiB,OACjBC,cAAgB,SAChBC,cAAgB,GACtB,IAAIC,SAAW,EACf,MAAMC,aAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,kBAAoB,4BACpBC,aAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WASF,SAASC,YAAYzJ,EAAS0J,GAC5B,OAAQA,GAAQ,GAAEA,MAAQR,cAAiBlJ,EAAQkJ,UAAYA,WAGjE,SAASS,SAAS3J,GAChB,MAAM0J,EAAMD,YAAYzJ,GAKxB,OAHAA,EAAQkJ,SAAWQ,EACnBT,cAAcS,GAAOT,cAAcS,IAAQ,GAEpCT,cAAcS,GAGvB,SAASE,iBAAiB5J,EAASyH,GACjC,OAAO,SAASoC,EAAQC,GAOtB,OANAA,EAAMC,eAAiB/J,EAEnB6J,EAAQG,QACVC,aAAaC,IAAIlK,EAAS8J,EAAMK,KAAM1C,GAGjCA,EAAG2C,MAAMpK,EAAS,CAAC8J,KAI9B,SAASO,2BAA2BrK,EAASD,EAAU0H,GACrD,OAAO,SAASoC,EAAQC,GACtB,MAAMQ,EAActK,EAAQM,iBAAiBP,GAE7C,IAAK,IAAIwK,OAAEA,GAAWT,EAAOS,GAAUA,IAAWC,KAAMD,EAASA,EAAOvJ,WACtE,IAAK,IAAIyJ,EAAIH,EAAYtG,OAAQyG,KAC/B,GAAIH,EAAYG,KAAOF,EAQrB,OAPAT,EAAMC,eAAiBQ,EAEnBV,EAAQG,QAEVC,aAAaC,IAAIlK,EAAS8J,EAAMK,KAAMpK,EAAU0H,GAG3CA,EAAG2C,MAAMG,EAAQ,CAACT,IAM/B,OAAO,MAIX,SAASY,YAAYC,EAAQd,EAASe,EAAqB,MACzD,MAAMC,EAAehG,OAAOC,KAAK6F,GAEjC,IAAK,IAAIF,EAAI,EAAGK,EAAMD,EAAa7G,OAAQyG,EAAIK,EAAKL,IAAK,CACvD,MAAMX,EAAQa,EAAOE,EAAaJ,IAElC,GAAIX,EAAMiB,kBAAoBlB,GAAWC,EAAMc,qBAAuBA,EACpE,OAAOd,EAIX,OAAO,KAGT,SAASkB,gBAAgBC,EAAmBpB,EAASqB,GACnD,MAAMC,EAAgC,iBAAZtB,EACpBkB,EAAkBI,EAAaD,EAAerB,EAEpD,IAAIuB,EAAYC,aAAaJ,GAO7B,OANiB1B,aAAanB,IAAIgD,KAGhCA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASE,WAAWtL,EAASiL,EAAmBpB,EAASqB,EAAclB,GACrE,GAAiC,iBAAtBiB,IAAmCjL,EAC5C,OAUF,GAPK6J,IACHA,EAAUqB,EACVA,EAAe,MAKb5B,kBAAkBjE,KAAK4F,GAAoB,CAC7C,MAAMM,EAAS9D,GACN,SAAUqC,GACf,IAAKA,EAAM0B,eAAkB1B,EAAM0B,gBAAkB1B,EAAMC,iBAAmBD,EAAMC,eAAe/D,SAAS8D,EAAM0B,eAChH,OAAO/D,EAAGlH,KAAKiK,KAAMV,IAKvBoB,EACFA,EAAeK,EAAOL,GAEtBrB,EAAU0B,EAAO1B,GAIrB,MAAOsB,EAAYJ,EAAiBK,GAAaJ,gBAAgBC,EAAmBpB,EAASqB,GACvFP,EAAShB,SAAS3J,GAClByL,EAAWd,EAAOS,KAAeT,EAAOS,GAAa,IACrDM,EAAahB,YAAYe,EAAUV,EAAiBI,EAAatB,EAAU,MAEjF,GAAI6B,EAGF,YAFAA,EAAW1B,OAAS0B,EAAW1B,QAAUA,GAK3C,MAAMN,EAAMD,YAAYsB,EAAiBE,EAAkBU,QAAQ7C,eAAgB,KAC7ErB,EAAK0D,EACTd,2BAA2BrK,EAAS6J,EAASqB,GAC7CtB,iBAAiB5J,EAAS6J,GAE5BpC,EAAGmD,mBAAqBO,EAAatB,EAAU,KAC/CpC,EAAGsD,gBAAkBA,EACrBtD,EAAGuC,OAASA,EACZvC,EAAGyB,SAAWQ,EACd+B,EAAS/B,GAAOjC,EAEhBzH,EAAQqE,iBAAiB+G,EAAW3D,EAAI0D,GAG1C,SAASS,cAAc5L,EAAS2K,EAAQS,EAAWvB,EAASe,GAC1D,MAAMnD,EAAKiD,YAAYC,EAAOS,GAAYvB,EAASe,GAE9CnD,IAILzH,EAAQuE,oBAAoB6G,EAAW3D,EAAIoE,QAAQjB,WAC5CD,EAAOS,GAAW3D,EAAGyB,WAG9B,SAAS4C,yBAAyB9L,EAAS2K,EAAQS,EAAWW,GAC5D,MAAMC,EAAoBrB,EAAOS,IAAc,GAE/CvG,OAAOC,KAAKkH,GAAmBjH,QAAQkH,IACrC,GAAIA,EAAWtJ,SAASoJ,GAAY,CAClC,MAAMjC,EAAQkC,EAAkBC,GAEhCL,cAAc5L,EAAS2K,EAAQS,EAAWtB,EAAMiB,gBAAiBjB,EAAMc,uBAK7E,SAASS,aAAavB,GAGpB,OADAA,EAAQA,EAAM6B,QAAQ5C,eAAgB,IAC/BI,aAAaW,IAAUA,EAGhC,MAAMG,aAAe,CACnBiC,GAAGlM,EAAS8J,EAAOD,EAASqB,GAC1BI,WAAWtL,EAAS8J,EAAOD,EAASqB,GAAc,IAGpDiB,IAAInM,EAAS8J,EAAOD,EAASqB,GAC3BI,WAAWtL,EAAS8J,EAAOD,EAASqB,GAAc,IAGpDhB,IAAIlK,EAASiL,EAAmBpB,EAASqB,GACvC,GAAiC,iBAAtBD,IAAmCjL,EAC5C,OAGF,MAAOmL,EAAYJ,EAAiBK,GAAaJ,gBAAgBC,EAAmBpB,EAASqB,GACvFkB,EAAchB,IAAcH,EAC5BN,EAAShB,SAAS3J,GAClBqM,EAAcpB,EAAkBrI,WAAW,KAEjD,QAA+B,IAApBmI,EAAiC,CAE1C,IAAKJ,IAAWA,EAAOS,GACrB,OAIF,YADAQ,cAAc5L,EAAS2K,EAAQS,EAAWL,EAAiBI,EAAatB,EAAU,MAIhFwC,GACFxH,OAAOC,KAAK6F,GAAQ5F,QAAQuH,IAC1BR,yBAAyB9L,EAAS2K,EAAQ2B,EAAcrB,EAAkBsB,MAAM,MAIpF,MAAMP,EAAoBrB,EAAOS,IAAc,GAC/CvG,OAAOC,KAAKkH,GAAmBjH,QAAQyH,IACrC,MAAMP,EAAaO,EAAYb,QAAQ3C,cAAe,IAEtD,IAAKoD,GAAenB,EAAkBtI,SAASsJ,GAAa,CAC1D,MAAMnC,EAAQkC,EAAkBQ,GAEhCZ,cAAc5L,EAAS2K,EAAQS,EAAWtB,EAAMiB,gBAAiBjB,EAAMc,wBAK7E6B,QAAQzM,EAAS8J,EAAO4C,GACtB,GAAqB,iBAAV5C,IAAuB9J,EAChC,OAAO,KAGT,MAAMqH,EAAIV,YACJyE,EAAYC,aAAavB,GACzBsC,EAActC,IAAUsB,EACxBuB,EAAWpD,aAAanB,IAAIgD,GAElC,IAAIwB,EACAC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CIZ,GAAe/E,IACjBuF,EAAcvF,EAAEzD,MAAMkG,EAAO4C,GAE7BrF,EAAErH,GAASyM,QAAQG,GACnBC,GAAWD,EAAYK,uBACvBH,GAAkBF,EAAYM,gCAC9BH,EAAmBH,EAAYO,sBAG7BR,GACFK,EAAM/M,SAASmN,YAAY,cAC3BJ,EAAIK,UAAUjC,EAAWyB,GAAS,IAElCG,EAAM,IAAIM,YAAYxD,EAAO,CAC3B+C,QAAAA,EACAU,YAAY,SAKI,IAATb,GACT7H,OAAOC,KAAK4H,GAAM3H,QAAQmD,IACxBrD,OAAO2I,eAAeR,EAAK9E,EAAK,CAC9BI,IAAG,IACMoE,EAAKxE,OAMhB6E,GACFC,EAAIS,iBAGFX,GACF9M,EAAQ2D,cAAcqJ,GAGpBA,EAAID,uBAA2C,IAAhBH,GACjCA,EAAYa,iBAGPT,IClULU,QAAU,QAEhB,MAAMC,cACJC,YAAY5N,IACVA,EAAU+D,WAAW/D,MAMrBwK,KAAKqD,SAAW7N,EAChBgI,KAAKC,IAAIuC,KAAKqD,SAAUrD,KAAKoD,YAAYE,SAAUtD,OAGrDuD,UACE/F,KAAKY,OAAO4B,KAAKqD,SAAUrD,KAAKoD,YAAYE,UAC5C7D,aAAaC,IAAIM,KAAKqD,SAAUrD,KAAKoD,YAAYI,WAEjDnJ,OAAOoJ,oBAAoBzD,MAAMzF,QAAQmJ,IACvC1D,KAAK0D,GAAgB,OAIzBC,eAAepH,EAAU/G,EAASoO,GAAa,GAC7C,IAAKA,EAEH,YADAvG,QAAQd,GAIV,MAAM7D,EAAqBD,iCAAiCjD,GAC5DiK,aAAakC,IAAInM,EAAS,gBAAiB,IAAM6H,QAAQd,IAEzD9C,qBAAqBjE,EAASkD,GAKdmL,mBAACrO,GACjB,OAAOgI,KAAKM,IAAItI,EAASwK,KAAKsD,UAGdJ,qBAChB,MA1CY,QA6CCnG,kBACb,MAAM,IAAI+G,MAAM,uEAGCR,sBACjB,MAAQ,MAAKtD,KAAKjD,KAGAyG,uBAClB,MAAQ,IAAGxD,KAAKsD,UCvDpB,MAAMvG,OAAO,QACPuG,WAAW,WACXE,YAAa,YACbO,eAAe,YAEfC,iBAAmB,4BAEnBC,YAAe,iBACfC,aAAgB,kBAChBC,uBAAwB,0BAExBC,iBAAmB,QACnBC,kBAAkB,OAClBC,kBAAkB,OAQxB,MAAMC,cAAcpB,cAGHpG,kBACb,OAAOA,OAKTyH,MAAMhP,GACJ,MAAMiP,EAAcjP,EAAUwK,KAAK0E,gBAAgBlP,GAAWwK,KAAKqD,SAC7DsB,EAAc3E,KAAK4E,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAYpC,kBAIxCvC,KAAK6E,eAAeJ,GAKtBC,gBAAgBlP,GACd,OAAOgD,uBAAuBhD,IAAYA,EAAQsP,QAAS,UAG7DF,mBAAmBpP,GACjB,OAAOiK,aAAawC,QAAQzM,EAASyO,aAGvCY,eAAerP,GACbA,EAAQ+F,UAAU6C,OAvCE,QAyCpB,MAAMwF,EAAapO,EAAQ+F,UAAUC,SA1CjB,QA2CpBwE,KAAK2D,eAAe,IAAM3D,KAAK+E,gBAAgBvP,GAAUA,EAASoO,GAGpEmB,gBAAgBvP,GACVA,EAAQgB,YACVhB,EAAQgB,WAAWwO,YAAYxP,GAGjCiK,aAAawC,QAAQzM,EAAS0O,cAKVL,uBAAC1J,GACrB,OAAO6F,KAAKiF,MAAK,WACf,IAAIC,EAAO1H,KAAKM,IAAIkC,KArET,YAuENkF,IACHA,EAAO,IAAIX,MAAMvE,OAGJ,UAAX7F,GACF+K,EAAK/K,GAAQ6F,SAKC6D,qBAACsB,GACnB,OAAO,SAAU7F,GACXA,GACFA,EAAM2D,iBAGRkC,EAAcX,MAAMxE,QAW1BP,aAAaiC,GAAGjM,SAAU0O,uBAAsBH,iBAAkBO,MAAMa,cAAc,IAAIb,QAS1F5H,mBAAmB4H,OC/GnB,MAAMxH,OAAO,SACPuG,WAAW,YACXE,YAAa,aACbO,eAAe,YAEfsB,oBAAoB,SAEpBC,uBAAuB,4BAEvBnB,uBAAwB,2BAQ9B,MAAMoB,eAAepC,cAGJpG,kBACb,OAAOA,OAKTyI,SAEExF,KAAKqD,SAASoC,aAAa,eAAgBzF,KAAKqD,SAAS9H,UAAUiK,OAvB7C,WA4BF3B,uBAAC1J,GACrB,OAAO6F,KAAKiF,MAAK,WACf,IAAIC,EAAO1H,KAAKM,IAAIkC,KAAMsD,YAErB4B,IACHA,EAAO,IAAIK,OAAOvF,OAGL,WAAX7F,GACF+K,EAAK/K,SCrDb,SAASuL,cAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQ5M,OAAO4M,GAAKpO,WACfwB,OAAO4M,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASC,iBAAiBlI,GACxB,OAAOA,EAAIyD,QAAQ,SAAU0E,GAAQ,IAAGA,EAAIpO,eD4C9CgI,aAAaiC,GAAGjM,SAAU0O,uBAAsBmB,uBAAsBhG,IACpEA,EAAM2D,iBAEN,MAAM6C,EAASxG,EAAMS,OAAO+E,QAAQQ,wBAEpC,IAAIJ,EAAO1H,KAAKM,IAAIgI,EAAQxC,YACvB4B,IACHA,EAAO,IAAIK,OAAOO,IAGpBZ,EAAKM,WAUP7I,mBAAmB4I,QC7DnB,MAAMQ,YAAc,CAClBC,iBAAiBxQ,EAASkI,EAAKhD,GAC7BlF,EAAQiQ,aAAc,WAAUG,iBAAiBlI,GAAQhD,IAG3DuL,oBAAoBzQ,EAASkI,GAC3BlI,EAAQ0Q,gBAAiB,WAAUN,iBAAiBlI,KAGtDyI,kBAAkB3Q,GAChB,IAAKA,EACH,MAAO,GAGT,MAAM4Q,EAAa,GAUnB,OARA/L,OAAOC,KAAK9E,EAAQ6Q,SACjBlQ,OAAOuH,GAAOA,EAAItF,WAAW,OAC7BmC,QAAQmD,IACP,IAAI4I,EAAU5I,EAAIyD,QAAQ,MAAO,IACjCmF,EAAUA,EAAQC,OAAO,GAAG9O,cAAgB6O,EAAQvE,MAAM,EAAGuE,EAAQ9M,QACrE4M,EAAWE,GAAWZ,cAAclQ,EAAQ6Q,QAAQ3I,MAGjD0I,GAGTI,iBAAgB,CAAChR,EAASkI,IACjBgI,cAAclQ,EAAQyC,aAAc,WAAU2N,iBAAiBlI,KAGxE+I,OAAOjR,GACL,MAAMkR,EAAOlR,EAAQmR,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAMnR,SAAS4G,KAAKwK,UAC9BC,KAAMJ,EAAKI,KAAOrR,SAAS4G,KAAK0K,aAIpCC,SAASxR,IACA,CACLoR,IAAKpR,EAAQyR,UACbH,KAAMtR,EAAQ0R,cC9CdnK,OAAO,WACPuG,WAAW,cACXE,YAAa,eACbO,eAAe,YAEfoD,eAAiB,YACjBC,gBAAkB,aAClBC,uBAAyB,IACzBC,gBAAkB,GAElBC,UAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,cAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAGHE,WAAa,OACbC,WAAa,OACbC,eAAiB,OACjBC,gBAAkB,QAElBC,YAAe,oBACfC,WAAc,mBACdC,cAAiB,sBACjBC,iBAAoB,yBACpBC,iBAAoB,yBACpBC,iBAAoB,yBACpBC,gBAAmB,wBACnBC,eAAkB,uBAClBC,kBAAqB,0BACrBC,gBAAmB,wBACnBC,iBAAoB,wBACpBC,sBAAuB,4BACvB3E,uBAAwB,6BAExB4E,oBAAsB,WACtB1D,oBAAoB,SACpB2D,iBAAmB,QACnBC,eAAiB,oBACjBC,iBAAmB,sBACnBC,gBAAkB,qBAClBC,gBAAkB,qBAClBC,yBAA2B,gBAE3BC,kBAAkB,UAClBC,qBAAuB,wBACvBC,cAAgB,iBAChBC,kBAAoB,qBACpBC,mBAAqB,2CACrBC,oBAAsB,uBACtBC,mBAAqB,mBACrBC,oBAAsB,sCACtBC,mBAAqB,4BAErBC,mBAAqB,QACrBC,iBAAmB,MAOzB,MAAMC,iBAAiB9G,cACrBC,YAAY5N,EAAS2E,GACnB+P,MAAM1U,GAENwK,KAAKmK,OAAS,KACdnK,KAAKoK,UAAY,KACjBpK,KAAKqK,eAAiB,KACtBrK,KAAKsK,WAAY,EACjBtK,KAAKuK,YAAa,EAClBvK,KAAKwK,aAAe,KACpBxK,KAAKyK,YAAc,EACnBzK,KAAK0K,YAAc,EAEnB1K,KAAK2K,QAAU3K,KAAK4K,WAAWzQ,GAC/B6F,KAAK6K,mBAAqBxV,eAAeW,QAAQ2T,oBAAqB3J,KAAKqD,UAC3ErD,KAAK8K,gBAAkB,iBAAkBrV,SAASC,iBAAmBqV,UAAUC,eAAiB,EAChGhL,KAAKiL,cAAgB5J,QAAQzI,OAAOsS,cAEpClL,KAAKmL,qBAKW5D,qBAChB,OAAOA,UAGMxK,kBACb,OAAOA,OAKT/F,OACOgJ,KAAKuK,YACRvK,KAAKoL,OAAOrD,YAIhBsD,mBAGO5V,SAAS6V,QAAUtQ,UAAUgF,KAAKqD,WACrCrD,KAAKhJ,OAITH,OACOmJ,KAAKuK,YACRvK,KAAKoL,OAAOpD,YAIhBL,MAAMrI,GACCA,IACHU,KAAKsK,WAAY,GAGfjV,eAAeW,QAAQ0T,mBAAoB1J,KAAKqD,YAClDnK,qBAAqB8G,KAAKqD,UAC1BrD,KAAKuL,OAAM,IAGbC,cAAcxL,KAAKoK,WACnBpK,KAAKoK,UAAY,KAGnBmB,MAAMjM,GACCA,IACHU,KAAKsK,WAAY,GAGftK,KAAKoK,YACPoB,cAAcxL,KAAKoK,WACnBpK,KAAKoK,UAAY,MAGfpK,KAAK2K,SAAW3K,KAAK2K,QAAQnD,WAAaxH,KAAKsK,YACjDtK,KAAKyL,kBAELzL,KAAKoK,UAAYsB,aACdjW,SAASkW,gBAAkB3L,KAAKqL,gBAAkBrL,KAAKhJ,MAAM4U,KAAK5L,MACnEA,KAAK2K,QAAQnD,WAKnBqE,GAAGC,GACD9L,KAAKqK,eAAiBhV,eAAeW,QAAQuT,qBAAsBvJ,KAAKqD,UACxE,MAAM0I,EAAc/L,KAAKgM,cAAchM,KAAKqK,gBAE5C,GAAIyB,EAAQ9L,KAAKmK,OAAO3Q,OAAS,GAAKsS,EAAQ,EAC5C,OAGF,GAAI9L,KAAKuK,WAEP,YADA9K,aAAakC,IAAI3B,KAAKqD,SAAU+E,WAAY,IAAMpI,KAAK6L,GAAGC,IAI5D,GAAIC,IAAgBD,EAGlB,OAFA9L,KAAK2H,aACL3H,KAAKuL,QAIP,MAAMU,EAAQH,EAAQC,EACpBhE,WACAC,WAEFhI,KAAKoL,OAAOa,EAAOjM,KAAKmK,OAAO2B,IAKjClB,WAAWzQ,GAMT,OALAA,EAAS,IACJoN,aACApN,GAELF,gBAAgB8C,OAAM5C,EAAQ2N,eACvB3N,EAGT+R,eACE,MAAMC,EAAYvU,KAAKwU,IAAIpM,KAAK0K,aAEhC,GAAIyB,GAjMgB,GAkMlB,OAGF,MAAME,EAAYF,EAAYnM,KAAK0K,YAEnC1K,KAAK0K,YAAc,EAEd2B,GAILrM,KAAKoL,OAAOiB,EAAY,EAAInE,gBAAkBD,gBAGhDkD,qBACMnL,KAAK2K,QAAQlD,UACfhI,aAAaiC,GAAG1B,KAAKqD,SAAUgF,cAAe/I,GAASU,KAAKsM,SAAShN,IAG5C,UAAvBU,KAAK2K,QAAQhD,QACflI,aAAaiC,GAAG1B,KAAKqD,SAAUiF,iBAAkBhJ,GAASU,KAAK2H,MAAMrI,IACrEG,aAAaiC,GAAG1B,KAAKqD,SAAUkF,iBAAkBjJ,GAASU,KAAKuL,MAAMjM,KAGnEU,KAAK2K,QAAQ9C,OAAS7H,KAAK8K,iBAC7B9K,KAAKuM,0BAITA,0BACE,MAAMC,EAAQlN,KACRU,KAAKiL,eAtKU,QAsKQ3L,EAAMmN,aAvKZ,UAuKgDnN,EAAMmN,YAE/DzM,KAAKiL,gBACfjL,KAAKyK,YAAcnL,EAAMoN,QAAQ,GAAGC,SAFpC3M,KAAKyK,YAAcnL,EAAMqN,SAMvBC,EAAOtN,IAEXU,KAAK0K,YAAcpL,EAAMoN,SAAWpN,EAAMoN,QAAQlT,OAAS,EACzD,EACA8F,EAAMoN,QAAQ,GAAGC,QAAU3M,KAAKyK,aAG9BoC,EAAMvN,KACNU,KAAKiL,eArLU,QAqLQ3L,EAAMmN,aAtLZ,UAsLgDnN,EAAMmN,cACzEzM,KAAK0K,YAAcpL,EAAMqN,QAAU3M,KAAKyK,aAG1CzK,KAAKkM,eACsB,UAAvBlM,KAAK2K,QAAQhD,QASf3H,KAAK2H,QACD3H,KAAKwK,cACPsC,aAAa9M,KAAKwK,cAGpBxK,KAAKwK,aAAexQ,WAAWsF,GAASU,KAAKuL,MAAMjM,GApQ5B,IAoQ6DU,KAAK2K,QAAQnD,YAIrGnS,eAAeC,KAAKmU,kBAAmBzJ,KAAKqD,UAAU9I,QAAQwS,IAC5DtN,aAAaiC,GAAGqL,EAASlE,iBAAkBmE,GAAKA,EAAE/J,oBAGhDjD,KAAKiL,eACPxL,aAAaiC,GAAG1B,KAAKqD,SAAUsF,kBAAmBrJ,GAASkN,EAAMlN,IACjEG,aAAaiC,GAAG1B,KAAKqD,SAAUuF,gBAAiBtJ,GAASuN,EAAIvN,IAE7DU,KAAKqD,SAAS9H,UAAU0R,IAjOG,mBAmO3BxN,aAAaiC,GAAG1B,KAAKqD,SAAUmF,iBAAkBlJ,GAASkN,EAAMlN,IAChEG,aAAaiC,GAAG1B,KAAKqD,SAAUoF,gBAAiBnJ,GAASsN,EAAKtN,IAC9DG,aAAaiC,GAAG1B,KAAKqD,SAAUqF,eAAgBpJ,GAASuN,EAAIvN,KAIhEgN,SAAShN,GACH,kBAAkBzE,KAAKyE,EAAMS,OAAOmN,WA3RrB,cA+Rf5N,EAAM5B,KACR4B,EAAM2D,iBACNjD,KAAKoL,OAAOlD,kBAhSM,eAiST5I,EAAM5B,MACf4B,EAAM2D,iBACNjD,KAAKoL,OAAOnD,kBAIhB+D,cAAcxW,GAKZ,OAJAwK,KAAKmK,OAAS3U,GAAWA,EAAQgB,WAC/BnB,eAAeC,KAAKkU,cAAehU,EAAQgB,YAC3C,GAEKwJ,KAAKmK,OAAOgD,QAAQ3X,GAG7B4X,gBAAgBnB,EAAOoB,GACrB,MAAMC,EAASrB,IAAUlE,WACnBwF,EAAStB,IAAUjE,WACnB+D,EAAc/L,KAAKgM,cAAcqB,GACjCG,EAAgBxN,KAAKmK,OAAO3Q,OAAS,EAG3C,IAFuB+T,GAA0B,IAAhBxB,GAAuBuB,GAAUvB,IAAgByB,KAE5DxN,KAAK2K,QAAQ/C,KACjC,OAAOyF,EAGT,MACMI,GAAa1B,GADLwB,GAAU,EAAI,IACcvN,KAAKmK,OAAO3Q,OAEtD,OAAsB,IAAfiU,EACLzN,KAAKmK,OAAOnK,KAAKmK,OAAO3Q,OAAS,GACjCwG,KAAKmK,OAAOsD,GAGhBC,mBAAmB1M,EAAe2M,GAChC,MAAMC,EAAc5N,KAAKgM,cAAchL,GACjC6M,EAAY7N,KAAKgM,cAAc3W,eAAeW,QAAQuT,qBAAsBvJ,KAAKqD,WAEvF,OAAO5D,aAAawC,QAAQjC,KAAKqD,SAAU8E,YAAa,CACtDnH,cAAAA,EACAqL,UAAWsB,EACXxP,KAAM0P,EACNhC,GAAI+B,IAIRE,2BAA2BtY,GACzB,GAAIwK,KAAK6K,mBAAoB,CAC3B,MAAMkD,EAAkB1Y,eAAeW,QA9RrB,UA8R8CgK,KAAK6K,oBAErEkD,EAAgBxS,UAAU6C,OAxSN,UAySpB2P,EAAgB7H,gBAAgB,gBAEhC,MAAM8H,EAAa3Y,eAAeC,KA7Rb,mBA6RsC0K,KAAK6K,oBAEhE,IAAK,IAAI5K,EAAI,EAAGA,EAAI+N,EAAWxU,OAAQyG,IACrC,GAAIlH,OAAOkV,SAASD,EAAW/N,GAAGhI,aAAa,oBAAqB,MAAQ+H,KAAKgM,cAAcxW,GAAU,CACvGwY,EAAW/N,GAAG1E,UAAU0R,IA/SR,UAgThBe,EAAW/N,GAAGwF,aAAa,eAAgB,QAC3C,QAMRgG,kBACE,MAAMjW,EAAUwK,KAAKqK,gBAAkBhV,eAAeW,QAAQuT,qBAAsBvJ,KAAKqD,UAEzF,IAAK7N,EACH,OAGF,MAAM0Y,EAAkBnV,OAAOkV,SAASzY,EAAQyC,aAAa,oBAAqB,IAE9EiW,GACFlO,KAAK2K,QAAQwD,gBAAkBnO,KAAK2K,QAAQwD,iBAAmBnO,KAAK2K,QAAQnD,SAC5ExH,KAAK2K,QAAQnD,SAAW0G,GAExBlO,KAAK2K,QAAQnD,SAAWxH,KAAK2K,QAAQwD,iBAAmBnO,KAAK2K,QAAQnD,SAIzE4D,OAAOgD,EAAkB5Y,GACvB,MAAMyW,EAAQjM,KAAKqO,kBAAkBD,GAC/Bf,EAAgBhY,eAAeW,QAAQuT,qBAAsBvJ,KAAKqD,UAClEiL,EAAqBtO,KAAKgM,cAAcqB,GACxCkB,EAAc/Y,GAAWwK,KAAKoN,gBAAgBnB,EAAOoB,GAErDmB,EAAmBxO,KAAKgM,cAAcuC,GACtCE,EAAYpN,QAAQrB,KAAKoK,WAEzBkD,EAASrB,IAAUlE,WACnB2G,EAAuBpB,EAASpE,iBAAmBD,eACnD0F,EAAiBrB,EAASnE,gBAAkBC,gBAC5CuE,EAAqB3N,KAAK4O,kBAAkB3C,GAElD,GAAIsC,GAAeA,EAAYhT,UAAUC,SAtVnB,UAwVpB,YADAwE,KAAKuK,YAAa,GAKpB,GADmBvK,KAAK0N,mBAAmBa,EAAaZ,GACzCpL,iBACb,OAGF,IAAK8K,IAAkBkB,EAErB,OAGFvO,KAAKuK,YAAa,EAEdkE,GACFzO,KAAK2H,QAGP3H,KAAK8N,2BAA2BS,GAChCvO,KAAKqK,eAAiBkE,EAEtB,MAAMM,EAAmB,KACvBpP,aAAawC,QAAQjC,KAAKqD,SAAU+E,WAAY,CAC9CpH,cAAeuN,EACflC,UAAWsB,EACXxP,KAAMmQ,EACNzC,GAAI2C,KAIR,GAAIxO,KAAKqD,SAAS9H,UAAUC,SAtXP,SAsXmC,CACtD+S,EAAYhT,UAAU0R,IAAI0B,GAE1B1S,OAAOsS,GAEPlB,EAAc9R,UAAU0R,IAAIyB,GAC5BH,EAAYhT,UAAU0R,IAAIyB,GAE1B,MAAMI,EAAmB,KACvBP,EAAYhT,UAAU6C,OAAOsQ,EAAsBC,GACnDJ,EAAYhT,UAAU0R,IAjYJ,UAmYlBI,EAAc9R,UAAU6C,OAnYN,SAmYgCuQ,EAAgBD,GAElE1O,KAAKuK,YAAa,EAElBvQ,WAAW6U,EAAkB,IAG/B7O,KAAK2D,eAAemL,EAAkBzB,GAAe,QAErDA,EAAc9R,UAAU6C,OA5YJ,UA6YpBmQ,EAAYhT,UAAU0R,IA7YF,UA+YpBjN,KAAKuK,YAAa,EAClBsE,IAGEJ,GACFzO,KAAKuL,QAIT8C,kBAAkBhC,GAChB,MAAK,CAACnE,gBAAiBD,gBAAgB9P,SAASkU,GAI5C5P,QACK4P,IAAcpE,eAAiBD,WAAaD,WAG9CsE,IAAcpE,eAAiBF,WAAaC,WAP1CqE,EAUXuC,kBAAkB3C,GAChB,MAAK,CAAClE,WAAYC,YAAY7P,SAAS8T,GAInCxP,QACKwP,IAAUjE,WAAaC,eAAiBC,gBAG1C+D,IAAUjE,WAAaE,gBAAkBD,eAPvCgE,EAYapI,yBAACrO,EAAS2E,GAChC,IAAI+K,EAAO1H,KAAKM,IAAItI,EAAS8N,YACzBqH,EAAU,IACTpD,aACAxB,YAAYI,kBAAkB3Q,IAGb,iBAAX2E,IACTwQ,EAAU,IACLA,KACAxQ,IAIP,MAAM4U,EAA2B,iBAAX5U,EAAsBA,EAASwQ,EAAQjD,MAM7D,GAJKxC,IACHA,EAAO,IAAI+E,SAASzU,EAASmV,IAGT,iBAAXxQ,EACT+K,EAAK2G,GAAG1R,QACH,GAAsB,iBAAX4U,EAAqB,CACrC,QAA4B,IAAjB7J,EAAK6J,GACd,MAAM,IAAIjU,UAAW,oBAAmBiU,MAG1C7J,EAAK6J,UACIpE,EAAQnD,UAAYmD,EAAQqE,OACrC9J,EAAKyC,QACLzC,EAAKqG,SAIa1H,uBAAC1J,GACrB,OAAO6F,KAAKiF,MAAK,WACfgF,SAASgF,kBAAkBjP,KAAM7F,MAIX0J,2BAACvE,GACzB,MAAMS,EAASvH,uBAAuBwH,MAEtC,IAAKD,IAAWA,EAAOxE,UAAUC,SA9dT,YA+dtB,OAGF,MAAMrB,EAAS,IACV4L,YAAYI,kBAAkBpG,MAC9BgG,YAAYI,kBAAkBnG,OAE7BkP,EAAalP,KAAK/H,aAAa,oBAEjCiX,IACF/U,EAAOqN,UAAW,GAGpByC,SAASgF,kBAAkBlP,EAAQ5F,GAE/B+U,GACF1R,KAAKM,IAAIiC,EAAQuD,YAAUuI,GAAGqD,GAGhC5P,EAAM2D,kBAUVxD,aAAaiC,GAAGjM,SAAU0O,uBAAsB0F,oBAAqBI,SAASkF,qBAE9E1P,aAAaiC,GAAG9I,OAAQkQ,sBAAqB,KAC3C,MAAMsG,EAAY/Z,eAAeC,KAAKwU,oBAEtC,IAAK,IAAI7J,EAAI,EAAGK,EAAM8O,EAAU5V,OAAQyG,EAAIK,EAAKL,IAC/CgK,SAASgF,kBAAkBG,EAAUnP,GAAIzC,KAAKM,IAAIsR,EAAUnP,GAAIqD,eAWpE3G,mBAAmBsN,UC7jBnB,MAAMlN,OAAO,WACPuG,WAAW,cACXE,YAAa,eACbO,eAAe,YAEfwD,UAAU,CACd/B,QAAQ,EACR6J,OAAQ,IAGJvH,cAAc,CAClBtC,OAAQ,UACR6J,OAAQ,oBAGJC,aAAc,mBACdC,cAAe,oBACfC,aAAc,mBACdC,eAAgB,qBAChBtL,uBAAwB,6BAExBG,kBAAkB,OAClBoL,oBAAsB,WACtBC,sBAAwB,aACxBC,qBAAuB,YAEvBC,MAAQ,QACRC,OAAS,SAETC,iBAAmB,qBACnBzK,uBAAuB,8BAQ7B,MAAM0K,iBAAiB7M,cACrBC,YAAY5N,EAAS2E,GACnB+P,MAAM1U,GAENwK,KAAKiQ,kBAAmB,EACxBjQ,KAAK2K,QAAU3K,KAAK4K,WAAWzQ,GAC/B6F,KAAKkQ,cAAgB7a,eAAeC,KACjC,GAAEgQ,iCAA+BtF,KAAKqD,SAAS8M,QAC7C7K,2CAAyCtF,KAAKqD,SAAS8M,QAG5D,MAAMC,EAAa/a,eAAeC,KAAKgQ,wBAEvC,IAAK,IAAIrF,EAAI,EAAGK,EAAM8P,EAAW5W,OAAQyG,EAAIK,EAAKL,IAAK,CACrD,MAAMoQ,EAAOD,EAAWnQ,GAClB1K,EAAWgD,uBAAuB8X,GAClCC,EAAgBjb,eAAeC,KAAKC,GACvCY,OAAOoa,GAAaA,IAAcvQ,KAAKqD,UAEzB,OAAb9N,GAAqB+a,EAAc9W,SACrCwG,KAAKwQ,UAAYjb,EACjByK,KAAKkQ,cAActZ,KAAKyZ,IAI5BrQ,KAAKyQ,QAAUzQ,KAAK2K,QAAQ0E,OAASrP,KAAK0Q,aAAe,KAEpD1Q,KAAK2K,QAAQ0E,QAChBrP,KAAK2Q,0BAA0B3Q,KAAKqD,SAAUrD,KAAKkQ,eAGjDlQ,KAAK2K,QAAQnF,QACfxF,KAAKwF,SAMS+B,qBAChB,OAAOA,UAGMxK,kBACb,OAAOA,OAKTyI,SACMxF,KAAKqD,SAAS9H,UAAUC,SAlER,QAmElBwE,KAAK4Q,OAEL5Q,KAAK6Q,OAITA,OACE,GAAI7Q,KAAKiQ,kBAAoBjQ,KAAKqD,SAAS9H,UAAUC,SA1EjC,QA2ElB,OAGF,IAAIsV,EACAC,EAEA/Q,KAAKyQ,UACPK,EAAUzb,eAAeC,KAAKya,iBAAkB/P,KAAKyQ,SAClDta,OAAOka,GAC6B,iBAAxBrQ,KAAK2K,QAAQ0E,OACfgB,EAAKpY,aAAa,oBAAsB+H,KAAK2K,QAAQ0E,OAGvDgB,EAAK9U,UAAUC,SAvFJ,aA0FC,IAAnBsV,EAAQtX,SACVsX,EAAU,OAId,MAAME,EAAY3b,eAAeW,QAAQgK,KAAKwQ,WAC9C,GAAIM,EAAS,CACX,MAAMG,EAAiBH,EAAQxb,KAAK+a,GAAQW,IAAcX,GAG1D,GAFAU,EAAcE,EAAiBzT,KAAKM,IAAImT,EAAgB3N,YAAY,KAEhEyN,GAAeA,EAAYd,iBAC7B,OAKJ,GADmBxQ,aAAawC,QAAQjC,KAAKqD,SAAUiM,cACxC/M,iBACb,OAGEuO,GACFA,EAAQvW,QAAQ2W,IACVF,IAAcE,GAChBlB,SAASmB,kBAAkBD,EAAY,QAGpCH,GACHvT,KAAKC,IAAIyT,EAAY5N,WAAU,QAKrC,MAAM8N,EAAYpR,KAAKqR,gBAEvBrR,KAAKqD,SAAS9H,UAAU6C,OA5HA,YA6HxB4B,KAAKqD,SAAS9H,UAAU0R,IA5HE,cA8H1BjN,KAAKqD,SAASpI,MAAMmW,GAAa,EAE7BpR,KAAKkQ,cAAc1W,QACrBwG,KAAKkQ,cAAc3V,QAAQ/E,IACzBA,EAAQ+F,UAAU6C,OAjIG,aAkIrB5I,EAAQiQ,aAAa,iBAAiB,KAI1CzF,KAAKsR,kBAAiB,GAEtB,MAYMC,EAAc,UADSH,EAAU,GAAGrW,cAAgBqW,EAAUrP,MAAM,IAG1E/B,KAAK2D,eAdY,KACf3D,KAAKqD,SAAS9H,UAAU6C,OA1IA,cA2IxB4B,KAAKqD,SAAS9H,UAAU0R,IA5IF,WADJ,QA+IlBjN,KAAKqD,SAASpI,MAAMmW,GAAa,GAEjCpR,KAAKsR,kBAAiB,GAEtB7R,aAAawC,QAAQjC,KAAKqD,SAAUkM,gBAMRvP,KAAKqD,UAAU,GAC7CrD,KAAKqD,SAASpI,MAAMmW,GAAgBpR,KAAKqD,SAASkO,GAAhB,KAGpCX,OACE,GAAI5Q,KAAKiQ,mBAAqBjQ,KAAKqD,SAAS9H,UAAUC,SA9JlC,QA+JlB,OAIF,GADmBiE,aAAawC,QAAQjC,KAAKqD,SAAUmM,cACxCjN,iBACb,OAGF,MAAM6O,EAAYpR,KAAKqR,gBAEvBrR,KAAKqD,SAASpI,MAAMmW,GAAgBpR,KAAKqD,SAASsD,wBAAwByK,GAAxC,KAElCnV,OAAO+D,KAAKqD,UAEZrD,KAAKqD,SAAS9H,UAAU0R,IA3KE,cA4K1BjN,KAAKqD,SAAS9H,UAAU6C,OA7KA,WADJ,QAgLpB,MAAMoT,EAAqBxR,KAAKkQ,cAAc1W,OAC9C,GAAIgY,EAAqB,EACvB,IAAK,IAAIvR,EAAI,EAAGA,EAAIuR,EAAoBvR,IAAK,CAC3C,MAAMgC,EAAUjC,KAAKkQ,cAAcjQ,GAC7BoQ,EAAO7X,uBAAuByJ,GAEhCoO,IAASA,EAAK9U,UAAUC,SAtLZ,UAuLdyG,EAAQ1G,UAAU0R,IApLC,aAqLnBhL,EAAQwD,aAAa,iBAAiB,IAK5CzF,KAAKsR,kBAAiB,GAStBtR,KAAKqD,SAASpI,MAAMmW,GAAa,GAEjCpR,KAAK2D,eATY,KACf3D,KAAKsR,kBAAiB,GACtBtR,KAAKqD,SAAS9H,UAAU6C,OA/LA,cAgMxB4B,KAAKqD,SAAS9H,UAAU0R,IAjMF,YAkMtBxN,aAAawC,QAAQjC,KAAKqD,SAAUoM,iBAKRzP,KAAKqD,UAAU,GAG/CiO,iBAAiBG,GACfzR,KAAKiQ,iBAAmBwB,EAK1B7G,WAAWzQ,GAOT,OANAA,EAAS,IACJoN,aACApN,IAEEqL,OAASnE,QAAQlH,EAAOqL,QAC/BvL,gBAAgB8C,OAAM5C,EAAQ2N,eACvB3N,EAGTkX,gBACE,OAAOrR,KAAKqD,SAAS9H,UAAUC,SAASqU,OAASA,MAAQC,OAG3DY,aACE,IAAIrB,OAAEA,GAAWrP,KAAK2K,QAEtB0E,EAAS9V,WAAW8V,GAEpB,MAAM9Z,EAAY,GAAE+P,0CAAwC+J,MAY5D,OAVAha,eAAeC,KAAKC,EAAU8Z,GAC3B9U,QAAQ/E,IACP,MAAMkc,EAAWlZ,uBAAuBhD,GAExCwK,KAAK2Q,0BACHe,EACA,CAAClc,MAIA6Z,EAGTsB,0BAA0Bnb,EAASmc,GACjC,IAAKnc,IAAYmc,EAAanY,OAC5B,OAGF,MAAMoY,EAASpc,EAAQ+F,UAAUC,SAxPb,QA0PpBmW,EAAapX,QAAQ8V,IACfuB,EACFvB,EAAK9U,UAAU6C,OAzPM,aA2PrBiS,EAAK9U,UAAU0R,IA3PM,aA8PvBoD,EAAK5K,aAAa,gBAAiBmM,KAMf/N,yBAACrO,EAAS2E,GAChC,IAAI+K,EAAO1H,KAAKM,IAAItI,EAAS8N,YAC7B,MAAMqH,EAAU,IACXpD,aACAxB,YAAYI,kBAAkB3Q,MACX,iBAAX2E,GAAuBA,EAASA,EAAS,IAWtD,IARK+K,GAAQyF,EAAQnF,QAA4B,iBAAXrL,GAAuB,YAAYU,KAAKV,KAC5EwQ,EAAQnF,QAAS,GAGdN,IACHA,EAAO,IAAI8K,SAASxa,EAASmV,IAGT,iBAAXxQ,EAAqB,CAC9B,QAA4B,IAAjB+K,EAAK/K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C+K,EAAK/K,MAIa0J,uBAAC1J,GACrB,OAAO6F,KAAKiF,MAAK,WACf+K,SAASmB,kBAAkBnR,KAAM7F,OAWvCsF,aAAaiC,GAAGjM,SAAU0O,uBAAsBmB,wBAAsB,SAAUhG,IAEjD,MAAzBA,EAAMS,OAAOmN,SAAoB5N,EAAMC,gBAAmD,MAAjCD,EAAMC,eAAe2N,UAChF5N,EAAM2D,iBAGR,MAAM4O,EAAc9L,YAAYI,kBAAkBnG,MAC5CzK,EAAWgD,uBAAuByH,MACf3K,eAAeC,KAAKC,GAE5BgF,QAAQ/E,IACvB,MAAM0P,EAAO1H,KAAKM,IAAItI,EAAS8N,YAC/B,IAAInJ,EACA+K,GAEmB,OAAjBA,EAAKuL,SAAkD,iBAAvBoB,EAAYxC,SAC9CnK,EAAKyF,QAAQ0E,OAASwC,EAAYxC,OAClCnK,EAAKuL,QAAUvL,EAAKwL,cAGtBvW,EAAS,UAETA,EAAS0X,EAGX7B,SAASmB,kBAAkB3b,EAAS2E,QAWxCwC,mBAAmBqT,UCjWnB,MAAMjT,OAAO,WACPuG,WAAW,cACXE,YAAa,eACbO,eAAe,YAEf+N,aAAa,SACbC,UAAY,QACZC,QAAU,MACVC,aAAe,UACfC,eAAiB,YACjBC,mBAAqB,EAErBC,eAAiB,IAAIxX,OAAQ,4BAE7B4U,aAAc,mBACdC,eAAgB,qBAChBH,aAAc,mBACdC,cAAe,oBACf8C,YAAe,oBACflO,uBAAwB,6BACxBmO,uBAA0B,+BAC1BC,qBAAwB,6BAExBjO,kBAAkB,OAClBkO,kBAAoB,SACpBC,mBAAqB,UACrBC,qBAAuB,YACvBC,kBAAoB,SAEpBrN,uBAAuB,8BACvBsN,cAAgB,iBAChBC,oBAAsB,cACtBC,uBAAyB,8DAEzBC,cAAgBtW,QAAU,UAAY,YACtCuW,iBAAmBvW,QAAU,YAAc,UAC3CwW,iBAAmBxW,QAAU,aAAe,eAC5CyW,oBAAsBzW,QAAU,eAAiB,aACjD0W,gBAAkB1W,QAAU,aAAe,cAC3C2W,eAAiB3W,QAAU,cAAgB,aAE3C8K,UAAU,CACdd,OAAQ,CAAC,EAAG,GACZ4M,SAAU,kBACVC,UAAW,SACXlY,QAAS,UACTmY,aAAc,KACdC,WAAW,GAGP1L,cAAc,CAClBrB,OAAQ,0BACR4M,SAAU,mBACVC,UAAW,0BACXlY,QAAS,SACTmY,aAAc,yBACdC,UAAW,oBASb,MAAMC,iBAAiBtQ,cACrBC,YAAY5N,EAAS2E,GACnB+P,MAAM1U,GAENwK,KAAK0T,QAAU,KACf1T,KAAK2K,QAAU3K,KAAK4K,WAAWzQ,GAC/B6F,KAAK2T,MAAQ3T,KAAK4T,kBAClB5T,KAAK6T,UAAY7T,KAAK8T,gBAEtB9T,KAAKmL,qBAKW5D,qBAChB,OAAOA,UAGaO,yBACpB,OAAOA,cAGM/K,kBACb,OAAOA,OAKTyI,SACMlK,WAAW0E,KAAKqD,YAIHrD,KAAKqD,SAAS9H,UAAUC,SA3ErB,QA8ElBwE,KAAK4Q,OAIP5Q,KAAK6Q,QAGPA,OACE,GAAIvV,WAAW0E,KAAKqD,WAAarD,KAAK2T,MAAMpY,UAAUC,SAtFlC,QAuFlB,OAGF,MAAM6T,EAASoE,SAASM,qBAAqB/T,KAAKqD,UAC5CrC,EAAgB,CACpBA,cAAehB,KAAKqD,UAKtB,IAFkB5D,aAAawC,QAAQjC,KAAKqD,SAAUiM,aAAYtO,GAEpDuB,iBAAd,CAKA,GAAIvC,KAAK6T,UACP9N,YAAYC,iBAAiBhG,KAAK2T,MAAO,SAAU,YAC9C,CACL,QAAsB,IAAXK,OACT,MAAM,IAAIlZ,UAAU,gEAGtB,IAAImZ,EAAmBjU,KAAKqD,SAEG,WAA3BrD,KAAK2K,QAAQ2I,UACfW,EAAmB5E,EACVhW,UAAU2G,KAAK2K,QAAQ2I,WAChCW,EAAmB1a,WAAWyG,KAAK2K,QAAQ2I,WACA,iBAA3BtT,KAAK2K,QAAQ2I,YAC7BW,EAAmBjU,KAAK2K,QAAQ2I,WAGlC,MAAMC,EAAevT,KAAKkU,mBACpBC,EAAkBZ,EAAaa,UAAU9e,KAAK+e,GAA8B,gBAAlBA,EAASvX,OAA+C,IAArBuX,EAASC,SAE5GtU,KAAK0T,QAAUM,OAAOO,aAAaN,EAAkBjU,KAAK2T,MAAOJ,GAE7DY,GACFpO,YAAYC,iBAAiBhG,KAAK2T,MAAO,SAAU,UAQnD,iBAAkBle,SAASC,kBAC5B2Z,EAAOvK,QA9Hc,gBA+HtB,GAAGnP,UAAUF,SAAS4G,KAAKnG,UACxBqE,QAAQ8V,GAAQ5Q,aAAaiC,GAAG2O,EAAM,YAAarU,OAGxDgE,KAAKqD,SAASmR,QACdxU,KAAKqD,SAASoC,aAAa,iBAAiB,GAE5CzF,KAAK2T,MAAMpY,UAAUiK,OA9ID,QA+IpBxF,KAAKqD,SAAS9H,UAAUiK,OA/IJ,QAgJpB/F,aAAawC,QAAQjC,KAAKqD,SAAUkM,cAAavO,IAGnD4P,OACE,GAAItV,WAAW0E,KAAKqD,YAAcrD,KAAK2T,MAAMpY,UAAUC,SApJnC,QAqJlB,OAGF,MAAMwF,EAAgB,CACpBA,cAAehB,KAAKqD,UAGtBrD,KAAKyU,cAAczT,GAGrBuC,UACMvD,KAAK0T,SACP1T,KAAK0T,QAAQgB,UAGfxK,MAAM3G,UAGRoR,SACE3U,KAAK6T,UAAY7T,KAAK8T,gBAClB9T,KAAK0T,SACP1T,KAAK0T,QAAQiB,SAMjBxJ,qBACE1L,aAAaiC,GAAG1B,KAAKqD,SAAUgP,YAAa/S,IAC1CA,EAAM2D,iBACNjD,KAAKwF,WAITiP,cAAczT,GACMvB,aAAawC,QAAQjC,KAAKqD,SAAUmM,aAAYxO,GACpDuB,mBAMV,iBAAkB9M,SAASC,iBAC7B,GAAGC,UAAUF,SAAS4G,KAAKnG,UACxBqE,QAAQ8V,GAAQ5Q,aAAaC,IAAI2Q,EAAM,YAAarU,OAGrDgE,KAAK0T,SACP1T,KAAK0T,QAAQgB,UAGf1U,KAAK2T,MAAMpY,UAAU6C,OAxMD,QAyMpB4B,KAAKqD,SAAS9H,UAAU6C,OAzMJ,QA0MpB4B,KAAKqD,SAASoC,aAAa,gBAAiB,SAC5CM,YAAYE,oBAAoBjG,KAAK2T,MAAO,UAC5ClU,aAAawC,QAAQjC,KAAKqD,SAAUoM,eAAczO,IAGpD4J,WAAWzQ,GAST,GARAA,EAAS,IACJ6F,KAAKoD,YAAYmE,WACjBxB,YAAYI,kBAAkBnG,KAAKqD,aACnClJ,GAGLF,gBAAgB8C,OAAM5C,EAAQ6F,KAAKoD,YAAY0E,aAEf,iBAArB3N,EAAOmZ,YAA2Bja,UAAUc,EAAOmZ,YACV,mBAA3CnZ,EAAOmZ,UAAU3M,sBAGxB,MAAM,IAAI7L,UAAaiC,OAAKhC,cAAP,kGAGvB,OAAOZ,EAGTyZ,kBACE,OAAOve,eAAe2B,KAAKgJ,KAAKqD,SAAUuP,eAAe,GAG3DgC,gBACE,MAAMC,EAAiB7U,KAAKqD,SAAS7M,WAErC,GAAIqe,EAAetZ,UAAUC,SAvON,WAwOrB,OAAO2X,gBAGT,GAAI0B,EAAetZ,UAAUC,SA1OJ,aA2OvB,OAAO4X,eAIT,MAAM0B,EAAkF,QAA1Ejc,iBAAiBmH,KAAK2T,OAAOoB,iBAAiB,iBAAiBzc,OAE7E,OAAIuc,EAAetZ,UAAUC,SAnPP,UAoPbsZ,EAAQ9B,iBAAmBD,cAG7B+B,EAAQ5B,oBAAsBD,iBAGvCa,gBACE,OAA0D,OAAnD9T,KAAKqD,SAASyB,QAAS,WAGhCkQ,aACE,MAAMvO,OAAEA,GAAWzG,KAAK2K,QAExB,MAAsB,iBAAXlE,EACFA,EAAOpO,MAAM,KAAK4c,IAAItP,GAAO5M,OAAOkV,SAAStI,EAAK,KAGrC,mBAAXc,EACFyO,GAAczO,EAAOyO,EAAYlV,KAAKqD,UAGxCoD,EAGTyN,mBACE,MAAMiB,EAAwB,CAC5BC,UAAWpV,KAAK4U,gBAChBR,UAAW,CAAC,CACVtX,KAAM,kBACNuY,QAAS,CACPhC,SAAUrT,KAAK2K,QAAQ0I,WAG3B,CACEvW,KAAM,SACNuY,QAAS,CACP5O,OAAQzG,KAAKgV,iBAanB,MAP6B,WAAzBhV,KAAK2K,QAAQvP,UACf+Z,EAAsBf,UAAY,CAAC,CACjCtX,KAAM,cACNwX,SAAS,KAIN,IACFa,KACsC,mBAA9BnV,KAAK2K,QAAQ4I,aAA8BvT,KAAK2K,QAAQ4I,aAAa4B,GAAyBnV,KAAK2K,QAAQ4I,cAI1H+B,gBAAgBhW,GACd,MAAMiW,EAAQlgB,eAAeC,KAAKwd,uBAAwB9S,KAAK2T,OAAOxd,OAAO6E,WAE7E,IAAKua,EAAM/b,OACT,OAGF,IAAIsS,EAAQyJ,EAAMpI,QAAQ7N,EAAMS,QAlUf,YAqUbT,EAAM5B,KAAwBoO,EAAQ,GACxCA,IArUiB,cAyUfxM,EAAM5B,KAA0BoO,EAAQyJ,EAAM/b,OAAS,GACzDsS,IAIFA,GAAmB,IAAXA,EAAe,EAAIA,EAE3ByJ,EAAMzJ,GAAO0I,QAKS3Q,yBAACrO,EAAS2E,GAChC,IAAI+K,EAAO1H,KAAKM,IAAItI,EAAS8N,YAO7B,GAJK4B,IACHA,EAAO,IAAIuO,SAASje,EAHY,iBAAX2E,EAAsBA,EAAS,OAMhC,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB+K,EAAK/K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C+K,EAAK/K,MAIa0J,uBAAC1J,GACrB,OAAO6F,KAAKiF,MAAK,WACfwO,SAAS+B,kBAAkBxV,KAAM7F,MAIpB0J,kBAACvE,GAChB,GAAIA,IA5WmB,IA4WTA,EAAMwG,QAAiD,UAAfxG,EAAMK,MA/WhD,QA+WoEL,EAAM5B,KACpF,OAGF,MAAM+X,EAAUpgB,eAAeC,KAAKgQ,wBAEpC,IAAK,IAAIrF,EAAI,EAAGK,EAAMmV,EAAQjc,OAAQyG,EAAIK,EAAKL,IAAK,CAClD,MAAMyV,EAAUlY,KAAKM,IAAI2X,EAAQxV,GAAIqD,YACrC,IAAKoS,IAAyC,IAA9BA,EAAQ/K,QAAQ6I,UAC9B,SAGF,IAAKkC,EAAQrS,SAAS9H,UAAUC,SA3Wd,QA4WhB,SAGF,MAAMwF,EAAgB,CACpBA,cAAe0U,EAAQrS,UAGzB,GAAI/D,EAAO,CACT,MAAMqW,EAAerW,EAAMqW,eACrBC,EAAeD,EAAaxd,SAASud,EAAQ/B,OACnD,GACEgC,EAAaxd,SAASud,EAAQrS,WACC,WAA9BqS,EAAQ/K,QAAQ6I,YAA2BoC,GACb,YAA9BF,EAAQ/K,QAAQ6I,WAA2BoC,EAE5C,SAIF,GAAIF,EAAQ/B,MAAMnY,SAAS8D,EAAMS,UAA4B,UAAfT,EAAMK,MA/Y5C,QA+YgEL,EAAM5B,KAAoB,qCAAqC7C,KAAKyE,EAAMS,OAAOmN,UACvJ,SAGiB,UAAf5N,EAAMK,OACRqB,EAAc6U,WAAavW,GAI/BoW,EAAQjB,cAAczT,IAIC6C,4BAACrO,GAC1B,OAAOgD,uBAAuBhD,IAAYA,EAAQgB,WAGxBqN,6BAACvE,GAQ3B,GAAI,kBAAkBzE,KAAKyE,EAAMS,OAAOmN,SAza1B,UA0aZ5N,EAAM5B,KA3aO,WA2ae4B,EAAM5B,MAvajB,cAwaf4B,EAAM5B,KAzaO,YAyamB4B,EAAM5B,KACtC4B,EAAMS,OAAO+E,QAAQ8N,iBACtBR,eAAevX,KAAKyE,EAAM5B,KAC3B,OAGF,MAAMoY,EAAW9V,KAAKzE,UAAUC,SAhaZ,QAkapB,IAAKsa,GApbU,WAobExW,EAAM5B,IACrB,OAMF,GAHA4B,EAAM2D,iBACN3D,EAAMyW,kBAEFza,WAAW0E,MACb,OAGF,MAAMgW,EAAkB,IAAMhW,KAAK3J,QAAQiP,wBAAwBtF,KAAO3K,eAAewB,KAAKmJ,KAAMsF,wBAAsB,GAE1H,GAjce,WAicXhG,EAAM5B,IAGR,OAFAsY,IAAkBxB,aAClBf,SAASwC,aAINH,GApcY,YAocCxW,EAAM5B,KAncL,cAmc6B4B,EAAM5B,IAKjDoY,GA3cS,UA2cGxW,EAAM5B,IAKvB+V,SAASyC,YAAYF,KAAmBV,gBAAgBhW,GAJtDmU,SAASwC,aALTD,IAAkBG,SAmBxB1W,aAAaiC,GAAGjM,SAAU6c,uBAAwBhN,uBAAsBmO,SAAS2C,uBACjF3W,aAAaiC,GAAGjM,SAAU6c,uBAAwBM,cAAea,SAAS2C,uBAC1E3W,aAAaiC,GAAGjM,SAAU0O,uBAAsBsP,SAASwC,YACzDxW,aAAaiC,GAAGjM,SAAU8c,qBAAsBkB,SAASwC,YACzDxW,aAAaiC,GAAGjM,SAAU0O,uBAAsBmB,wBAAsB,SAAUhG,GAC9EA,EAAM2D,iBACNwQ,SAAS+B,kBAAkBxV,SAU7BrD,mBAAmB8W,UCtgBnB,MAAM4C,uBAAyB,oDACzBC,wBAA0B,cAE1BC,SAAW,KAEf,MAAMC,EAAgB/gB,SAASC,gBAAgB+gB,YAC/C,OAAO7e,KAAKwU,IAAIxT,OAAO8d,WAAaF,IAGhC5F,KAAO,CAAC+F,EAAQJ,cACpBK,mBAEAC,sBAAsB,OAAQ,eAAgBC,GAAmBA,EAAkBH,GAEnFE,sBAAsBR,uBAAwB,eAAgBS,GAAmBA,EAAkBH,GACnGE,sBAd8B,cAciB,cAAeC,GAAmBA,EAAkBH,IAG/FC,iBAAmB,KACvB,MAAMG,EAActhB,SAAS4G,KAAKpB,MAAM+b,SACpCD,GACFhR,YAAYC,iBAAiBvQ,SAAS4G,KAAM,WAAY0a,GAG1DthB,SAAS4G,KAAKpB,MAAM+b,SAAW,UAG3BH,sBAAwB,CAACthB,EAAU0hB,EAAW1a,KAClD,MAAM2a,EAAiBX,WACvBlhB,eAAeC,KAAKC,GACjBgF,QAAQ/E,IACP,GAAIA,IAAYC,SAAS4G,MAAQzD,OAAO8d,WAAalhB,EAAQihB,YAAcS,EACzE,OAGF,MAAMH,EAAcvhB,EAAQyF,MAAMgc,GAC5BH,EAAkBle,OAAOC,iBAAiBrD,GAASyhB,GACzDlR,YAAYC,iBAAiBxQ,EAASyhB,EAAWF,GACjDvhB,EAAQyF,MAAMgc,GAAgB1a,EAASxD,OAAOC,WAAW8d,IAA7B,QAI5BK,MAAQ,KACZC,wBAAwB,OAAQ,YAChCA,wBAAwB,OAAQ,gBAChCA,wBAAwBf,uBAAwB,gBAChDe,wBA7C8B,cA6CmB,gBAG7CA,wBAA0B,CAAC7hB,EAAU0hB,KACzC5hB,eAAeC,KAAKC,GAAUgF,QAAQ/E,IACpC,MAAMkF,EAAQqL,YAAYS,iBAAiBhR,EAASyhB,QAC/B,IAAVvc,EACTlF,EAAQyF,MAAMoc,eAAeJ,IAE7BlR,YAAYE,oBAAoBzQ,EAASyhB,GACzCzhB,EAAQyF,MAAMgc,GAAavc,MCxD3B6M,UAAU,CACdvM,WAAW,EACX4I,YAAY,EACZa,YAAahP,SAAS4G,KACtBib,cAAe,MAGXxP,cAAc,CAClB9M,UAAW,UACX4I,WAAY,UACZa,YAAa,UACb6S,cAAe,mBAEXva,OAAO,WACPwa,oBAAsB,iBACtBlT,kBAAkB,OAClBC,kBAAkB,OAElBkT,gBAAmB,wBAEzB,MAAMC,SACJrU,YAAYjJ,GACV6F,KAAK2K,QAAU3K,KAAK4K,WAAWzQ,GAC/B6F,KAAK0X,aAAc,EACnB1X,KAAKqD,SAAW,KAGlBwN,KAAKtU,GACEyD,KAAK2K,QAAQ3P,WAKlBgF,KAAK2X,UAED3X,KAAK2K,QAAQ/G,YACf3H,OAAO+D,KAAK4X,eAGd5X,KAAK4X,cAAcrc,UAAU0R,IAvBT,QAyBpBjN,KAAK6X,kBAAkB,KACrBxa,QAAQd,MAbRc,QAAQd,GAiBZqU,KAAKrU,GACEyD,KAAK2K,QAAQ3P,WAKlBgF,KAAK4X,cAAcrc,UAAU6C,OApCT,QAsCpB4B,KAAK6X,kBAAkB,KACrB7X,KAAKuD,UACLlG,QAAQd,MARRc,QAAQd,GAcZqb,cACE,IAAK5X,KAAKqD,SAAU,CAClB,MAAMyU,EAAWriB,SAASsiB,cAAc,OACxCD,EAASE,UAnDa,iBAoDlBhY,KAAK2K,QAAQ/G,YACfkU,EAASvc,UAAU0R,IApDH,QAuDlBjN,KAAKqD,SAAWyU,EAGlB,OAAO9X,KAAKqD,SAGduH,WAAWzQ,GAQT,OAPAA,EAAS,IACJoN,aACmB,iBAAXpN,EAAsBA,EAAS,KAGrCsK,YAActK,EAAOsK,aAAehP,SAAS4G,KACpDpC,gBAAgB8C,OAAM5C,EAAQ2N,eACvB3N,EAGTwd,UACM3X,KAAK0X,cAIT1X,KAAK2K,QAAQlG,YAAYwT,YAAYjY,KAAK4X,eAE1CnY,aAAaiC,GAAG1B,KAAK4X,cAAeJ,gBAAiB,KACnDna,QAAQ2C,KAAK2K,QAAQ2M,iBAGvBtX,KAAK0X,aAAc,GAGrBnU,UACOvD,KAAK0X,cAIVjY,aAAaC,IAAIM,KAAKqD,SAAUmU,iBAEhCxX,KAAK4X,cAAcphB,WAAWwO,YAAYhF,KAAKqD,UAC/CrD,KAAK0X,aAAc,GAGrBG,kBAAkBtb,GAChB,IAAKyD,KAAK2K,QAAQ/G,WAEhB,YADAvG,QAAQd,GAIV,MAAM2b,EAA6Bzf,iCAAiCuH,KAAK4X,eACzEnY,aAAakC,IAAI3B,KAAK4X,cAAe,gBAAiB,IAAMva,QAAQd,IACpE9C,qBAAqBuG,KAAK4X,cAAeM,ICpG7C,MAAMnb,OAAO,QACPuG,WAAW,WACXE,YAAa,YACbO,eAAe,YACf+N,aAAa,SAEbvK,UAAU,CACduQ,UAAU,EACVrQ,UAAU,EACV+M,OAAO,GAGH1M,cAAc,CAClBgQ,SAAU,mBACVrQ,SAAU,UACV+M,MAAO,WAGHhF,aAAc,gBACd2I,qBAAwB,yBACxB1I,eAAgB,kBAChBH,aAAc,gBACdC,cAAe,iBACf6I,gBAAiB,mBACjBC,aAAgB,kBAChBC,sBAAuB,yBACvBC,wBAAyB,2BACzBC,sBAAyB,2BACzBC,wBAA2B,6BAC3BtU,uBAAwB,0BAExBuU,gBAAkB,aAClBrU,kBAAkB,OAClBC,kBAAkB,OAClBqU,kBAAoB,eAEpBC,gBAAkB,gBAClBC,oBAAsB,cACtBvT,uBAAuB,2BACvBwT,wBAAwB,4BAQ9B,MAAMC,cAAc5V,cAClBC,YAAY5N,EAAS2E,GACnB+P,MAAM1U,GAENwK,KAAK2K,QAAU3K,KAAK4K,WAAWzQ,GAC/B6F,KAAKgZ,QAAU3jB,eAAeW,QAhBV,gBAgBmCgK,KAAKqD,UAC5DrD,KAAKiZ,UAAYjZ,KAAKkZ,sBACtBlZ,KAAKmZ,UAAW,EAChBnZ,KAAKoZ,sBAAuB,EAC5BpZ,KAAKiQ,kBAAmB,EAKR1I,qBAChB,OAAOA,UAGMxK,kBACb,OAAOA,OAKTyI,OAAOxE,GACL,OAAOhB,KAAKmZ,SAAWnZ,KAAK4Q,OAAS5Q,KAAK6Q,KAAK7P,GAGjD6P,KAAK7P,GACH,GAAIhB,KAAKmZ,UAAYnZ,KAAKiQ,iBACxB,OAGEjQ,KAAKqZ,gBACPrZ,KAAKiQ,kBAAmB,GAG1B,MAAMqJ,EAAY7Z,aAAawC,QAAQjC,KAAKqD,SAAUiM,aAAY,CAChEtO,cAAAA,IAGEhB,KAAKmZ,UAAYG,EAAU/W,mBAI/BvC,KAAKmZ,UAAW,EAEhBI,OAEA9jB,SAAS4G,KAAKd,UAAU0R,IAjEJ,cAmEpBjN,KAAKwZ,gBAELxZ,KAAKyZ,kBACLzZ,KAAK0Z,kBAELja,aAAaiC,GAAG1B,KAAKqD,SAAUiV,sBAAqBQ,wBAAuBxZ,GAASU,KAAK4Q,KAAKtR,IAE9FG,aAAaiC,GAAG1B,KAAKgZ,QAASP,wBAAyB,KACrDhZ,aAAakC,IAAI3B,KAAKqD,SAAUmV,sBAAuBlZ,IACjDA,EAAMS,SAAWC,KAAKqD,WACxBrD,KAAKoZ,sBAAuB,OAKlCpZ,KAAK2Z,cAAc,IAAM3Z,KAAK4Z,aAAa5Y,KAG7C4P,KAAKtR,GAKH,GAJIA,GACFA,EAAM2D,kBAGHjD,KAAKmZ,UAAYnZ,KAAKiQ,iBACzB,OAKF,GAFkBxQ,aAAawC,QAAQjC,KAAKqD,SAAUmM,cAExCjN,iBACZ,OAGFvC,KAAKmZ,UAAW,EAChB,MAAMvV,EAAa5D,KAAKqZ,cAEpBzV,IACF5D,KAAKiQ,kBAAmB,GAG1BjQ,KAAKyZ,kBACLzZ,KAAK0Z,kBAELja,aAAaC,IAAIjK,SAAU2iB,iBAE3BpY,KAAKqD,SAAS9H,UAAU6C,OA9GJ,QAgHpBqB,aAAaC,IAAIM,KAAKqD,SAAUiV,uBAChC7Y,aAAaC,IAAIM,KAAKgZ,QAASP,yBAE/BzY,KAAK2D,eAAe,IAAM3D,KAAK6Z,aAAc7Z,KAAKqD,SAAUO,GAG9DL,UACE,CAAC3K,OAAQoH,KAAKgZ,SACXze,QAAQuf,GAAera,aAAaC,IAAIoa,EAvJ5B,cAyJf9Z,KAAKiZ,UAAU1V,UACf2G,MAAM3G,UAON9D,aAAaC,IAAIjK,SAAU2iB,iBAG7B2B,eACE/Z,KAAKwZ,gBAKPN,sBACE,OAAO,IAAIzB,SAAS,CAClBzc,UAAWqG,QAAQrB,KAAK2K,QAAQmN,UAChClU,WAAY5D,KAAKqZ,gBAIrBzO,WAAWzQ,GAOT,OANAA,EAAS,IACJoN,aACAxB,YAAYI,kBAAkBnG,KAAKqD,aACnClJ,GAELF,gBAAgB8C,OAAM5C,EAAQ2N,eACvB3N,EAGTyf,aAAa5Y,GACX,MAAM4C,EAAa5D,KAAKqZ,cAClBW,EAAY3kB,eAAeW,QA1JT,cA0JsCgK,KAAKgZ,SAE9DhZ,KAAKqD,SAAS7M,YAAcwJ,KAAKqD,SAAS7M,WAAWC,WAAaC,KAAKC,cAE1ElB,SAAS4G,KAAK4b,YAAYjY,KAAKqD,UAGjCrD,KAAKqD,SAASpI,MAAMG,QAAU,QAC9B4E,KAAKqD,SAAS6C,gBAAgB,eAC9BlG,KAAKqD,SAASoC,aAAa,cAAc,GACzCzF,KAAKqD,SAASoC,aAAa,OAAQ,UACnCzF,KAAKqD,SAASwD,UAAY,EAEtBmT,IACFA,EAAUnT,UAAY,GAGpBjD,GACF3H,OAAO+D,KAAKqD,UAGdrD,KAAKqD,SAAS9H,UAAU0R,IAnLJ,QAqLhBjN,KAAK2K,QAAQ6J,OACfxU,KAAKia,gBAcPja,KAAK2D,eAXsB,KACrB3D,KAAK2K,QAAQ6J,OACfxU,KAAKqD,SAASmR,QAGhBxU,KAAKiQ,kBAAmB,EACxBxQ,aAAawC,QAAQjC,KAAKqD,SAAUkM,cAAa,CAC/CvO,cAAAA,KAIoChB,KAAKgZ,QAASpV,GAGxDqW,gBACExa,aAAaC,IAAIjK,SAAU2iB,iBAC3B3Y,aAAaiC,GAAGjM,SAAU2iB,gBAAe9Y,IACnC7J,WAAa6J,EAAMS,QACnBC,KAAKqD,WAAa/D,EAAMS,QACvBC,KAAKqD,SAAS7H,SAAS8D,EAAMS,SAChCC,KAAKqD,SAASmR,UAKpBiF,kBACMzZ,KAAKmZ,SACP1Z,aAAaiC,GAAG1B,KAAKqD,SAAUkV,wBAAuBjZ,IAChDU,KAAK2K,QAAQlD,UAlPN,WAkPkBnI,EAAM5B,KACjC4B,EAAM2D,iBACNjD,KAAK4Q,QACK5Q,KAAK2K,QAAQlD,UArPd,WAqP0BnI,EAAM5B,KACzCsC,KAAKka,+BAITza,aAAaC,IAAIM,KAAKqD,SAAUkV,yBAIpCmB,kBACM1Z,KAAKmZ,SACP1Z,aAAaiC,GAAG9I,OAAQyf,aAAc,IAAMrY,KAAKwZ,iBAEjD/Z,aAAaC,IAAI9G,OAAQyf,cAI7BwB,aACE7Z,KAAKqD,SAASpI,MAAMG,QAAU,OAC9B4E,KAAKqD,SAASoC,aAAa,eAAe,GAC1CzF,KAAKqD,SAAS6C,gBAAgB,cAC9BlG,KAAKqD,SAAS6C,gBAAgB,QAC9BlG,KAAKiQ,kBAAmB,EACxBjQ,KAAKiZ,UAAUrI,KAAK,KAClBnb,SAAS4G,KAAKd,UAAU6C,OAlPN,cAmPlB4B,KAAKma,oBACLC,QACA3a,aAAawC,QAAQjC,KAAKqD,SAAUoM,kBAIxCkK,cAAcpd,GACZkD,aAAaiC,GAAG1B,KAAKqD,SAAUiV,sBAAqBhZ,IAC9CU,KAAKoZ,qBACPpZ,KAAKoZ,sBAAuB,EAI1B9Z,EAAMS,SAAWT,EAAM+a,iBAIG,IAA1Bra,KAAK2K,QAAQmN,SACf9X,KAAK4Q,OAC8B,WAA1B5Q,KAAK2K,QAAQmN,UACtB9X,KAAKka,gCAITla,KAAKiZ,UAAUpI,KAAKtU,GAGtB8c,cACE,OAAOrZ,KAAKqD,SAAS9H,UAAUC,SA9QX,QAiRtB0e,6BAEE,GADkBza,aAAawC,QAAQjC,KAAKqD,SAAU8U,sBACxC5V,iBACZ,OAGF,MAAM+X,EAAqBta,KAAKqD,SAASkX,aAAe9kB,SAASC,gBAAgB8kB,aAE5EF,IACHta,KAAKqD,SAASpI,MAAMwf,UAAY,UAGlCza,KAAKqD,SAAS9H,UAAU0R,IA3RF,gBA4RtB,MAAMyN,EAA0BjiB,iCAAiCuH,KAAKgZ,SACtEvZ,aAAaC,IAAIM,KAAKqD,SAAU,iBAChC5D,aAAakC,IAAI3B,KAAKqD,SAAU,gBAAiB,KAC/CrD,KAAKqD,SAAS9H,UAAU6C,OA/RJ,gBAgSfkc,IACH7a,aAAakC,IAAI3B,KAAKqD,SAAU,gBAAiB,KAC/CrD,KAAKqD,SAASpI,MAAMwf,UAAY,KAElChhB,qBAAqBuG,KAAKqD,SAAUqX,MAGxCjhB,qBAAqBuG,KAAKqD,SAAUqX,GACpC1a,KAAKqD,SAASmR,QAOhBgF,gBACE,MAAMc,EAAqBta,KAAKqD,SAASkX,aAAe9kB,SAASC,gBAAgB8kB,aAC3EtD,EAAiByD,WACjBC,EAAoB1D,EAAiB,IAErC0D,GAAqBN,IAAuB7d,SAAame,IAAsBN,GAAsB7d,WACzGuD,KAAKqD,SAASpI,MAAM4f,YAAiB3D,EAAF,OAGhC0D,IAAsBN,IAAuB7d,UAAcme,GAAqBN,GAAsB7d,WACzGuD,KAAKqD,SAASpI,MAAM6f,aAAkB5D,EAAF,MAIxCiD,oBACEna,KAAKqD,SAASpI,MAAM4f,YAAc,GAClC7a,KAAKqD,SAASpI,MAAM6f,aAAe,GAKfjX,uBAAC1J,EAAQ6G,GAC7B,OAAOhB,KAAKiF,MAAK,WACf,MAAMC,EAAO6T,MAAM7C,YAAYlW,OAAS,IAAI+Y,MAAM/Y,KAAwB,iBAAX7F,EAAsBA,EAAS,IAE9F,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjB+K,EAAK/K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C+K,EAAK/K,GAAQ6G,QAWnBvB,aAAaiC,GAAGjM,SAAU0O,uBAAsBmB,wBAAsB,SAAUhG,GAC9E,MAAMS,EAASvH,uBAAuBwH,MAElC,CAAC,IAAK,QAAQ7H,SAAS6H,KAAKkN,UAC9B5N,EAAM2D,iBAGRxD,aAAakC,IAAI5B,EAAQuP,aAAYgK,IAC/BA,EAAU/W,kBAKd9C,aAAakC,IAAI5B,EAAQ0P,eAAc,KACjCzU,UAAUgF,OACZA,KAAKwU,aAKEuE,MAAM7C,YAAYnW,IAAW,IAAIgZ,MAAMhZ,IAE/CyF,OAAOxF,SAUdrD,mBAAmBoc,OC/ZnB,MAAMhc,OAAO,YACPuG,WAAW,eACXE,YAAa,gBACbO,eAAe,YACf+E,sBAAuB,6BACvBgJ,WAAa,SAEbvK,UAAU,CACduQ,UAAU,EACVrQ,UAAU,EACVsT,QAAQ,GAGJjT,cAAc,CAClBgQ,SAAU,UACVrQ,SAAU,UACVsT,OAAQ,WAGJzW,kBAAkB,OAClB0W,cAAgB,kBAEhB1L,aAAc,oBACdC,cAAe,qBACfC,aAAc,oBACdC,eAAgB,sBAChB2I,gBAAiB,uBACjBjU,uBAAwB,8BACxBmU,sBAAuB,6BACvBC,sBAAyB,+BAEzBO,wBAAwB,gCACxBxT,uBAAuB,+BAQ7B,MAAM2V,kBAAkB9X,cACtBC,YAAY5N,EAAS2E,GACnB+P,MAAM1U,GAENwK,KAAK2K,QAAU3K,KAAK4K,WAAWzQ,GAC/B6F,KAAKmZ,UAAW,EAChBnZ,KAAKiZ,UAAYjZ,KAAKkZ,sBACtBlZ,KAAKmL,qBAKQpO,kBACb,OAAOA,OAGSwK,qBAChB,OAAOA,UAKT/B,OAAOxE,GACL,OAAOhB,KAAKmZ,SAAWnZ,KAAK4Q,OAAS5Q,KAAK6Q,KAAK7P,GAGjD6P,KAAK7P,GACChB,KAAKmZ,UAIS1Z,aAAawC,QAAQjC,KAAKqD,SAAUiM,aAAY,CAAEtO,cAAAA,IAEtDuB,mBAIdvC,KAAKmZ,UAAW,EAChBnZ,KAAKqD,SAASpI,MAAMI,WAAa,UAEjC2E,KAAKiZ,UAAUpI,OAEV7Q,KAAK2K,QAAQoQ,SAChBxB,OACAvZ,KAAKkb,uBAAuBlb,KAAKqD,WAGnCrD,KAAKqD,SAAS6C,gBAAgB,eAC9BlG,KAAKqD,SAASoC,aAAa,cAAc,GACzCzF,KAAKqD,SAASoC,aAAa,OAAQ,UACnCzF,KAAKqD,SAAS9H,UAAU0R,IAvEJ,QA6EpBjN,KAAK2D,eAJoB,KACvBlE,aAAawC,QAAQjC,KAAKqD,SAAUkM,cAAa,CAAEvO,cAAAA,KAGfhB,KAAKqD,UAAU,IAGvDuN,OACO5Q,KAAKmZ,WAIQ1Z,aAAawC,QAAQjC,KAAKqD,SAAUmM,cAExCjN,mBAId9C,aAAaC,IAAIjK,SAAU2iB,iBAC3BpY,KAAKqD,SAAS8X,OACdnb,KAAKmZ,UAAW,EAChBnZ,KAAKqD,SAAS9H,UAAU6C,OA9FJ,QA+FpB4B,KAAKiZ,UAAUrI,OAef5Q,KAAK2D,eAboB,KACvB3D,KAAKqD,SAASoC,aAAa,eAAe,GAC1CzF,KAAKqD,SAAS6C,gBAAgB,cAC9BlG,KAAKqD,SAAS6C,gBAAgB,QAC9BlG,KAAKqD,SAASpI,MAAMI,WAAa,SAE5B2E,KAAK2K,QAAQoQ,QAChBX,QAGF3a,aAAawC,QAAQjC,KAAKqD,SAAUoM,iBAGAzP,KAAKqD,UAAU,KAGvDE,UACEvD,KAAKiZ,UAAU1V,UACf2G,MAAM3G,UACN9D,aAAaC,IAAIjK,SAAU2iB,iBAK7BxN,WAAWzQ,GAOT,OANAA,EAAS,IACJoN,aACAxB,YAAYI,kBAAkBnG,KAAKqD,aAChB,iBAAXlJ,EAAsBA,EAAS,IAE5CF,gBAAgB8C,OAAM5C,EAAQ2N,eACvB3N,EAGT+e,sBACE,OAAO,IAAIzB,SAAS,CAClBzc,UAAWgF,KAAK2K,QAAQmN,SACxBlU,YAAY,EACZa,YAAazE,KAAKqD,SAAS7M,WAC3B8gB,cAAe,IAAMtX,KAAK4Q,SAI9BsK,uBAAuB1lB,GACrBiK,aAAaC,IAAIjK,SAAU2iB,iBAC3B3Y,aAAaiC,GAAGjM,SAAU2iB,gBAAe9Y,IACnC7J,WAAa6J,EAAMS,QACrBvK,IAAY8J,EAAMS,QACjBvK,EAAQgG,SAAS8D,EAAMS,SACxBvK,EAAQgf,UAGZhf,EAAQgf,QAGVrJ,qBACE1L,aAAaiC,GAAG1B,KAAKqD,SAAUiV,sBAAqBQ,wBAAuB,IAAM9Y,KAAK4Q,QAEtFnR,aAAaiC,GAAG1B,KAAKqD,SAAUkV,sBAAuBjZ,IAChDU,KAAK2K,QAAQlD,UA1KJ,WA0KgBnI,EAAM5B,KACjCsC,KAAK4Q,SAOW/M,uBAAC1J,GACrB,OAAO6F,KAAKiF,MAAK,WACf,MAAMC,EAAO1H,KAAKM,IAAIkC,KAAMsD,aAAa,IAAI2X,UAAUjb,KAAwB,iBAAX7F,EAAsBA,EAAS,IAEnG,GAAsB,iBAAXA,EAAX,CAIA,QAAqBihB,IAAjBlW,EAAK/K,IAAyBA,EAAO/B,WAAW,MAAmB,gBAAX+B,EAC1D,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C+K,EAAK/K,GAAQ6F,WAWnBP,aAAaiC,GAAGjM,SAAU0O,uBAAsBmB,wBAAsB,SAAUhG,GAC9E,MAAMS,EAASvH,uBAAuBwH,MAMtC,GAJI,CAAC,IAAK,QAAQ7H,SAAS6H,KAAKkN,UAC9B5N,EAAM2D,iBAGJ3H,WAAW0E,MACb,OAGFP,aAAakC,IAAI5B,EAAQ0P,eAAc,KAEjCzU,UAAUgF,OACZA,KAAKwU,UAKT,MAAM6G,EAAehmB,eAAeW,QAAQglB,eACxCK,GAAgBA,IAAiBtb,GACnCkb,UAAU/E,YAAYmF,GAAczK,QAGzBpT,KAAKM,IAAIiC,EAAQuD,aAAa,IAAI2X,UAAUlb,IAEpDyF,OAAOxF,SAGdP,aAAaiC,GAAG9I,OAAQkQ,sBAAqB,KAC3CzT,eAAeC,KAAK0lB,eAAezgB,QAAQ+gB,IAAO9d,KAAKM,IAAIwd,EAAIhY,aAAa,IAAI2X,UAAUK,IAAKzK,UASjGlU,mBAAmBse,WC1QnB,MAAMM,SAAW,IAAIvc,IAAI,CACvB,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAGIwc,uBAAyB,iBAOzBC,iBAAmB,6DAOnBC,iBAAmB,qIAEnBC,iBAAmB,CAACC,EAAMC,KAC9B,MAAMC,EAAWF,EAAKG,SAAStkB,cAE/B,GAAIokB,EAAqB1jB,SAAS2jB,GAChC,OAAIP,SAAS3d,IAAIke,IACRza,QAAQoa,iBAAiB5gB,KAAK+gB,EAAKI,YAAcN,iBAAiB7gB,KAAK+gB,EAAKI,YAMvF,MAAMC,EAASJ,EAAqB1lB,OAAO+lB,GAAaA,aAAqBthB,QAG7E,IAAK,IAAIqF,EAAI,EAAGK,EAAM2b,EAAOziB,OAAQyG,EAAIK,EAAKL,IAC5C,GAAIgc,EAAOhc,GAAGpF,KAAKihB,GACjB,OAAO,EAIX,OAAO,GAGIK,iBAAmB,CAE9BC,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAAQZ,wBAC5Ca,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJld,EAAG,GACHmd,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAGC,SAASC,aAAaC,EAAYC,EAAWC,GAClD,IAAKF,EAAW1kB,OACd,OAAO0kB,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAGpB,MACMG,GADY,IAAIzlB,OAAO0lB,WACKC,gBAAgBL,EAAY,aACxDM,EAAgBnkB,OAAOC,KAAK6jB,GAC5BM,EAAW,GAAG9oB,UAAU0oB,EAAgBhiB,KAAKvG,iBAAiB,MAEpE,IAAK,IAAImK,EAAI,EAAGK,EAAMme,EAASjlB,OAAQyG,EAAIK,EAAKL,IAAK,CACnD,MAAMqb,EAAKmD,EAASxe,GACdye,EAASpD,EAAGS,SAAStkB,cAE3B,IAAK+mB,EAAcrmB,SAASumB,GAAS,CACnCpD,EAAG9kB,WAAWwO,YAAYsW,GAE1B,SAGF,MAAMqD,EAAgB,GAAGhpB,UAAU2lB,EAAGlV,YAChCwY,EAAoB,GAAGjpB,OAAOwoB,EAAU,MAAQ,GAAIA,EAAUO,IAAW,IAE/EC,EAAcpkB,QAAQqhB,IACfD,iBAAiBC,EAAMgD,IAC1BtD,EAAGpV,gBAAgB0V,EAAKG,YAK9B,OAAOsC,EAAgBhiB,KAAKwiB,UC1F9B,MAAM9hB,OAAO,UACPuG,WAAW,aACXE,YAAa,cACbsb,eAAe,aACfC,qBAAqB,IAAInkB,OAAQ,wBAA6B,KAC9DokB,sBAAwB,IAAIhgB,IAAI,CAAC,WAAY,YAAa,eAE1D8I,cAAc,CAClBmX,UAAW,UACXC,SAAU,SACVC,MAAO,4BACPld,QAAS,SACTmd,MAAO,kBACPC,KAAM,UACN9pB,SAAU,mBACV6f,UAAW,oBACX3O,OAAQ,0BACRuK,UAAW,2BACXsO,mBAAoB,QACpBjM,SAAU,mBACVkM,YAAa,oBACbC,SAAU,UACVpB,WAAY,kBACZD,UAAW,SACX5K,aAAc,0BAGVkM,cAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOnjB,QAAU,OAAS,QAC1BojB,OAAQ,SACRC,KAAMrjB,QAAU,QAAU,QAGtB8K,UAAU,CACd0X,WAAW,EACXC,SAAU,+GAIVjd,QAAS,cACTkd,MAAO,GACPC,MAAO,EACPC,MAAM,EACN9pB,UAAU,EACV6f,UAAW,MACX3O,OAAQ,CAAC,EAAG,GACZuK,WAAW,EACXsO,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/CjM,SAAU,kBACVkM,YAAa,GACbC,UAAU,EACVpB,WAAY,KACZD,UAAWhC,iBACX5I,aAAc,MAGVna,QAAQ,CACZ2mB,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAGTnc,kBAAkB,OAClBoc,iBAAmB,QACnBnc,kBAAkB,OAElBoc,iBAAmB,OACnBC,gBAAkB,MAElBC,uBAAyB,iBAEzBC,cAAgB,QAChBC,cAAgB,QAChBC,cAAgB,QAChBC,eAAiB,SAQvB,MAAMC,gBAAgB9d,cACpBC,YAAY5N,EAAS2E,GACnB,QAAsB,IAAX6Z,OACT,MAAM,IAAIlZ,UAAU,+DAGtBoP,MAAM1U,GAGNwK,KAAKkhB,YAAa,EAClBlhB,KAAKmhB,SAAW,EAChBnhB,KAAKohB,YAAc,GACnBphB,KAAKqhB,eAAiB,GACtBrhB,KAAK0T,QAAU,KAGf1T,KAAK2K,QAAU3K,KAAK4K,WAAWzQ,GAC/B6F,KAAKshB,IAAM,KAEXthB,KAAKuhB,gBAKWha,qBAChB,OAAOA,UAGMxK,kBACb,OAAOA,OAGO3D,mBACd,OAAOA,QAGa0O,yBACpB,OAAOA,cAKT0Z,SACExhB,KAAKkhB,YAAa,EAGpBO,UACEzhB,KAAKkhB,YAAa,EAGpBQ,gBACE1hB,KAAKkhB,YAAclhB,KAAKkhB,WAG1B1b,OAAOlG,GACL,GAAKU,KAAKkhB,WAIV,GAAI5hB,EAAO,CACT,MAAMoW,EAAU1V,KAAK2hB,6BAA6BriB,GAElDoW,EAAQ2L,eAAelL,OAAST,EAAQ2L,eAAelL,MAEnDT,EAAQkM,uBACVlM,EAAQmM,OAAO,KAAMnM,GAErBA,EAAQoM,OAAO,KAAMpM,OAElB,CACL,GAAI1V,KAAK+hB,gBAAgBxmB,UAAUC,SAxFjB,QA0FhB,YADAwE,KAAK8hB,OAAO,KAAM9hB,MAIpBA,KAAK6hB,OAAO,KAAM7hB,OAItBuD,UACEuJ,aAAa9M,KAAKmhB,UAElB1hB,aAAaC,IAAIM,KAAKqD,SAASyB,QAAS,UAAwB,gBAAiB9E,KAAKgiB,mBAElFhiB,KAAKshB,KAAOthB,KAAKshB,IAAI9qB,YACvBwJ,KAAKshB,IAAI9qB,WAAWwO,YAAYhF,KAAKshB,KAGnCthB,KAAK0T,SACP1T,KAAK0T,QAAQgB,UAGfxK,MAAM3G,UAGRsN,OACE,GAAoC,SAAhC7Q,KAAKqD,SAASpI,MAAMG,QACtB,MAAM,IAAI0I,MAAM,uCAGlB,IAAM9D,KAAKiiB,kBAAmBjiB,KAAKkhB,WACjC,OAGF,MAAM5H,EAAY7Z,aAAawC,QAAQjC,KAAKqD,SAAUrD,KAAKoD,YAAYhK,MAAM6mB,MACvEiC,EAAavmB,eAAeqE,KAAKqD,UACjC8e,EAA4B,OAAfD,EACjBliB,KAAKqD,SAAS+e,cAAc1sB,gBAAgB8F,SAASwE,KAAKqD,UAC1D6e,EAAW1mB,SAASwE,KAAKqD,UAE3B,GAAIiW,EAAU/W,mBAAqB4f,EACjC,OAGF,MAAMb,EAAMthB,KAAK+hB,gBACXM,EAAQ3qB,OAAOsI,KAAKoD,YAAYrG,MAEtCukB,EAAI7b,aAAa,KAAM4c,GACvBriB,KAAKqD,SAASoC,aAAa,mBAAoB4c,GAE/CriB,KAAKsiB,aAEDtiB,KAAK2K,QAAQsU,WACfqC,EAAI/lB,UAAU0R,IA/II,QAkJpB,MAAMmI,EAA8C,mBAA3BpV,KAAK2K,QAAQyK,UACpCpV,KAAK2K,QAAQyK,UAAUrf,KAAKiK,KAAMshB,EAAKthB,KAAKqD,UAC5CrD,KAAK2K,QAAQyK,UAETmN,EAAaviB,KAAKwiB,eAAepN,GACvCpV,KAAKyiB,oBAAoBF,GAEzB,MAAMvR,UAAEA,GAAchR,KAAK2K,QAC3BnN,KAAKC,IAAI6jB,EAAKthB,KAAKoD,YAAYE,SAAUtD,MAEpCA,KAAKqD,SAAS+e,cAAc1sB,gBAAgB8F,SAASwE,KAAKshB,OAC7DtQ,EAAUiH,YAAYqJ,GACtB7hB,aAAawC,QAAQjC,KAAKqD,SAAUrD,KAAKoD,YAAYhK,MAAM+mB,WAGzDngB,KAAK0T,QACP1T,KAAK0T,QAAQiB,SAEb3U,KAAK0T,QAAUM,OAAOO,aAAavU,KAAKqD,SAAUie,EAAKthB,KAAKkU,iBAAiBqO,IAG/EjB,EAAI/lB,UAAU0R,IArKM,QAuKpB,MAAMsS,EAAkD,mBAA7Bvf,KAAK2K,QAAQ4U,YAA6Bvf,KAAK2K,QAAQ4U,cAAgBvf,KAAK2K,QAAQ4U,YAC3GA,GACF+B,EAAI/lB,UAAU0R,OAAOsS,EAAYlnB,MAAM,MAOrC,iBAAkB5C,SAASC,iBAC7B,GAAGC,UAAUF,SAAS4G,KAAKnG,UAAUqE,QAAQ/E,IAC3CiK,aAAaiC,GAAGlM,EAAS,YAAawG,QAI1C,MAWM4H,EAAa5D,KAAKshB,IAAI/lB,UAAUC,SAnMlB,QAoMpBwE,KAAK2D,eAZY,KACf,MAAM+e,EAAiB1iB,KAAKohB,YAE5BphB,KAAKohB,YAAc,KACnB3hB,aAAawC,QAAQjC,KAAKqD,SAAUrD,KAAKoD,YAAYhK,MAAM8mB,OAvLzC,QAyLdwC,GACF1iB,KAAK8hB,OAAO,KAAM9hB,OAKQA,KAAKshB,IAAK1d,GAG1CgN,OACE,IAAK5Q,KAAK0T,QACR,OAGF,MAAM4N,EAAMthB,KAAK+hB,gBAqBjB,GADkBtiB,aAAawC,QAAQjC,KAAKqD,SAAUrD,KAAKoD,YAAYhK,MAAM2mB,MAC/Dxd,iBACZ,OAGF+e,EAAI/lB,UAAU6C,OAnOM,QAuOhB,iBAAkB3I,SAASC,iBAC7B,GAAGC,UAAUF,SAAS4G,KAAKnG,UACxBqE,QAAQ/E,GAAWiK,aAAaC,IAAIlK,EAAS,YAAawG,OAG/DgE,KAAKqhB,eAAL,OAAqC,EACrCrhB,KAAKqhB,eAAL,OAAqC,EACrCrhB,KAAKqhB,eAAL,OAAqC,EAErC,MAAMzd,EAAa5D,KAAKshB,IAAI/lB,UAAUC,SAlPlB,QAmPpBwE,KAAK2D,eAtCY,KACX3D,KAAK4hB,yBA1MU,SA8Mf5hB,KAAKohB,aAAoCE,EAAI9qB,YAC/C8qB,EAAI9qB,WAAWwO,YAAYsc,GAG7BthB,KAAK2iB,iBACL3iB,KAAKqD,SAAS6C,gBAAgB,oBAC9BzG,aAAawC,QAAQjC,KAAKqD,SAAUrD,KAAKoD,YAAYhK,MAAM4mB,QAEvDhgB,KAAK0T,UACP1T,KAAK0T,QAAQgB,UACb1U,KAAK0T,QAAU,QAuBW1T,KAAKshB,IAAK1d,GACxC5D,KAAKohB,YAAc,GAGrBzM,SACuB,OAAjB3U,KAAK0T,SACP1T,KAAK0T,QAAQiB,SAMjBsN,gBACE,OAAO5gB,QAAQrB,KAAK4iB,YAGtBb,gBACE,GAAI/hB,KAAKshB,IACP,OAAOthB,KAAKshB,IAGd,MAAM9rB,EAAUC,SAASsiB,cAAc,OAIvC,OAHAviB,EAAQqpB,UAAY7e,KAAK2K,QAAQuU,SAEjClf,KAAKshB,IAAM9rB,EAAQU,SAAS,GACrB8J,KAAKshB,IAGdgB,aACE,MAAMhB,EAAMthB,KAAK+hB,gBACjB/hB,KAAK6iB,kBAAkBxtB,eAAeW,QA1QX,iBA0Q2CsrB,GAAMthB,KAAK4iB,YACjFtB,EAAI/lB,UAAU6C,OAlRM,OAEA,QAmRtBykB,kBAAkBrtB,EAASstB,GACzB,GAAgB,OAAZttB,EAIJ,OAAI6D,UAAUypB,IACZA,EAAUvpB,WAAWupB,QAGjB9iB,KAAK2K,QAAQ0U,KACXyD,EAAQtsB,aAAehB,IACzBA,EAAQqpB,UAAY,GACpBrpB,EAAQyiB,YAAY6K,IAGtBttB,EAAQutB,YAAcD,EAAQC,mBAM9B/iB,KAAK2K,QAAQ0U,MACXrf,KAAK2K,QAAQ6U,WACfsD,EAAU7E,aAAa6E,EAAS9iB,KAAK2K,QAAQwT,UAAWne,KAAK2K,QAAQyT,aAGvE5oB,EAAQqpB,UAAYiE,GAEpBttB,EAAQutB,YAAcD,GAI1BF,WACE,IAAIzD,EAAQnf,KAAKqD,SAASpL,aAAa,0BAQvC,OANKknB,IACHA,EAAsC,mBAAvBnf,KAAK2K,QAAQwU,MAC1Bnf,KAAK2K,QAAQwU,MAAMppB,KAAKiK,KAAKqD,UAC7BrD,KAAK2K,QAAQwU,OAGVA,EAGT6D,iBAAiBT,GACf,MAAmB,UAAfA,EACK,MAGU,SAAfA,EACK,QAGFA,EAKTZ,6BAA6BriB,EAAOoW,GAClC,MAAMuN,EAAUjjB,KAAKoD,YAAYE,SAQjC,OAPAoS,EAAUA,GAAWlY,KAAKM,IAAIwB,EAAMC,eAAgB0jB,MAGlDvN,EAAU,IAAI1V,KAAKoD,YAAY9D,EAAMC,eAAgBS,KAAKkjB,sBAC1D1lB,KAAKC,IAAI6B,EAAMC,eAAgB0jB,EAASvN,IAGnCA,EAGTV,aACE,MAAMvO,OAAEA,GAAWzG,KAAK2K,QAExB,MAAsB,iBAAXlE,EACFA,EAAOpO,MAAM,KAAK4c,IAAItP,GAAO5M,OAAOkV,SAAStI,EAAK,KAGrC,mBAAXc,EACFyO,GAAczO,EAAOyO,EAAYlV,KAAKqD,UAGxCoD,EAGTyN,iBAAiBqO,GACf,MAAMpN,EAAwB,CAC5BC,UAAWmN,EACXnO,UAAW,CACT,CACEtX,KAAM,OACNuY,QAAS,CACPiK,mBAAoBtf,KAAK2K,QAAQ2U,qBAGrC,CACExiB,KAAM,SACNuY,QAAS,CACP5O,OAAQzG,KAAKgV,eAGjB,CACElY,KAAM,kBACNuY,QAAS,CACPhC,SAAUrT,KAAK2K,QAAQ0I,WAG3B,CACEvW,KAAM,QACNuY,QAAS,CACP7f,QAAU,IAAGwK,KAAKoD,YAAYrG,eAGlC,CACED,KAAM,WACNwX,SAAS,EACT6O,MAAO,aACPlmB,GAAIiI,GAAQlF,KAAKojB,6BAA6Ble,KAGlDme,cAAene,IACTA,EAAKmQ,QAAQD,YAAclQ,EAAKkQ,WAClCpV,KAAKojB,6BAA6Ble,KAKxC,MAAO,IACFiQ,KACsC,mBAA9BnV,KAAK2K,QAAQ4I,aAA8BvT,KAAK2K,QAAQ4I,aAAa4B,GAAyBnV,KAAK2K,QAAQ4I,cAI1HkP,oBAAoBF,GAClBviB,KAAK+hB,gBAAgBxmB,UAAU0R,IAAK,cAAkBjN,KAAKgjB,iBAAiBT,IAG9EC,eAAepN,GACb,OAAOqK,cAAcrK,EAAUra,eAGjCwmB,gBACmBvhB,KAAK2K,QAAQ1I,QAAQ5J,MAAM,KAEnCkC,QAAQ0H,IACf,GAAgB,UAAZA,EACFxC,aAAaiC,GAAG1B,KAAKqD,SAAUrD,KAAKoD,YAAYhK,MAAMgnB,MAAOpgB,KAAK2K,QAAQpV,SAAU+J,GAASU,KAAKwF,OAAOlG,SACpG,GA3ZU,WA2ZN2C,EAA4B,CACrC,MAAMqhB,EA/ZQ,UA+ZErhB,EACdjC,KAAKoD,YAAYhK,MAAMmnB,WACvBvgB,KAAKoD,YAAYhK,MAAMinB,QACnBkD,EAlaQ,UAkaGthB,EACfjC,KAAKoD,YAAYhK,MAAMonB,WACvBxgB,KAAKoD,YAAYhK,MAAMknB,SAEzB7gB,aAAaiC,GAAG1B,KAAKqD,SAAUigB,EAAStjB,KAAK2K,QAAQpV,SAAU+J,GAASU,KAAK6hB,OAAOviB,IACpFG,aAAaiC,GAAG1B,KAAKqD,SAAUkgB,EAAUvjB,KAAK2K,QAAQpV,SAAU+J,GAASU,KAAK8hB,OAAOxiB,OAIzFU,KAAKgiB,kBAAoB,KACnBhiB,KAAKqD,UACPrD,KAAK4Q,QAITnR,aAAaiC,GAAG1B,KAAKqD,SAASyB,QAAS,UAAwB,gBAAiB9E,KAAKgiB,mBAEjFhiB,KAAK2K,QAAQpV,SACfyK,KAAK2K,QAAU,IACV3K,KAAK2K,QACR1I,QAAS,SACT1M,SAAU,IAGZyK,KAAKwjB,YAITA,YACE,MAAMrE,EAAQnf,KAAKqD,SAASpL,aAAa,SACnCwrB,SAA2BzjB,KAAKqD,SAASpL,aAAa,2BAExDknB,GAA+B,WAAtBsE,KACXzjB,KAAKqD,SAASoC,aAAa,yBAA0B0Z,GAAS,KAC1DA,GAAUnf,KAAKqD,SAASpL,aAAa,eAAkB+H,KAAKqD,SAAS0f,aACvE/iB,KAAKqD,SAASoC,aAAa,aAAc0Z,GAG3Cnf,KAAKqD,SAASoC,aAAa,QAAS,KAIxCoc,OAAOviB,EAAOoW,GACZA,EAAU1V,KAAK2hB,6BAA6BriB,EAAOoW,GAE/CpW,IACFoW,EAAQ2L,eACS,YAAf/hB,EAAMK,KAhdQ,QADA,UAkdZ,GAGF+V,EAAQqM,gBAAgBxmB,UAAUC,SA5dlB,SAEC,SA0d8Cka,EAAQ0L,YACzE1L,EAAQ0L,YA3dW,QA+drBtU,aAAa4I,EAAQyL,UAErBzL,EAAQ0L,YAjea,OAmehB1L,EAAQ/K,QAAQyU,OAAU1J,EAAQ/K,QAAQyU,MAAMvO,KAKrD6E,EAAQyL,SAAWnnB,WAAW,KAxeT,SAyef0b,EAAQ0L,aACV1L,EAAQ7E,QAET6E,EAAQ/K,QAAQyU,MAAMvO,MARvB6E,EAAQ7E,QAWZiR,OAAOxiB,EAAOoW,GACZA,EAAU1V,KAAK2hB,6BAA6BriB,EAAOoW,GAE/CpW,IACFoW,EAAQ2L,eACS,aAAf/hB,EAAMK,KA9eQ,QADA,SAgfZ+V,EAAQrS,SAAS7H,SAAS8D,EAAM0B,gBAGlC0U,EAAQkM,yBAIZ9U,aAAa4I,EAAQyL,UAErBzL,EAAQ0L,YA7fY,MA+ff1L,EAAQ/K,QAAQyU,OAAU1J,EAAQ/K,QAAQyU,MAAMxO,KAKrD8E,EAAQyL,SAAWnnB,WAAW,KApgBV,QAqgBd0b,EAAQ0L,aACV1L,EAAQ9E,QAET8E,EAAQ/K,QAAQyU,MAAMxO,MARvB8E,EAAQ9E,QAWZgR,uBACE,IAAK,MAAM3f,KAAWjC,KAAKqhB,eACzB,GAAIrhB,KAAKqhB,eAAepf,GACtB,OAAO,EAIX,OAAO,EAGT2I,WAAWzQ,GACT,MAAMupB,EAAiB3d,YAAYI,kBAAkBnG,KAAKqD,UAqC1D,OAnCAhJ,OAAOC,KAAKopB,GAAgBnpB,QAAQopB,IAC9B3E,sBAAsBphB,IAAI+lB,WACrBD,EAAeC,MAI1BxpB,EAAS,IACJ6F,KAAKoD,YAAYmE,WACjBmc,KACmB,iBAAXvpB,GAAuBA,EAASA,EAAS,KAG/C6W,WAAiC,IAArB7W,EAAO6W,UAAsBvb,SAAS4G,KAAO9C,WAAWY,EAAO6W,WAEtD,iBAAjB7W,EAAOilB,QAChBjlB,EAAOilB,MAAQ,CACbvO,KAAM1W,EAAOilB,MACbxO,KAAMzW,EAAOilB,QAIW,iBAAjBjlB,EAAOglB,QAChBhlB,EAAOglB,MAAQhlB,EAAOglB,MAAM5nB,YAGA,iBAAnB4C,EAAO2oB,UAChB3oB,EAAO2oB,QAAU3oB,EAAO2oB,QAAQvrB,YAGlC0C,gBAAgB8C,OAAM5C,EAAQ6F,KAAKoD,YAAY0E,aAE3C3N,EAAOqlB,WACTrlB,EAAO+kB,SAAWjB,aAAa9jB,EAAO+kB,SAAU/kB,EAAOgkB,UAAWhkB,EAAOikB,aAGpEjkB,EAGT+oB,qBACE,MAAM/oB,EAAS,GAEf,GAAI6F,KAAK2K,QACP,IAAK,MAAMjN,KAAOsC,KAAK2K,QACjB3K,KAAKoD,YAAYmE,QAAQ7J,KAASsC,KAAK2K,QAAQjN,KACjDvD,EAAOuD,GAAOsC,KAAK2K,QAAQjN,IAKjC,OAAOvD,EAGTwoB,iBACE,MAAMrB,EAAMthB,KAAK+hB,gBACX6B,EAAWtC,EAAIrpB,aAAa,SAAST,MAAMunB,sBAChC,OAAb6E,GAAqBA,EAASpqB,OAAS,GACzCoqB,EAAS3O,IAAI4O,GAASA,EAAMvrB,QACzBiC,QAAQupB,GAAUxC,EAAI/lB,UAAU6C,OAAO0lB,IAI9CV,6BAA6BlO,GAC3B,MAAM6O,MAAEA,GAAU7O,EAEb6O,IAIL/jB,KAAKshB,IAAMyC,EAAMtF,SAASuF,OAC1BhkB,KAAK2iB,iBACL3iB,KAAKyiB,oBAAoBziB,KAAKwiB,eAAeuB,EAAM3O,aAK/BvR,uBAAC1J,GACrB,OAAO6F,KAAKiF,MAAK,WACf,IAAIC,EAAO1H,KAAKM,IAAIkC,KAAMsD,YAC1B,MAAMqH,EAA4B,iBAAXxQ,GAAuBA,EAE9C,IAAK+K,IAAQ,eAAerK,KAAKV,MAI5B+K,IACHA,EAAO,IAAI+b,QAAQjhB,KAAM2K,IAGL,iBAAXxQ,GAAqB,CAC9B,QAA4B,IAAjB+K,EAAK/K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C+K,EAAK/K,UAabwC,mBAAmBskB,SC/tBnB,MAAMlkB,OAAO,UACPuG,WAAW,aACXE,YAAa,cACbsb,aAAe,aACfC,mBAAqB,IAAInkB,OAAQ,wBAA6B,KAE9D2M,UAAU,IACX0Z,QAAQ1Z,QACX6N,UAAW,QACX3O,OAAQ,CAAC,EAAG,GACZxE,QAAS,QACT6gB,QAAS,GACT5D,SAAU,+IAONpX,cAAc,IACfmZ,QAAQnZ,YACXgb,QAAS,6BAGL1pB,QAAQ,CACZ2mB,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAGTnc,kBAAkB,OAClBC,kBAAkB,OAElB2f,eAAiB,kBACjBC,iBAAmB,gBAQzB,MAAMC,gBAAgBlD,QAGF1Z,qBAChB,OAAOA,UAGMxK,kBACb,OAAOA,OAGO3D,mBACd,OAAOA,QAGa0O,yBACpB,OAAOA,cAKTma,gBACE,OAAOjiB,KAAK4iB,YAAc5iB,KAAKokB,cAGjC9B,aACE,MAAMhB,EAAMthB,KAAK+hB,gBAGjB/hB,KAAK6iB,kBAAkBxtB,eAAeW,QAAQiuB,eAAgB3C,GAAMthB,KAAK4iB,YACzE,IAAIE,EAAU9iB,KAAKokB,cACI,mBAAZtB,IACTA,EAAUA,EAAQ/sB,KAAKiK,KAAKqD,WAG9BrD,KAAK6iB,kBAAkBxtB,eAAeW,QA3CjB,gBA2C2CsrB,GAAMwB,GAEtExB,EAAI/lB,UAAU6C,OAjDM,OACA,QAqDtBqkB,oBAAoBF,GAClBviB,KAAK+hB,gBAAgBxmB,UAAU0R,IAAK,cAAkBjN,KAAKgjB,iBAAiBT,IAG9E6B,cACE,OAAOpkB,KAAKqD,SAASpL,aAAa,oBAAsB+H,KAAK2K,QAAQmY,QAGvEH,iBACE,MAAMrB,EAAMthB,KAAK+hB,gBACX6B,EAAWtC,EAAIrpB,aAAa,SAAST,MAAMunB,oBAChC,OAAb6E,GAAqBA,EAASpqB,OAAS,GACzCoqB,EAAS3O,IAAI4O,GAASA,EAAMvrB,QACzBiC,QAAQupB,GAAUxC,EAAI/lB,UAAU6C,OAAO0lB,IAMxBjgB,uBAAC1J,GACrB,OAAO6F,KAAKiF,MAAK,WACf,IAAIC,EAAO1H,KAAKM,IAAIkC,KAAMsD,YAC1B,MAAMqH,EAA4B,iBAAXxQ,EAAsBA,EAAS,KAEtD,IAAK+K,IAAQ,eAAerK,KAAKV,MAI5B+K,IACHA,EAAO,IAAIif,QAAQnkB,KAAM2K,GACzBnN,KAAKC,IAAIuC,KAAMsD,WAAU4B,IAGL,iBAAX/K,GAAqB,CAC9B,QAA4B,IAAjB+K,EAAK/K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C+K,EAAK/K,UAabwC,mBAAmBwnB,SCvInB,MAAMpnB,OAAO,YACPuG,WAAW,eACXE,YAAa,gBACbO,eAAe,YAEfwD,UAAU,CACdd,OAAQ,GACR4d,OAAQ,OACRtkB,OAAQ,IAGJ+H,cAAc,CAClBrB,OAAQ,SACR4d,OAAQ,SACRtkB,OAAQ,oBAGJukB,eAAkB,wBAClBC,aAAgB,sBAChBzb,oBAAuB,6BAEvB0b,yBAA2B,gBAC3Bnf,oBAAoB,SAEpBof,kBAAoB,yBACpBC,0BAA0B,oBAC1BC,mBAAqB,YACrBC,mBAAqB,YACrBC,oBAAsB,mBACtBC,oBAAoB,YACpBC,2BAA2B,mBAE3BC,cAAgB,SAChBC,gBAAkB,WAQxB,MAAMC,kBAAkB/hB,cACtBC,YAAY5N,EAAS2E,GACnB+P,MAAM1U,GACNwK,KAAKmlB,eAA2C,SAA1BnlB,KAAKqD,SAAS6J,QAAqBtU,OAASoH,KAAKqD,SACvErD,KAAK2K,QAAU3K,KAAK4K,WAAWzQ,GAC/B6F,KAAKwQ,UAAa,GAAExQ,KAAK2K,QAAQ5K,qBAAiCC,KAAK2K,QAAQ5K,4BAAkCC,KAAK2K,QAAQ5K,wBAC9HC,KAAKolB,SAAW,GAChBplB,KAAKqlB,SAAW,GAChBrlB,KAAKslB,cAAgB,KACrBtlB,KAAKulB,cAAgB,EAErB9lB,aAAaiC,GAAG1B,KAAKmlB,eAAgBZ,aAAc,IAAMvkB,KAAKwlB,YAE9DxlB,KAAKylB,UACLzlB,KAAKwlB,WAKWje,qBAChB,OAAOA,UAGMxK,kBACb,OAAOA,OAKT0oB,UACE,MAAMC,EAAa1lB,KAAKmlB,iBAAmBnlB,KAAKmlB,eAAevsB,OAvC7C,SACE,WA0Cd+sB,EAAuC,SAAxB3lB,KAAK2K,QAAQ0Z,OAChCqB,EACA1lB,KAAK2K,QAAQ0Z,OAETuB,EA9Cc,aA8CDD,EACjB3lB,KAAK6lB,gBACL,EAEF7lB,KAAKolB,SAAW,GAChBplB,KAAKqlB,SAAW,GAChBrlB,KAAKulB,cAAgBvlB,KAAK8lB,mBAEVzwB,eAAeC,KAAK0K,KAAKwQ,WAEjCyE,IAAIzf,IACV,MAAMuwB,EAAiBxtB,uBAAuB/C,GACxCuK,EAASgmB,EAAiB1wB,eAAeW,QAAQ+vB,GAAkB,KAEzE,GAAIhmB,EAAQ,CACV,MAAMimB,EAAYjmB,EAAO4G,wBACzB,GAAIqf,EAAUrP,OAASqP,EAAUC,OAC/B,MAAO,CACLlgB,YAAY4f,GAAc5lB,GAAQ6G,IAAMgf,EACxCG,GAKN,OAAO,OAEN5vB,OAAO+vB,GAAQA,GACfC,KAAK,CAAC9J,EAAGE,IAAMF,EAAE,GAAKE,EAAE,IACxBhiB,QAAQ2rB,IACPlmB,KAAKolB,SAASxuB,KAAKsvB,EAAK,IACxBlmB,KAAKqlB,SAASzuB,KAAKsvB,EAAK,MAI9B3iB,UACE9D,aAAaC,IAAIM,KAAKmlB,eAAgB3hB,aACtC0G,MAAM3G,UAKRqH,WAAWzQ,GAOT,GAA6B,iBAN7BA,EAAS,IACJoN,aACAxB,YAAYI,kBAAkBnG,KAAKqD,aAChB,iBAAXlJ,GAAuBA,EAASA,EAAS,KAGpC4F,QAAuB1G,UAAUc,EAAO4F,QAAS,CACjE,IAAIoQ,GAAEA,GAAOhW,EAAO4F,OACfoQ,IACHA,EAAKzY,OAAOqF,QACZ5C,EAAO4F,OAAOoQ,GAAKA,GAGrBhW,EAAO4F,OAAU,IAAGoQ,EAKtB,OAFAlW,gBAAgB8C,OAAM5C,EAAQ2N,eAEvB3N,EAGT0rB,gBACE,OAAO7lB,KAAKmlB,iBAAmBvsB,OAC7BoH,KAAKmlB,eAAeiB,YACpBpmB,KAAKmlB,eAAete,UAGxBif,mBACE,OAAO9lB,KAAKmlB,eAAe5K,cAAgB3iB,KAAKyuB,IAC9C5wB,SAAS4G,KAAKke,aACd9kB,SAASC,gBAAgB6kB,cAI7B+L,mBACE,OAAOtmB,KAAKmlB,iBAAmBvsB,OAC7BA,OAAO2tB,YACPvmB,KAAKmlB,eAAexe,wBAAwBsf,OAGhDT,WACE,MAAM3e,EAAY7G,KAAK6lB,gBAAkB7lB,KAAK2K,QAAQlE,OAChD8T,EAAeva,KAAK8lB,mBACpBU,EAAYxmB,KAAK2K,QAAQlE,OAAS8T,EAAeva,KAAKsmB,mBAM5D,GAJItmB,KAAKulB,gBAAkBhL,GACzBva,KAAKylB,UAGH5e,GAAa2f,EAAjB,CACE,MAAMzmB,EAASC,KAAKqlB,SAASrlB,KAAKqlB,SAAS7rB,OAAS,GAEhDwG,KAAKslB,gBAAkBvlB,GACzBC,KAAKymB,UAAU1mB,OAJnB,CAUA,GAAIC,KAAKslB,eAAiBze,EAAY7G,KAAKolB,SAAS,IAAMplB,KAAKolB,SAAS,GAAK,EAG3E,OAFAplB,KAAKslB,cAAgB,UACrBtlB,KAAK0mB,SAIP,IAAK,IAAIzmB,EAAID,KAAKolB,SAAS5rB,OAAQyG,KACVD,KAAKslB,gBAAkBtlB,KAAKqlB,SAASplB,IACxD4G,GAAa7G,KAAKolB,SAASnlB,UACM,IAAzBD,KAAKolB,SAASnlB,EAAI,IAAsB4G,EAAY7G,KAAKolB,SAASnlB,EAAI,KAGhFD,KAAKymB,UAAUzmB,KAAKqlB,SAASplB,KAKnCwmB,UAAU1mB,GACRC,KAAKslB,cAAgBvlB,EAErBC,KAAK0mB,SAEL,MAAMC,EAAU3mB,KAAKwQ,UAAUnY,MAAM,KAClC4c,IAAI1f,GAAa,GAAEA,qBAA4BwK,OAAYxK,WAAkBwK,OAE1E6mB,EAAOvxB,eAAeW,QAAQ2wB,EAAQE,KAAK,MAE7CD,EAAKrrB,UAAUC,SA1LU,kBA2L3BnG,eAAeW,QAlLY,mBAkLsB4wB,EAAK9hB,QAnLlC,cAoLjBvJ,UAAU0R,IA3LO,UA6LpB2Z,EAAKrrB,UAAU0R,IA7LK,YAgMpB2Z,EAAKrrB,UAAU0R,IAhMK,UAkMpB5X,eAAeiB,QAAQswB,EA/LG,qBAgMvBrsB,QAAQusB,IAGPzxB,eAAewB,KAAKiwB,EAAY,+BAC7BvsB,QAAQ2rB,GAAQA,EAAK3qB,UAAU0R,IAvMlB,WA0MhB5X,eAAewB,KAAKiwB,EArMH,aAsMdvsB,QAAQwsB,IACP1xB,eAAea,SAAS6wB,EAxMX,aAyMVxsB,QAAQ2rB,GAAQA,EAAK3qB,UAAU0R,IA7MtB,gBAkNtBxN,aAAawC,QAAQjC,KAAKmlB,eAAgBb,eAAgB,CACxDtjB,cAAejB,IAInB2mB,SACErxB,eAAeC,KAAK0K,KAAKwQ,WACtBra,OAAO6wB,GAAQA,EAAKzrB,UAAUC,SAzNX,WA0NnBjB,QAAQysB,GAAQA,EAAKzrB,UAAU6C,OA1NZ,WA+NFyF,uBAAC1J,GACrB,OAAO6F,KAAKiF,MAAK,WACf,MAAMC,EAAOggB,UAAUhP,YAAYlW,OAAS,IAAIklB,UAAUllB,KAAwB,iBAAX7F,EAAsBA,EAAS,IAEtG,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjB+K,EAAK/K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C+K,EAAK/K,UAWXsF,aAAaiC,GAAG9I,OAAQkQ,oBAAqB,KAC3CzT,eAAeC,KAAKmvB,mBACjBlqB,QAAQ0sB,GAAO,IAAI/B,UAAU+B,MAUlCtqB,mBAAmBuoB,WCzRnB,MAAMnoB,OAAO,MACPuG,WAAW,SACXE,YAAa,UACbO,aAAe,YAEfyL,aAAc,cACdC,eAAgB,gBAChBH,aAAc,cACdC,cAAe,eACfpL,qBAAwB,wBAExB+iB,yBAA2B,gBAC3B7hB,kBAAoB,SACpBhB,kBAAkB,OAClBC,kBAAkB,OAElBwgB,kBAAoB,YACpBJ,wBAA0B,oBAC1Bpb,gBAAkB,UAClB6d,mBAAqB,wBACrB7hB,qBAAuB,2EACvByf,yBAA2B,mBAC3BqC,+BAAiC,kCAQvC,MAAMC,YAAYlkB,cAGDpG,kBACb,MAlCS,MAuCX8T,OACE,GAAK7Q,KAAKqD,SAAS7M,YACjBwJ,KAAKqD,SAAS7M,WAAWC,WAAaC,KAAKC,cAC3CqJ,KAAKqD,SAAS9H,UAAUC,SA9BJ,UA+BpB,OAGF,IAAI1E,EACJ,MAAMiJ,EAASvH,uBAAuBwH,KAAKqD,UACrCikB,EAActnB,KAAKqD,SAASyB,QA/BN,qBAiC5B,GAAIwiB,EAAa,CACf,MAAMC,EAAwC,OAAzBD,EAAYvL,UAA8C,OAAzBuL,EAAYvL,SAAoBoL,mBAjCpE,UAkClBrwB,EAAWzB,eAAeC,KAAKiyB,EAAcD,GAC7CxwB,EAAWA,EAASA,EAAS0C,OAAS,GAGxC,MAAMguB,EAAY1wB,EAChB2I,aAAawC,QAAQnL,EAAU0Y,aAAY,CACzCxO,cAAehB,KAAKqD,WAEtB,KAMF,GAJkB5D,aAAawC,QAAQjC,KAAKqD,SAAUiM,aAAY,CAChEtO,cAAelK,IAGHyL,kBAAmC,OAAdilB,GAAsBA,EAAUjlB,iBACjE,OAGFvC,KAAKymB,UAAUzmB,KAAKqD,SAAUikB,GAE9B,MAAMG,EAAW,KACfhoB,aAAawC,QAAQnL,EAAU2Y,eAAc,CAC3CzO,cAAehB,KAAKqD,WAEtB5D,aAAawC,QAAQjC,KAAKqD,SAAUkM,cAAa,CAC/CvO,cAAelK,KAIfiJ,EACFC,KAAKymB,UAAU1mB,EAAQA,EAAOvJ,WAAYixB,GAE1CA,IAMJhB,UAAUjxB,EAASwb,EAAWzU,GAC5B,MAIMmrB,IAJiB1W,GAAqC,OAAvBA,EAAU+K,UAA4C,OAAvB/K,EAAU+K,SAE5E1mB,eAAea,SAAS8a,EA3EN,WA0ElB3b,eAAeC,KAAK6xB,mBAAoBnW,IAGZ,GACxBS,EAAkBlV,GAAamrB,GAAUA,EAAOnsB,UAAUC,SAnF5C,QAqFdisB,EAAW,IAAMznB,KAAK2nB,oBAAoBnyB,EAASkyB,EAAQnrB,GAE7DmrB,GAAUjW,GACZiW,EAAOnsB,UAAU6C,OAvFC,QAwFlB4B,KAAK2D,eAAe8jB,EAAUjyB,GAAS,IAEvCiyB,IAIJE,oBAAoBnyB,EAASkyB,EAAQnrB,GACnC,GAAImrB,EAAQ,CACVA,EAAOnsB,UAAU6C,OAlGG,UAoGpB,MAAMwpB,EAAgBvyB,eAAeW,QAAQoxB,+BAAgCM,EAAOlxB,YAEhFoxB,GACFA,EAAcrsB,UAAU6C,OAvGN,UA0GgB,QAAhCspB,EAAOzvB,aAAa,SACtByvB,EAAOjiB,aAAa,iBAAiB,GAIzCjQ,EAAQ+F,UAAU0R,IA/GI,UAgHe,QAAjCzX,EAAQyC,aAAa,SACvBzC,EAAQiQ,aAAa,iBAAiB,GAGxCxJ,OAAOzG,GAEHA,EAAQ+F,UAAUC,SArHF,SAsHlBhG,EAAQ+F,UAAU0R,IArHA,QAwHpB,IAAIoC,EAAS7Z,EAAQgB,WAKrB,GAJI6Y,GAA8B,OAApBA,EAAO0M,WACnB1M,EAASA,EAAO7Y,YAGd6Y,GAAUA,EAAO9T,UAAUC,SAhIF,iBAgIsC,CACjE,MAAMqsB,EAAkBryB,EAAQsP,QA5HZ,aA8HhB+iB,GACFxyB,eAAeC,KA1HU,mBA0HqBuyB,GAC3CttB,QAAQutB,GAAYA,EAASvsB,UAAU0R,IApIxB,WAuIpBzX,EAAQiQ,aAAa,iBAAiB,GAGpClJ,GACFA,IAMkBsH,uBAAC1J,GACrB,OAAO6F,KAAKiF,MAAK,WACf,MAAMC,EAAO1H,KAAKM,IAAIkC,KA9JX,WA8J8B,IAAIqnB,IAAIrnB,MAEjD,GAAsB,iBAAX7F,EAAqB,CAC9B,QAA4B,IAAjB+K,EAAK/K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C+K,EAAK/K,UAYbsF,aAAaiC,GAAGjM,SAAU0O,qBAAsBmB,sBAAsB,SAAUhG,GAC1E,CAAC,IAAK,QAAQnH,SAAS6H,KAAKkN,UAC9B5N,EAAM2D,iBAGJ3H,WAAW0E,QAIFxC,KAAKM,IAAIkC,KA1LP,WA0L0B,IAAIqnB,IAAIrnB,OAC5C6Q,UAUPlU,mBAAmB0qB,KCvMnB,MAAMtqB,KAAO,QACPuG,SAAW,WACXE,UAAa,YAEb8U,oBAAuB,yBACvByP,gBAAmB,qBACnBC,eAAkB,oBAClB5P,cAAiB,mBACjB6P,eAAkB,oBAClBzY,WAAc,gBACdC,aAAgB,kBAChBH,WAAc,gBACdC,YAAe,iBAEflL,gBAAkB,OAClB6jB,gBAAkB,OAClB5jB,gBAAkB,OAClB6jB,mBAAqB,UAErBrgB,YAAc,CAClBmX,UAAW,UACXmJ,SAAU,UACVhJ,MAAO,UAGH7X,QAAU,CACd0X,WAAW,EACXmJ,UAAU,EACVhJ,MAAO,KAGHtG,sBAAwB,4BAQ9B,MAAMuP,cAAcllB,cAClBC,YAAY5N,EAAS2E,GACnB+P,MAAM1U,GAENwK,KAAK2K,QAAU3K,KAAK4K,WAAWzQ,GAC/B6F,KAAKmhB,SAAW,KAChBnhB,KAAKsoB,sBAAuB,EAC5BtoB,KAAKuoB,yBAA0B,EAC/BvoB,KAAKuhB,gBAKezZ,yBACpB,OAAOA,YAGSP,qBAChB,OAAOA,QAGMxK,kBACb,OAAOA,KAKT8T,OACoBpR,aAAawC,QAAQjC,KAAKqD,SAAUiM,YAExC/M,mBAIdvC,KAAKwoB,gBAEDxoB,KAAK2K,QAAQsU,WACfjf,KAAKqD,SAAS9H,UAAU0R,IA9DN,QA0EpBjN,KAAKqD,SAAS9H,UAAU6C,OAzEJ,QA0EpBnC,OAAO+D,KAAKqD,UACZrD,KAAKqD,SAAS9H,UAAU0R,IAzED,WA2EvBjN,KAAK2D,eAbY,KACf3D,KAAKqD,SAAS9H,UAAU6C,OA/DH,WAgErB4B,KAAKqD,SAAS9H,UAAU0R,IAjEN,QAmElBxN,aAAawC,QAAQjC,KAAKqD,SAAUkM,aAEpCvP,KAAKyoB,sBAOuBzoB,KAAKqD,SAAUrD,KAAK2K,QAAQsU,YAG5DrO,OACO5Q,KAAKqD,SAAS9H,UAAUC,SAhFT,UAoFFiE,aAAawC,QAAQjC,KAAKqD,SAAUmM,YAExCjN,mBASdvC,KAAKqD,SAAS9H,UAAU6C,OA/FJ,QAgGpB4B,KAAK2D,eANY,KACf3D,KAAKqD,SAAS9H,UAAU0R,IA5FN,QA6FlBxN,aAAawC,QAAQjC,KAAKqD,SAAUoM,eAIRzP,KAAKqD,SAAUrD,KAAK2K,QAAQsU,aAG5D1b,UACEvD,KAAKwoB,gBAEDxoB,KAAKqD,SAAS9H,UAAUC,SAtGR,SAuGlBwE,KAAKqD,SAAS9H,UAAU6C,OAvGN,QA0GpB8L,MAAM3G,UAKRqH,WAAWzQ,GAST,OARAA,EAAS,IACJoN,WACAxB,YAAYI,kBAAkBnG,KAAKqD,aAChB,iBAAXlJ,GAAuBA,EAASA,EAAS,IAGtDF,gBAAgB8C,KAAM5C,EAAQ6F,KAAKoD,YAAY0E,aAExC3N,EAGTsuB,qBACOzoB,KAAK2K,QAAQyd,WAIdpoB,KAAKsoB,sBAAwBtoB,KAAKuoB,0BAItCvoB,KAAKmhB,SAAWnnB,WAAW,KACzBgG,KAAK4Q,QACJ5Q,KAAK2K,QAAQyU,SAGlBsJ,eAAeppB,EAAOqpB,GACpB,OAAQrpB,EAAMK,MACZ,IAAK,YACL,IAAK,WACHK,KAAKsoB,qBAAuBK,EAC5B,MACF,IAAK,UACL,IAAK,WACH3oB,KAAKuoB,wBAA0BI,EAMnC,GAAIA,EAEF,YADA3oB,KAAKwoB,gBAIP,MAAMja,EAAcjP,EAAM0B,cACtBhB,KAAKqD,WAAakL,GAAevO,KAAKqD,SAAS7H,SAAS+S,IAI5DvO,KAAKyoB,qBAGPlH,gBACE9hB,aAAaiC,GAAG1B,KAAKqD,SAAUiV,oBAAqBQ,sBAAuB,IAAM9Y,KAAK4Q,QACtFnR,aAAaiC,GAAG1B,KAAKqD,SAAU0kB,gBAAiBzoB,GAASU,KAAK0oB,eAAeppB,GAAO,IACpFG,aAAaiC,GAAG1B,KAAKqD,SAAU2kB,eAAgB1oB,GAASU,KAAK0oB,eAAeppB,GAAO,IACnFG,aAAaiC,GAAG1B,KAAKqD,SAAU+U,cAAe9Y,GAASU,KAAK0oB,eAAeppB,GAAO,IAClFG,aAAaiC,GAAG1B,KAAKqD,SAAU4kB,eAAgB3oB,GAASU,KAAK0oB,eAAeppB,GAAO,IAGrFkpB,gBACE1b,aAAa9M,KAAKmhB,UAClBnhB,KAAKmhB,SAAW,KAKItd,uBAAC1J,GACrB,OAAO6F,KAAKiF,MAAK,WACf,IAAIC,EAAO1H,KAAKM,IAAIkC,KAAMsD,UAO1B,GAJK4B,IACHA,EAAO,IAAImjB,MAAMroB,KAHe,iBAAX7F,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB+K,EAAK/K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C+K,EAAK/K,GAAQ6F,WAarBrD,mBAAmB0rB", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst customEventsRegex = /^(mouseenter|mouseleave)/i\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            // eslint-disable-next-line unicorn/consistent-destructuring\n            EventHandler.off(element, event.type, selector, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  let typeEvent = getTypeEvent(originalTypeEvent)\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (customEventsRegex.test(originalTypeEvent)) {\n    const wrapFn = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    if (delegationFn) {\n      delegationFn = wrapFn(delegationFn)\n    } else {\n      handler = wrapFn(handler)\n    }\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport {\n  emulateTransitionEnd,\n  execute,\n  getElement,\n  getTransitionDurationFromElement\n} from './util/index'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.0.1'\n\nclass BaseComponent {\n  constructor(element) {\n    element = getElement(element)\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    Object.getOwnPropertyNames(this).forEach(propertyName => {\n      this[propertyName] = null\n    })\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    if (!isAnimated) {\n      execute(callback)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n    EventHandler.one(element, 'transitionend', () => execute(callback))\n\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(element, this.DATA_KEY)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-bs-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_ALERT = 'alert'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASS_NAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(element), element, isAnimated)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.get(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toL<PERSON>er<PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_INDICATOR = '[data-bs-target]'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(ORDER_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(ORDER_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const order = index > activeIndex ?\n      ORDER_NEXT :\n      ORDER_PREV\n\n    this._slide(order, this._items[index])\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    this._slide(direction > 0 ? DIRECTION_RIGHT : DIRECTION_LEFT)\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.touches && event.touches.length > 1 ?\n        0 :\n        event.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    if (event.key === ARROW_LEFT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_RIGHT)\n    } else if (event.key === ARROW_RIGHT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_LEFT)\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByOrder(order, activeElement) {\n    const isNext = order === ORDER_NEXT\n    const isPrev = order === ORDER_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrev && activeIndex === 0) || (isNext && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = isPrev ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n      activeIndicator.removeAttribute('aria-current')\n\n      const indicators = SelectorEngine.find(SELECTOR_INDICATOR, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        if (Number.parseInt(indicators[i].getAttribute('data-bs-slide-to'), 10) === this._getItemIndex(element)) {\n          indicators[i].classList.add(CLASS_NAME_ACTIVE)\n          indicators[i].setAttribute('aria-current', 'true')\n          break\n        }\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(directionOrOrder, element) {\n    const order = this._directionToOrder(directionOrOrder)\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || this._getItemByOrder(order, activeElement)\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    const isNext = order === ORDER_NEXT\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n    const eventDirectionName = this._orderToDirection(order)\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    const triggerSlidEvent = () => {\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const completeCallBack = () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(triggerSlidEvent, 0)\n      }\n\n      this._queueCallback(completeCallBack, activeElement, true)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      triggerSlidEvent()\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (![DIRECTION_RIGHT, DIRECTION_LEFT].includes(direction)) {\n      return direction\n    }\n\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (![ORDER_NEXT, ORDER_PREV].includes(order)) {\n      return order\n    }\n\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.get(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.get(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getSelectorFromElement,\n  getElementFromSelector,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${this._element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${this._element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Data.get(tempActiveData, DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.set(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    parent = getElement(parent)\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.get(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  isDisabled,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null,\n  autoClose: true\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)',\n  autoClose: '(boolean|string)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (isDisabled(this._element)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    if (isActive) {\n      this.hide()\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = getElement(this._config.reference)\n      } else if (typeof this._config.reference === 'object') {\n        referenceElement = this._config.reference\n      }\n\n      const popperConfig = this._getPopperConfig()\n      const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n      if (isDisplayStatic) {\n        Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n      }\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', noop))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      this.toggle()\n    })\n  }\n\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.off(elem, 'mouseover', noop))\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem(event) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    // Up\n    if (event.key === ARROW_UP_KEY && index > 0) {\n      index--\n    }\n\n    // Down\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) {\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Data.get(toggles[i], DATA_KEY)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      if (!context._element.classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      const relatedTarget = {\n        relatedTarget: context._element\n      }\n\n      if (event) {\n        const composedPath = event.composedPath()\n        const isMenuTarget = composedPath.includes(context._menu)\n        if (\n          composedPath.includes(context._element) ||\n          (context._config.autoClose === 'inside' && !isMenuTarget) ||\n          (context._config.autoClose === 'outside' && isMenuTarget)\n        ) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n        if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n          continue\n        }\n\n        if (event.type === 'click') {\n          relatedTarget.clickEvent = event\n        }\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (!isActive && event.key === ESCAPE_KEY) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const getToggleButton = () => this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n\n    if (event.key === ESCAPE_KEY) {\n      getToggleButton().focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive && (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY)) {\n      getToggleButton().click()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    Dropdown.getInstance(getToggleButton())._selectMenuItem(event)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.dropdownInterface(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nconst getWidth = () => {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = document.documentElement.clientWidth\n  return Math.abs(window.innerWidth - documentWidth)\n}\n\nconst hide = (width = getWidth()) => {\n  _disableOverFlow()\n  // give padding to element to balances the hidden scrollbar width\n  _setElementAttributes('body', 'paddingRight', calculatedValue => calculatedValue + width)\n  // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements, to keep shown fullwidth\n  _setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n  _setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n}\n\nconst _disableOverFlow = () => {\n  const actualValue = document.body.style.overflow\n  if (actualValue) {\n    Manipulator.setDataAttribute(document.body, 'overflow', actualValue)\n  }\n\n  document.body.style.overflow = 'hidden'\n}\n\nconst _setElementAttributes = (selector, styleProp, callback) => {\n  const scrollbarWidth = getWidth()\n  SelectorEngine.find(selector)\n    .forEach(element => {\n      if (element !== document.body && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      const actualValue = element.style[styleProp]\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n      element.style[styleProp] = `${callback(Number.parseFloat(calculatedValue))}px`\n    })\n}\n\nconst reset = () => {\n  _resetElementAttributes('body', 'overflow')\n  _resetElementAttributes('body', 'paddingRight')\n  _resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n  _resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n}\n\nconst _resetElementAttributes = (selector, styleProp) => {\n  SelectorEngine.find(selector).forEach(element => {\n    const value = Manipulator.getDataAttribute(element, styleProp)\n    if (typeof value === 'undefined') {\n      element.style.removeProperty(styleProp)\n    } else {\n      Manipulator.removeDataAttribute(element, styleProp)\n      element.style[styleProp] = value\n    }\n  })\n}\n\nconst isBodyOverflowing = () => {\n  return getWidth() > 0\n}\n\nexport {\n  getWidth,\n  hide,\n  isBodyOverflowing,\n  reset\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { emulateTransitionEnd, execute, getTransitionDurationFromElement, reflow, typeCheckConfig } from './index'\n\nconst Default = {\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: document.body, // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: 'element',\n  clickCallback: '(function|null)'\n}\nconst NAME = 'backdrop'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nclass Backdrop {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    if (this._config.isAnimated) {\n      reflow(this._getElement())\n    }\n\n    this._getElement().classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  // Private\n\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = CLASS_NAME_BACKDROP\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n\n    config.rootElement = config.rootElement || document.body\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    this._config.rootElement.appendChild(this._getElement())\n\n    EventHandler.on(this._getElement(), EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._getElement().parentNode.removeChild(this._element)\n    this._isAppended = false\n  }\n\n  _emulateAnimation(callback) {\n    if (!this._config.isAnimated) {\n      execute(callback)\n      return\n    }\n\n    const backdropTransitionDuration = getTransitionDurationFromElement(this._getElement())\n    EventHandler.one(this._getElement(), 'transitionend', () => execute(callback))\n    emulateTransitionEnd(this._getElement(), backdropTransitionDuration)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isRTL,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport { getWidth as getScroll<PERSON><PERSON><PERSON>idth, hide as scrollBarHide, reset as scrollBarReset } from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._isShown = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    scrollBarHide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    this._queueCallback(() => this._hideModal(), this._element, isAnimated)\n  }\n\n  dispose() {\n    [window, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    this._backdrop.dispose()\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, isAnimated)\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      scrollBarReset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _showBackdrop(callback) {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n      if (this._ignoreBackdropClick) {\n        this._ignoreBackdropClick = false\n        return\n      }\n\n      if (event.target !== event.currentTarget) {\n        return\n      }\n\n      if (this._config.backdrop === true) {\n        this.hide()\n      } else if (this._config.backdrop === 'static') {\n        this._triggerBackdropTransition()\n      }\n    })\n\n    this._backdrop.show(callback)\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n    EventHandler.off(this._element, 'transitionend')\n    EventHandler.one(this._element, 'transitionend', () => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        EventHandler.one(this._element, 'transitionend', () => {\n          this._element.style.overflowY = ''\n        })\n        emulateTransitionEnd(this._element, modalTransitionDuration)\n      }\n    })\n    emulateTransitionEnd(this._element, modalTransitionDuration)\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = getScrollBarWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if ((!isBodyOverflowing && isModalOverflowing && !isRTL()) || (isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${scrollbarWidth}px`\n    }\n\n    if ((isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getInstance(this) || new Modal(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  const data = Modal.getInstance(target) || new Modal(target)\n\n  data.toggle(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport { hide as scrollBarHide, reset as scrollBarReset } from './util/scrollbar'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\nimport Backdrop from './util/backdrop'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_SHOW = 'show'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"offcanvas\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      scrollBarHide()\n      this._enforceFocusOnElement(this._element)\n    }\n\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (!this._config.scroll) {\n        scrollBarReset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    super.dispose()\n    EventHandler.off(document, EVENT_FOCUSIN)\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: this._config.backdrop,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: () => this.hide()\n    })\n  }\n\n  _enforceFocusOnElement(element) {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n        element !== event.target &&\n        !element.contains(event.target)) {\n        element.focus()\n      }\n    })\n    element.focus()\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Offcanvas(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    Offcanvas.getInstance(allReadyOpen).hide()\n  }\n\n  const data = Data.get(target, DATA_KEY) || new Offcanvas(target)\n\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => (Data.get(el, DATA_KEY) || new Offcanvas(el)).show())\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  findShadowRoot,\n  getElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this._config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip && this.tip.parentNode) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    this.setContent()\n\n    if (this._config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const { container } = this._config\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.appendChild(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = typeof this._config.customClass === 'function' ? this._config.customClass() : this._config.customClass\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop)\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this._config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (isElement(content)) {\n      content = getElement(content)\n\n      // content is a DOM node or a jQuery\n      if (this._config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this._config.html) {\n      if (this._config.sanitize) {\n        content = sanitizeHtml(content, this._config.allowList, this._config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this._config.title === 'function' ?\n        this._config.title.call(this._element) :\n        this._config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.get(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n      Data.set(event.delegateTarget, dataKey, context)\n    }\n\n    return context\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this._config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this._config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this._config.selector) {\n      this._config = {\n        ...this._config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context._config.delay || !context._config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context._config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context._config.delay || !context._config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context._config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this._config) {\n      for (const key in this._config) {\n        if (this.constructor.Default[key] !== this._config[key]) {\n          config[key] = this._config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n                '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this._config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.set(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getInstance(this) || new ScrollSpy(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE))) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      active.classList.remove(CLASS_NAME_SHOW)\n      this._queueCallback(complete, element, true)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    let parent = element.parentNode\n    if (parent && parent.nodeName === 'LI') {\n      parent = parent.parentNode\n    }\n\n    if (parent && parent.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE, dropdownElement)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  const data = Data.get(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        this._hasMouseInteraction = isInteracting\n        break\n      case 'focusin':\n      case 'focusout':\n        this._hasKeyboardInteraction = isInteracting\n        break\n      default:\n        break\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n"]}