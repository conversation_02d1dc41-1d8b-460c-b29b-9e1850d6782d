﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class galeriemedia : System.Web.UI.Page
    {
        APIYoutube_Class objapi = new APIYoutube_Class();
        
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                Affichage();
            }

        }

        public void Affichage()
        {
            var videos = objapi.GetVideosFromYouTube("***************************************", "UCOx6qFYhYdt4USjc9PuWrmQ");
            lvVideos.DataSource = videos;
            lvVideos.DataBind();
        }
    }
}