﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class RoleMembreImp : IRoleMembre
    {
        int msg;
        private rolemembre role = new rolemembre();

        public void AfficherDetails(int idRole,long idorg,string name,int cd, RoleMembre_Class roleClass)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var r = con.rolemembres.FirstOrDefault(x => x.RoleMembreID == idRole);
                    if (r != null)
                    {
                        roleClass.RoleMembreID = r.RoleMembreID;
                        roleClass.OrganisationId = r.OrganisationId;
                        roleClass.NomRole = r.NomRole;
                        roleClass.Description = r.Description;
                        roleClass.TYPE = r.TYPE;
                        roleClass.name = r.name;
                        roleClass.statut = r.statut;
                        roleClass.CDROLE = r.CDROLE;
                    }
                }
                else if (cd==1)
                {
                    var r = con.rolemembres.FirstOrDefault(x => x.RoleMembreID == idRole && x.OrganisationId==idorg);
                    if (r != null)
                    {
                        roleClass.RoleMembreID = r.RoleMembreID;
                        roleClass.OrganisationId = r.OrganisationId;
                        roleClass.NomRole = r.NomRole;
                        roleClass.Description = r.Description;
                        roleClass.TYPE = r.TYPE;
                        roleClass.name = r.name;
                        roleClass.statut = r.statut;
                        roleClass.CDROLE = r.CDROLE;
                    }
                }
                   
            }
        }
        public void AfficherDetails(string code, RoleMembre_Class roleClass)
        {
            using (Connection con = new Connection())
            {
                var r = con.rolemembres.FirstOrDefault(x => x.name == code);
                if (r != null)
                {
                    roleClass.RoleMembreID = r.RoleMembreID;
                    roleClass.OrganisationId = r.OrganisationId;
                    roleClass.NomRole = r.NomRole;
                    roleClass.Description = r.Description;
                    roleClass.TYPE = r.TYPE;
                    roleClass.name = r.name;
                    roleClass.statut = r.statut;
                    roleClass.CDROLE = r.CDROLE;
                }
            }
        }
        public void AfficherDetailRole(int id, RoleMembre_Class roleClass)
        {
            using (Connection con = new Connection())
            {
                var r = con.rolemembres.FirstOrDefault(x => x.RoleMembreID == id);
                if (r != null)
                {
                    roleClass.RoleMembreID = r.RoleMembreID;
                    roleClass.OrganisationId = r.OrganisationId;
                    roleClass.NomRole = r.NomRole;
                    roleClass.Description = r.Description;
                    roleClass.TYPE = r.TYPE;
                    roleClass.name = r.name;
                    roleClass.statut = r.statut;
                    roleClass.CDROLE = r.CDROLE;
                }
            }
        }

        public int Ajouter(RoleMembre_Class roleClass)
        {
            using (Connection con = new Connection())
            {
                role.NomRole = roleClass.NomRole;
                role.Description = roleClass.Description;
                role.name = roleClass.name;
                role.TYPE = roleClass.TYPE;
                role.CDROLE = roleClass.CDROLE;
                role.statut = roleClass.statut;
                role.OrganisationId = roleClass.OrganisationId;

                try
                {
                    con.rolemembres.Add(role);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void chargerRole(DropDownList lst,long id, long idorg,int cd)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var obj = from p in con.rolemembres
                              where (p.TYPE != "administrateur" || p.TYPE != "organisation" || p.TYPE != "partenaire") && p.OrganisationId == idorg
                              select new
                              {
                                  RoleMembreID = p.RoleMembreID,
                                  name = p.name,
                                  NomRole = p.NomRole,

                              };

                    if (obj != null && obj.Count() > 0)
                    {
                        lst.Items.Clear();

                        ListItem item0 = new ListItem();
                        item0.Value = "-1";
                        item0.Text = "Selectionner le Role";
                        lst.Items.Add(item0);

                        foreach (var data in obj)
                        {
                            ListItem item = new ListItem();
                            item.Value = data.RoleMembreID.ToString();
                            item.Text = data.NomRole;
                            lst.Items.Add(item);
                        }

                    }
                    else
                    {
                        lst.Items.Clear();

                        ListItem item0 = new ListItem();
                        item0.Value = "-1";
                        item0.Text = "Aucune donnée";
                        lst.Items.Add(item0);
                    }

                }
                else if (cd==1)
                {
                    var obj = from p in con.rolemembres
                               select new
                              {
                                  RoleMembreID = p.RoleMembreID,
                                  name = p.name,
                                  NomRole = p.NomRole,

                              };

                    if (obj != null && obj.Count() > 0)
                    {
                        lst.Items.Clear();

                        ListItem item0 = new ListItem();
                        item0.Value = "-1";
                        item0.Text = "Selectionner le Role";
                        lst.Items.Add(item0);

                        foreach (var data in obj)
                        {
                            ListItem item = new ListItem();
                            item.Value = data.RoleMembreID.ToString();
                            item.Text = data.NomRole;
                            lst.Items.Add(item);
                        }

                    }
                    else
                    {
                        lst.Items.Clear();

                        ListItem item0 = new ListItem();
                        item0.Value = "-1";
                        item0.Text = "Aucune donnée";
                        lst.Items.Add(item0);
                    }

                }

            }
        }

        public void ChargerGridviewRoles(GridView gdv,long idorg, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var query = from r in con.rolemembres
                                join o in con.Organisations on r.OrganisationId equals o.OrganisationId

                                where (r.TYPE != "administrateur" || r.TYPE != "organisation" || r.TYPE != "partenaire") && r.OrganisationId==idorg
                                select new
                                {
                                    id = r.RoleMembreID,
                                    Nom=r.NomRole,
                                    name = r.name,
                                    description = r.Description,
                                    statut = r.statut,
                                    code = r.CDROLE,
                                    type = r.TYPE,
                                    org = r.OrganisationId,
                                };

                    gdv.DataSource = query.OrderBy(x => x.name).ToList();
                    gdv.DataBind();
                }
                else if (cd==1)
                {
                    var query = from r in con.rolemembres
                                select new
                                {
                                    id = r.RoleMembreID,
                                    Nom = r.NomRole,
                                    name = r.name,
                                    description = r.Description,
                                    statut = r.statut,
                                    code = r.CDROLE,
                                    type = r.TYPE,
                                    org = r.OrganisationId,
                                };

                    gdv.DataSource = query.OrderBy(x => x.name).ToList();
                    gdv.DataBind();

                }
                   
            }
        }

        public int Modifier(RoleMembre_Class roleClass, int id, long idorg)
        {
            using (Connection con = new Connection())
            {
                var r = con.rolemembres.FirstOrDefault(x => x.RoleMembreID == id && x.OrganisationId==idorg);
                if (r != null)
                {
                    r.NomRole = roleClass.NomRole;
                    r.Description = roleClass.Description;
                    r.name = roleClass.name;
                    r.CDROLE = roleClass.CDROLE;
                    r.TYPE = roleClass.TYPE;
                    r.statut = roleClass.statut;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }


        public int Supprimer(int idRole,long idorg)
        {
            using (Connection con = new Connection())
            {
                var r = con.rolemembres.FirstOrDefault(x => x.RoleMembreID == idRole && x.OrganisationId==idorg);
                if (r != null)
                {
                    r.statut = "supprime";
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

    }
}