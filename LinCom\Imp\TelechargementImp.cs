﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{

    public class TelechargementImp : ITelechargement
    {
        int n, msg;
        TelechargementRessource p = new TelechargementRessource();
        public int Ajout(TelechargementRessources_Class add)
        {
            using (Connection con = new Connection())
            {

                p.RessourceId = add.RessourceId;
                p.MembreId = add.MembreId;
                p.DateTelechargement = add.DateTelechargement;
                p.name = add.name;

                try
                {
                    con.TelechargementRessources.Add(p);

                    if (con.SaveChanges() == 1)
                    {
                        con.TelechargementRessources.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }

        public void afficherDetails(int code, TelechargementRessources_Class pr)
        {
            using (Connection con = new Connection())
            {
                p = con.TelechargementRessources.Where(x => x.TelechargementId == code).FirstOrDefault();

                if (p != null)
                {

                    pr.RessourceId = p.RessourceId;
                    pr.MembreId = p.MembreId;
                    pr.DateTelechargement = p.DateTelechargement;
                    pr.name = p.name;

                }

            }
        }

        public void afficherDetails(string code, TelechargementRessources_Class pr)
        {
            throw new NotImplementedException();
        }

        public void Chargement_GDV(GridView GV_apv)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.TelechargementRessources

                           select new
                           {

                               ressourc = ep.RessourceId,
                               member = ep.MembreId,
                               datetelecharg = ep.DateTelechargement,
                               nom = ep.name,




                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public void chargerTelecharg(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.TelechargementRessources select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner le Telechargement";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.TelechargementId.ToString();
                        item.Text = data.name;
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }

        public int count()
        {
            using (Connection con = new Connection())
            {
                var b = (from l in con.TelechargementRessources
                         select l).Count();
                n = b;
            }
            return n;
        }

        public int edit(TelechargementRessources_Class cl, int id)
        {
            using (Connection con = new Connection())
            {
                p = con.TelechargementRessources.Where(x => x.TelechargementId == id).FirstOrDefault();

                try
                {
                    p.RessourceId = cl.RessourceId;
                    p.MembreId = cl.MembreId;
                    p.DateTelechargement = cl.DateTelechargement;
                    p.name = cl.name;


                    if (con.SaveChanges() == 1)
                    {
                        con.TelechargementRessources.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }
                return msg;
            }
        }

        public void search(GridView GV_apv, string code)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.TelechargementRessources
                           join a in con.Ressources on ep.RessourceId equals a.RessourceId
                           join e in con.Membres on ep.MembreId equals e.MembreId
                           where (ep.RessourceId.ToString().Contains(code) && ep.MembreId.ToString().Contains(code))
                           select new
                           {

                               ressourc = ep.RessourceId,
                               member = ep.MembreId,
                               datetelecharg = ep.DateTelechargement,
                               nom = ep.name,




                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public int supprimer(int id)
        {
            using (Connection con = new Connection())
            {

                p = con.TelechargementRessources.Where(x => x.TelechargementId == id).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.TelechargementRessources.Attach(p);

                con.TelechargementRessources.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }
    }
}