﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ITypeOrganisation
    {
        void AfficherDetails(int typeOrganisationId, TypeOrganisation_Class typeOrganisationClass);
        int Ajouter(TypeOrganisation_Class typeOrganisationClass);
        void ChargerTypesOrganisation(GridView gdv);
        void ChargerTypesDisponibles(DropDownList ddl);
        int Modifier(int id,TypeOrganisation_Class typeOrganisationClass);
        int Supprimer(int typeOrganisationId);
        void AfficherDetails(string idTypeOrganisation, TypeOrganisation_Class typeOrganisationClass);
    }
}
