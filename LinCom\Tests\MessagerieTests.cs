using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using LinCom.Classe;
using LinCom.Imp;

namespace LinCom.Tests
{
    /// <summary>
    /// Tests unitaires pour le module de messagerie
    /// </summary>
    [TestClass]
    public class MessagerieTests
    {
        [TestMethod]
        public void TestValidationMessage_MessageValide()
        {
            // Arrange
            string messageValide = "Ceci est un message de test valide.";

            // Act
            var resultat = MessagerieValidator.ValiderMessage(messageValide);

            // Assert
            Assert.IsTrue(resultat.EstValide);
            Assert.AreEqual("Message valide", resultat.Message);
        }

        [TestMethod]
        public void TestValidationMessage_MessageVide()
        {
            // Arrange
            string messageVide = "";

            // Act
            var resultat = MessagerieValidator.ValiderMessage(messageVide);

            // Assert
            Assert.IsFalse(resultat.EstValide);
            Assert.AreEqual(MessagerieConfig.Messages.MessageVide, resultat.Message);
        }

        [TestMethod]
        public void TestValidationMessage_MessageTropLong()
        {
            // Arrange
            string messageTropLong = new string('a', MessagerieConfig.MaxLongueurMessage + 1);

            // Act
            var resultat = MessagerieValidator.ValiderMessage(messageTropLong);

            // Assert
            Assert.IsFalse(resultat.EstValide);
            Assert.IsTrue(resultat.Message.Contains("trop long"));
        }

        [TestMethod]
        public void TestValidationMembreId_IdValide()
        {
            // Arrange
            string idValide = "123";

            // Act
            var resultat = MessagerieValidator.ValiderMembreId(idValide);

            // Assert
            Assert.IsTrue(resultat.EstValide);
            Assert.AreEqual(123L, resultat.Valeur);
        }

        [TestMethod]
        public void TestValidationMembreId_IdInvalide()
        {
            // Arrange
            string idInvalide = "abc";

            // Act
            var resultat = MessagerieValidator.ValiderMembreId(idInvalide);

            // Assert
            Assert.IsFalse(resultat.EstValide);
            Assert.AreEqual(MessagerieConfig.Messages.DestinataireInvalide, resultat.Message);
        }

        [TestMethod]
        public void TestNettoyageTexte_TexteAvecHTML()
        {
            // Arrange
            string texteAvecHTML = "<script>alert('test')</script>Bonjour";

            // Act
            string texteNettoye = MessagerieValidator.NettoyerTexte(texteAvecHTML);

            // Assert
            Assert.IsFalse(texteNettoye.Contains("<script>"));
            Assert.IsTrue(texteNettoye.Contains("Bonjour"));
        }

        [TestMethod]
        public void TestValidationRechercheContact_TermeValide()
        {
            // Arrange
            string termeValide = "Jean";

            // Act
            var resultat = MessagerieValidator.ValiderRechercheContact(termeValide);

            // Assert
            Assert.IsTrue(resultat.EstValide);
            Assert.AreEqual("Recherche valide", resultat.Message);
        }

        [TestMethod]
        public void TestValidationRechercheContact_TermeTropCourt()
        {
            // Arrange
            string termeTropCourt = "J";

            // Act
            var resultat = MessagerieValidator.ValiderRechercheContact(termeTropCourt);

            // Assert
            Assert.IsFalse(resultat.EstValide);
            Assert.IsTrue(resultat.Message.Contains("au moins 2 caractères"));
        }

        [TestMethod]
        public void TestValidationNomFichier_ExtensionAutorisee()
        {
            // Arrange
            string nomFichierValide = "document.pdf";

            // Act
            var resultat = MessagerieValidator.ValiderNomFichier(nomFichierValide);

            // Assert
            Assert.IsTrue(resultat.EstValide);
        }

        [TestMethod]
        public void TestValidationNomFichier_ExtensionNonAutorisee()
        {
            // Arrange
            string nomFichierInvalide = "virus.exe";

            // Act
            var resultat = MessagerieValidator.ValiderNomFichier(nomFichierInvalide);

            // Assert
            Assert.IsFalse(resultat.EstValide);
            Assert.AreEqual(MessagerieConfig.Messages.ExtensionNonAutorisee, resultat.Message);
        }

        [TestMethod]
        public void TestConfiguration_ExtensionAutorisee()
        {
            // Act & Assert
            Assert.IsTrue(MessagerieConfig.EstExtensionAutorisee(".pdf"));
            Assert.IsTrue(MessagerieConfig.EstExtensionAutorisee(".jpg"));
            Assert.IsFalse(MessagerieConfig.EstExtensionAutorisee(".exe"));
        }

        [TestMethod]
        public void TestConfiguration_TailleFichier()
        {
            // Arrange
            long taillePetite = 1024 * 1024; // 1 MB
            long tailleGrosse = MessagerieConfig.MaxPieceJointeTailleMo * 1024 * 1024 + 1; // > limite

            // Act & Assert
            Assert.IsTrue(MessagerieConfig.EstTailleFichierValide(taillePetite));
            Assert.IsFalse(MessagerieConfig.EstTailleFichierValide(tailleGrosse));
        }

        [TestMethod]
        public void TestFormatMessage()
        {
            // Arrange
            string template = "Le message est trop long (maximum {0} caractères)";
            int maxCaracteres = 1000;

            // Act
            string messageFormate = MessagerieConfig.FormatMessage(template, maxCaracteres);

            // Assert
            Assert.AreEqual("Le message est trop long (maximum 1000 caractères)", messageFormate);
        }
    }

    /// <summary>
    /// Tests d'intégration pour le module de messagerie
    /// </summary>
    [TestClass]
    public class MessagerieIntegrationTests
    {
        [TestMethod]
        public void TestEnvoiMessage_Complet()
        {
            // Ce test nécessiterait une base de données de test
            // À implémenter avec une base de données en mémoire ou des mocks
            Assert.Inconclusive("Test d'intégration à implémenter avec base de données de test");
        }

        [TestMethod]
        public void TestRechercheContacts_Complet()
        {
            // Ce test nécessiterait une base de données de test
            // À implémenter avec une base de données en mémoire ou des mocks
            Assert.Inconclusive("Test d'intégration à implémenter avec base de données de test");
        }

        [TestMethod]
        public void TestStatistiquesMessagerie_Complet()
        {
            // Ce test nécessiterait une base de données de test
            // À implémenter avec une base de données en mémoire ou des mocks
            Assert.Inconclusive("Test d'intégration à implémenter avec base de données de test");
        }
    }

    /// <summary>
    /// Tests de performance pour le module de messagerie
    /// </summary>
    [TestClass]
    public class MessageriePerformanceTests
    {
        [TestMethod]
        public void TestPerformance_ValidationMessage()
        {
            // Arrange
            string message = "Message de test pour la performance";
            int nombreIterations = 10000;
            DateTime debut = DateTime.Now;

            // Act
            for (int i = 0; i < nombreIterations; i++)
            {
                MessagerieValidator.ValiderMessage(message);
            }

            // Assert
            TimeSpan duree = DateTime.Now - debut;
            Assert.IsTrue(duree.TotalMilliseconds < 1000, 
                $"La validation devrait prendre moins de 1 seconde pour {nombreIterations} itérations. Durée: {duree.TotalMilliseconds}ms");
        }

        [TestMethod]
        public void TestPerformance_NettoyageTexte()
        {
            // Arrange
            string texte = "Texte avec <script>alert('test')</script> du contenu malveillant";
            int nombreIterations = 1000;
            DateTime debut = DateTime.Now;

            // Act
            for (int i = 0; i < nombreIterations; i++)
            {
                MessagerieValidator.NettoyerTexte(texte);
            }

            // Assert
            TimeSpan duree = DateTime.Now - debut;
            Assert.IsTrue(duree.TotalMilliseconds < 1000, 
                $"Le nettoyage devrait prendre moins de 1 seconde pour {nombreIterations} itérations. Durée: {duree.TotalMilliseconds}ms");
        }
    }
}
