﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ISujetForm
    {
        int add(SujetForum_Class add);
        void Chargement_GDV(GridView GV_apv);
        void search(GridView GV_apv, string code);
        void afficherDetails(int code, SujetForum_Class pr);
        void afficherDetails(string code, SujetForum_Class pr);
        int edit(SujetForum_Class cl, int id);
        int supprimer(int id);

        void chargerForum(DropDownList lst);
        int count();

     
    }
}
