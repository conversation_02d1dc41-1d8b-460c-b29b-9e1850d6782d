﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ICategoryPost
    {
        int add(CategoriePost_Class add);
        void Chargement_GDV(GridView GV_apv);
        void search(GridView GV_apv, string code);
        void afficherDetails(int code, CategoriePost_Class pr);
        void afficherDetails(string code, CategoriePost_Class pr);
        int edit(CategoriePost_Class cl, int id);
        int supprimer(int id);

        void chargerCategoriePost(DropDownList lst);
        int count();
    }
}
