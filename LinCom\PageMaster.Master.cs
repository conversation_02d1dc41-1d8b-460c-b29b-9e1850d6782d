﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class PageMaster : System.Web.UI.MasterPage
    {
        private int info;
        Membre_Class membreClass = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objMembre = new MembreImp();
        IProvince objProvince = new ProvinceImp();
        ICommune objCommune = new CommuneImp();
        ICommonCode co = new CommonCode();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        IPoste obj = new PosteImp();
        Post_Class pos = new Post_Class();
        Post_Class post = new Post_Class();
        Post_Class p = new Post_Class();
        CommentairePoste_Class com = new CommentairePoste_Class();
        ICommentairePoste objcom = new CommentaireImp();

        DomainePost_Class actco = new DomainePost_Class();
        IDomainePost objactco = new DomainePostImp();

        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();

        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();

        CategoriePost_Class catpost = new CategoriePost_Class();
        ICategoryPost objcatpost = new CategoriePostImp();

        INotification objnotif = new NotificationImp();
        Notification_Class notif = new Notification_Class();

        static string imge, imge1, pdfe, nameorg;
         long ide; static long idorg;
        static int rolid;

       
        long index;

        static string slug;
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Session["Langue"] == null)
                Session["Langue"] = "fr"; // Langue par défaut

            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin
                long.TryParse(Request.Cookies["iduser"].Value, out ide);
            }

            if (!IsPostBack)
            {
                Notification();
            }
        }
        public string GetRelativeDate(DateTime date)
        {
            return co.GetRelativeDate(date);
        }

        void Deconnection()
        {
            Session.Clear(); // ou Session.Abandon();
            Response.Cookies["iduser"].Expires = DateTime.Now.AddDays(-1);
            Response.Cookies["role"].Expires = DateTime.Now.AddDays(-1);
           
            Response.Redirect("~/home.aspx");

        }
        void Notification()
        {
            if (ide>0)
            {
                litNombreNonLues.Text=objnotif.CompterNonLues(ide).ToString();
                //   objnotif.ChargerNotificationsRepeater(rptNotifications, ide, -1, "", 0);
            }

        }
        protected void btndeconnect_ServerClick(object sender, EventArgs e)
        {
            Deconnection();
        }


    }
}