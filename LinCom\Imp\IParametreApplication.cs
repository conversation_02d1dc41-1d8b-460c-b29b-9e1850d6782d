﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IParametreApplication
    {
        void AfficherDetails(int idParametre, ParametreApplication_Class parametreClass);
        int Ajouter(ParametreApplication_Class parametreClass);
        void ChargerParametres(GridView gdv, string categorie = "");
        string ObtenirValeur(string nomParametre);
        int ModifierValeur(string nomParametre, string nouvelleValeur, string cles);
        void ChargerParametres(GridView gdv);
        int RestaurerParDefaut(int idParametre);
     
    }
}
