﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.file;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class programmementorat : System.Web.UI.Page
    {

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IPoste objpost = new PosteImp();
        Post_Class post = new Post_Class();
        Post_Class pos = new Post_Class();
        IDomainePost objdompost = new DomainePostImp();
        DomainePost_Class dompost = new DomainePost_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();
        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        ICommonCode co = new CommonCode();
        //   UrlPartage = $"{Request.Url.GetLeftPart(UriPartial.Authority)}/post/" + ep.name,
        IDomaineInterventionOrganisation objdomorg = new DomaineInterventionOrganisationImp();
        DomaineInterventionOrganisation_Class domorg = new DomaineInterventionOrganisation_Class();

        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;

        protected void Page_Load(object sender, EventArgs e)
        {
          
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte

                }
            if (!IsPostBack)
            {
                affich();
                parcour();
            }
        }
        void affich()
        {
             objpost.Chargement_GDV(listprogrammentorat, -1, -1, "programmementorat", "publié", 1);
             objactdom.ChargerDomaines(listcategorie, "programmementorat");
            //objpost.Chargement_GDV(listprogrammentorat, -1, -1, "programmementorat", "fnuap", 1);

            // objc.Chargement_GDVL(listinst, 2);
        }
        protected void parcour()
        {//affichage 
            foreach (ListViewDataItem row in this.listprogrammentorat.Items)
            {
                //if (row.ItemType == DataControlRowType.)
                //{
                try
                {
                    Label lbldate = (Label)row.FindControl("lbldate");
                    //HtmlAnchor btnainscript = (HtmlAnchor)row.FindControl("btnainscript");
                      LinkButton brndevenirmentor = (LinkButton)row.FindControl("brndevenirmentor");
                      LinkButton btndevenirmentore = (LinkButton)row.FindControl("btndevenirmentore");
                    // Label lbldescript = (Label)row.FindControl("lbldescript");
                    HtmlGenericControl txtstatut = (HtmlGenericControl)row.FindControl("txtstatut");

                    // Span extérieur pour changer le style
                    HtmlGenericControl spanStatut = (HtmlGenericControl)row.FindControl("spanStatut");


                    if (Convert.ToDateTime(lbldate.Text) < DateTime.Now)
                    {
                        brndevenirmentor.Visible = false;
                        btndevenirmentore.Visible = false;
                    }
                    if (txtstatut != null && spanStatut != null)
                    {
                        string statut = txtstatut.InnerText.Trim().ToLower();

                        if (statut == "cloturé")
                        {
                            spanStatut.Attributes["class"] = "badge";
                            spanStatut.Style["background-color"] = "red";
                          
                        }
                        else if (statut == "ouvert")
                        {
                            spanStatut.Attributes["class"] = "badge bg-success";
                            // couleur verte maintenue
                        }
                    }
                    }
                catch (Exception e)
                {
                    // Label lbldescrit = (Label)row.FindControl("lbldescat");
                    // lbldescrit.Text = "";
                }

            }

        }
        protected void lvArticles_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                // Récupérer la ligne de données
                var row = (DataRowView)e.Item.DataItem;

                HiddenField hf = (HiddenField)e.Item.FindControl("hfPostId");
                long postId = Convert.ToInt64(hf.Value);

                // Trouver le contrôle enfant listdomai
                var listdomai = (ListView)e.Item.FindControl("listdomai");

                objdompost.ChargerListView(listdomai, postId, -1, 1, "");
            }
        }

        public string GetRelativeDate(DateTime date)
        {
            return co.GetRelativeDate(date);
        }


        protected void listpost_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "view" || e.CommandName == "viewblog")
            {
                objpost.AfficherDetailsname(index, -1, 1, pos);
                post.number_of_view = pos.number_of_view + 1;
                objpost.MiseajourData(post, pos.PostId, -1, 0);

                Response.Redirect("~/program.aspx?name=" + index);



            }
        }
        protected void listcategorie_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            long index = Convert.ToInt64(e.CommandArgument);
            if (e.CommandName == "viewdom")
            {
                objpost.Chargement_GDV(listprogrammentorat, index, -1, "programmementorat", "publié", 2);



            }
        }
       
        protected void listinst_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                // Récupérer l'organisation courante
                //  DataRowView drv = (DataRowView)e.Item.DataItem;
                //  long organisationId = Convert.ToInt64(drv["id"]);

                dynamic post = e.Item.DataItem;
                long id = post.id;


                // Trouver le Repeater des domaines
                Repeater rptDomaines = (Repeater)e.Item.FindControl("rptDomaines");
                if (rptDomaines != null)
                {
                    objdompost.ChargerRepeater(rptDomaines, id, -1, 1, "actif");
                    // objdomorg.ChargerRepeaterDomainesInterventionOrganisation(rptDomaines, id);

                }
            }
        }

        protected void btnsearch_ServerClick(object sender, EventArgs e)
        {
            //if (txtsearch.Value == "")
            //    objpost.Chargement_GDV(listpost, -1, -1, "programme", "publié", 1);
            //else objpost.searchListview(listpost, -1, -1, "programme", "publié", txtsearch.Value, 0);

        }

        protected void listprogrammentorat_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "view" || e.CommandName == "lireplu")
            {

                objpost.AfficherDetailsname(index, -1, 1, pos);
                post.number_of_view = pos.number_of_view + 1;
                objpost.MiseajourData(post, pos.PostId, -1, 0);

                Response.Redirect("~/mentorat.aspx?name=" + index);



            }
            if (e.CommandName=="mento" || e.CommandName=="mentore")
            {
                if (ide <= 0)
                    Response.Redirect("~/login.aspx");
                else
                {
                    Response.Redirect("~/mentorat.aspx?name=" + index);

                }
            }
        }
    }
}