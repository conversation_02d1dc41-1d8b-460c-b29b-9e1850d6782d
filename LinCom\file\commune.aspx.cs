﻿using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class commune : System.Web.UI.Page
    {
        private int info;
        string nscno;
        Province_Class prov = new Province_Class();
        IProvince objp = new ProvinceImp();
        CommuneClass pc = new CommuneClass();
        ICommune obj = new CommuneImp();
        ICommonCode co=new CommonCode();
        static string typenm; static int id, idpers, rol;

        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        long ide; static long idorg;
        static int rolid;
        static string imge, imge1, pdfe, nameorg;

        protected void Page_Load(object sender, EventArgs e)
        {
            nscno = Request.QueryString["name"];
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin
                ide = Convert.ToInt64(idperso.Value);
                rolid = Convert.ToInt32(role.Value);

            }
            else Response.Redirect("~/login.aspx");

            objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
            idorg = Convert.ToInt64(memorg.OrganisationId);
            objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
            nameorg = org.Nom;
            if (!IsPostBack)
            {
              
                initial_msg();
                if (nscno == null)
                {
                    btn_enreg.InnerText = "Enregistrer";
                    // Response.Redirect("~/sima/province.aspx/");
                }
                else

                {
                    btn_enreg.InnerText = "Modifier";
                    afficher();
                }

            }
        }
        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
            objp.chargerProvince(drpdprov);
        }

        public void adCmn()
        {
            try
            {
                if (txtnm.Value == "" || drpdprov.SelectedValue == "-1")
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Renseignez tous les champs";
                }
                else
                {
                    pc.ProvinceId = Convert.ToInt32(drpdprov.SelectedValue);
                   pc.name=co.GenerateSlug(txtnm.Value);
                    pc.Nom = txtnm.Value;
                    info = obj.add(pc);
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listcommune.aspx");

                    }
                    else
                    {
                        div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Cette Commune Existe deja";

                    }


                }

            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Cette Commune Existe deja";

            }

            //LabelMsg.InnerText = obj.ajouterProvince(prov).ToString();
        }

        public void upte()
        {
            try
            {
                if (txtnm.Value == "" || drpdprov.SelectedValue == "-1")
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Renseignez tous les champs";
                }
                else
                {
                    pc.ProvinceId = Convert.ToInt32(drpdprov.SelectedValue);
                    pc.name = co.GenerateSlug(txtnm.Value);
                    pc.Nom = txtnm.Value;
                    info = obj.edit(pc, nscno);
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listcommune.aspx");
                    }
                    else
                    {
                        Response.Redirect("~/file/listcommune.aspx");

                    }

                }
            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Cette Province Existe deja";

            }
        }

        protected void btn_enreg_ServerClick(object sender, EventArgs e)
        {
            if (nscno == null)
            {
                adCmn();

            }
            else
                upte();
        }

        protected void btn_enreg_Click(object sender, EventArgs e)
        {
            if (nscno == null)
            {
                adCmn();

            }
            else
                upte();

        }

        protected void afficher()
        {
            if (nscno != null)
            {
                obj.afficherDetails(nscno, pc);
                drpdprov.SelectedValue = pc.ProvinceId.ToString();
                txtnm.Value = pc.Nom;
            }
        }


    }
}