﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="inscriptionorganisation.aspx.cs" Inherits="LinCom.inscriptionorganisation" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <style>
        #map {
            height: 400px;
            width: 100%;
            margin-bottom: 15px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h2>Inscription des Organisations des Jeunes d'impact social</h2>
                        <%--<p class="mb-0">Odio et unde deleniti. Deserunt numquam exercitationem. Officiis quo odio sint voluptas consequatur ut a odio voluptatem. Sit dolorum debitis veritatis natus dolores. Quasi ratione sint. Sit quaerat ipsum dolorem.</p>
                        --%>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">Connection</a></li>
                    <li class="current"><a href="inscriptionorganisation.aspx">Inscription des Organisations</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">
        <!-- Contact Section -->
        <section id="contact" class="contact section">

            <!-- Section Title -->
            <div class="container section-title" data-aos="fade-up">
                <h2>Inscription</h2>
                <p>Si vous etes une organisation des jeunes d'impact social, voici votre portail d'inscription pour rejoindre le reseau des autres acteurs</p>
            </div>
            <!-- End Section Title -->

            <div class="container" data-aos="fade-up" data-aos-delay="100">

                <div class="row gx-lg-0 gy-4">

                    <div class="col-lg-2">
                        <div class="info-container d-flex flex-column align-items-center justify-content-center">
                            <%-- <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="200">
                                <i class="bi bi-geo-alt flex-shrink-0"></i>
                                <div>
                                    <h3>Address</h3>
                                    <p>A108 Adam Street, New York, NY 535022</p>
                                </div>
                            </div>
                            <!-- End Info Item -->

                            <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="300">
                                <i class="bi bi-telephone flex-shrink-0"></i>
                                <div>
                                    <h3>Call Us</h3>
                                    <p>******* 55488 55</p>
                                </div>
                            </div>
                            <!-- End Info Item -->

                            <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="400">
                                <i class="bi bi-envelope flex-shrink-0"></i>
                                <div>
                                    <h3>Email Us</h3>
                                    <p><EMAIL></p>
                                </div>
                            </div>
                            <!-- End Info Item -->

                            <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="500">
                                <i class="bi bi-clock flex-shrink-0"></i>
                                <div>
                                    <h3>Open Hours:</h3>
                                    <p>Mon-Sat: 11AM - 23PM</p>
                                </div>
                            </div>
                            <!-- End Info Item -->--%>
                        </div>

                    </div>

                    <div class="col-lg-8">
                        <div class="php-email-form" data-aos="fade" data-aos-delay="100">
                            <div class="row gy-4">

                                <div class="col-md-6">
                                    <asp:DropDownList ID="drpdtype" class="form-control-plaintext" runat="server">
                                        <asp:ListItem>Selection le type de l'organisation</asp:ListItem>
                                    </asp:DropDownList>
                                    <span id="errortype" style="color: red; display: none;">Ce champ est requis.</span>
                                </div>
                                <div class="col-md-6 ">
                                    <asp:DropDownList ID="drpdexiste" class="form-control-plaintext" runat="server">
                                        <asp:ListItem Value="-1">Est ce que votre organisation est enregistrée?</asp:ListItem>
                                        <asp:ListItem Value="formel">Oui</asp:ListItem>
                                        <asp:ListItem Value="informel">Non</asp:ListItem>
                                    </asp:DropDownList>
                                    <span id="errorexiste" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>
                                <div class="col-md-6 ">
                                    <input type="text" name="name" class="form-control" runat="server" id="txtnm" placeholder="Nom de l'organisation" required="">
                                    <span id="errortxtnm" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>
                                <div class="col-md-6 ">
                                    <input type="text" name="name" runat="server" id="txtsigle" class="form-control" placeholder="Sigle de l'organisation" required="">
                                </div>
                                <div class="col-md-6 ">
                                    <input type="email" runat="server" id="txtemail" name="name" class="form-control" placeholder="Email de l'organisation" required="">
                                    <span id="erroremail" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>
                                <div class="col-md-6 ">
                                    <input type="email" runat="server" id="txtConfirmEmail" name="name" class="form-control" placeholder="Confirmer l'Email de l'organisation" required="">
                                    <span id="erroremailconfirm" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>

                                <div class="col-md-6 ">
                                    <input type="text" name="name" runat="server" id="txtphone" class="form-control" placeholder="Numero de téléphone de l'organisation" required="">
                                    <span id="errorphone" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>


                                <div class="col-md-12 ">
                                    <h6>Information sur la localisation de l'organisation</h6>

                                </div>
                                <div class="col-md-6 ">
                                    <asp:DropDownList ID="drpdprov" class="form-control" runat="server" AutoPostBack="true" OnSelectedIndexChanged="drpd_prov_SelectedIndexChanged">
                                        <asp:ListItem>Selection la province</asp:ListItem>
                                    </asp:DropDownList>
                                    <span id="errorprov" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>

                                <div class="col-md-6">
                                    <asp:DropDownList ID="drpdcom" class="form-control" runat="server">
                                        <asp:ListItem>Selection la commune</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                                <div class="col-md-12 ">
                                    <input type="text" name="name" runat="server" id="txt_adress" class="form-control" placeholder="Adresse complète de l'organisation" required="">
                                </div>

                                <div class="col-md-6 ">
                                    <label>Date de création de l'organisation</label>
                                    <input type="date" name="name" runat="server" id="txtdatecreation" class="form-control" placeholder="Date de Création de l'organisation" required="">
                                </div>

                                <div class="col-md-12 ">
                                    <h6>Information sur les domaines d'intervention</h6>
                                    <div class="col-md-12">
                                        <i>Vous pouvez choisir un ou plusieurs domaines d'intervention de votre organisation en selectionnant dans la liste. Si votre domaine n'existe pas, veuillez cliquer sur <b>Mon domaine n'existe pas, je veux creer</b>.</i>
                                        <asp:DropDownList ID="drpddomai" runat="server" data-parsley-trigger="change" autocomplete="off" class="form-control" AutoPostBack="true" OnSelectedIndexChanged="drpddomai_SelectedIndexChanged">
                                            <asp:ListItem Value="-1">Selectionner le domaine d'Intervention</asp:ListItem>
                                        </asp:DropDownList>
                                        <a href="srdikis.aspx" runat="server" id="doma" onserverclick="doma_ServerClick" visible="false">Ajouter d'autres domaines</a> <b>|</b>
                                        <a href="srdikis.aspx" runat="server" id="ajoudom" onserverclick="ajoudom_ServerClick">Mon domaine n'existe pas, je veux creer</a>
                                        <div id="dom" runat="server" visible="false">
                                            <h6 runat="server" visible="false">Veuillez selectionner le domaine et cliquer sur le button Ajouter un domaine, et ajoutez d'autres domaines</h6>
                                            <button type="submit" visible="false" runat="server" id="btnajourdom" onserverclick="btnajourdom_ServerClick">Ajouter à la un domaine</button>
                                            <button type="submit" runat="server" id="btnannuldom" onserverclick="btnannuldom_ServerClick">Recommencer</button>

                                            <asp:GridView ID="GridView1" runat="server"
                                                ShowHeaderWhenEmpty="True"
                                                AutoGenerateColumns="True"
                                                EmptyDataText="Aucune Donnée Trouvée pour votre Rercherche"
                                                ShowFooter="true" FooterStyle-Font-Bold="true"
                                                EditRowStyle-Font-Bold="true"
                                                EmptyDataRowStyle-Font-Names="century"
                                                EmptyDataRowStyle-Font-Size="X-Large"
                                                EmptyDataRowStyle-HorizontalAlign="Center"
                                                GridLines="None" AllowPaging="True"
                                                CellSpacing="0" Width="100%">
                                                <AlternatingRowStyle BackColor="#DCDCDC" />

                                            </asp:GridView>
                                        </div>

                                        <div runat="server" id="ajourdom" visible="false">
                                            <div class="card">
                                                <h5 class="card-header">Informations sur Les Domaines d'Intervention</h5>

                                                <div class="col-md-12 ">
                                                    <label>Nom du Domaine d'activité</label>
                                                    <input id="txtnmdom" runat="server" type="text" data-parsley-trigger="change" placeholder="Nom du Domaine d'activité" autocomplete="off" class="form-control" />
                                                </div>
                                                <div class="col-md-12 ">
                                                    <div class="col-sm-6 pl-0">
                                                        <p class="text-right">
                                                            <button type="submit" runat="server" id="btnajoutnouvdom" onserverclick="btnajoutnouvdom_ServerClick">Enregistrer</button>

                                                        </p>
                                                    </div>
                                                </div>

                                            </div>

                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <button type="button" onclick="getCoordinates()" class="btn btn-success notika-gp-success">Obtenir les coordonnées</button><br />
                                    <br />


                                    <asp:HiddenField ID="hdnLatitude" runat="server" />
                                    <asp:HiddenField ID="hdnLongitude" runat="server" />

                                    <label for="txtLatitude">Latitude :</label><br />
                                    <asp:TextBox ID="txtLatitude" class="form-control" runat="server" ReadOnly="true" /><br />
                                    <br />

                                    <label for="txtLongitude">Longitude :</label><br />
                                    <asp:TextBox ID="txtLongitude" class="form-control" runat="server" ReadOnly="true" /><br />
                                    <br />

                                    <div id="map"></div>
                                </div>
                                <div class="col-md-12">
                                    <div class="col-md-6 ">
                                        <input type="password" name="name" runat="server" id="txtmotdepasse" class="form-control" placeholder="Mot de passe utilisateur" required="">
                                    </div>
                                    <div class="col-md-6 ">
                                        <span>Logo de l'organisation (Optionnel)</span>
                                        <asp:FileUpload ID="fileupd" runat="server" class="form-control" />
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <asp:CheckBox ID="chkConditions" runat="server" />
                                    <asp:Label runat="server" AssociatedControlID="chkConditions" Text=" J’ai lu et j’accepte les " />
                                    <a href="politique.aspx" target="_blank">politiques et conditions</a><br />

                                </div>
                                <div class="col-md-12 text-center">

                                    <!-- Google reCAPTCHA -->
                                    <div class="g-recaptcha" data-sitekey="6LcJKC0rAAAAADijgx7o3xkGWEQ01zjnphoRKfcJ"></div>
                                    <br />
                                    <div class="loading">Loading</div>
                                    <div class="error-message"></div>
                                    <div class="sent-message">Your message has been sent. Thank you!</div>

                                    <button type="submit" runat="server" id="btnreng" onserverclick="btnreng_ServerClick">Enregistrer</button>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2">
                        <div class="info-container d-flex flex-column align-items-center justify-content-center">
                            <%-- <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="200">
              <i class="bi bi-geo-alt flex-shrink-0"></i>
              <div>
                  <h3>Address</h3>
                  <p>A108 Adam Street, New York, NY 535022</p>
              </div>
          </div>
          <!-- End Info Item -->

          <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="300">
              <i class="bi bi-telephone flex-shrink-0"></i>
              <div>
                  <h3>Call Us</h3>
                  <p>******* 55488 55</p>
              </div>
          </div>
          <!-- End Info Item -->

          <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="400">
              <i class="bi bi-envelope flex-shrink-0"></i>
              <div>
                  <h3>Email Us</h3>
                  <p><EMAIL></p>
              </div>
          </div>
          <!-- End Info Item -->

          <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="500">
              <i class="bi bi-clock flex-shrink-0"></i>
              <div>
                  <h3>Open Hours:</h3>
                  <p>Mon-Sat: 11AM - 23PM</p>
              </div>
          </div>
          <!-- End Info Item -->--%>
                        </div>

                    </div>


                    <!-- End Contact Form -->

                </div>

            </div>

        </section>
        <!-- /Contact Section -->

    </main>
    <script type="text/javascript">
        function validateForm() {
            var isValid = true;

            // Efface les anciens messages d'erreur
            $('#errortxtnm,#erroremail,#errorphone,#errortxtnm').hide();

            // Vérifie si le champ est vide
            if ($('#<%= txtnm.ClientID %>').val().trim() === "") {
                $('#errortxtnm').show();
                isValid = false;
            }

            return isValid; // Empêche l'envoi si false
        }

        // Configuration Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyAp1PHTxzyIXzvdnHZA0B8tkvWjds7cqzg",
            authDomain: "Lincom.firebaseapp.com",
            projectId: "Lincom",
            messagingSenderId: "893157791741",
            appId: "lincom-2ee86"
        };

        // Initialiser Firebase
        firebase.initializeApp(firebaseConfig);
        const messaging = firebase.messaging();

        // Demander la permission de recevoir des notifications
        messaging.requestPermission()
            .then(() => messaging.getToken())
            .then((token) => {
                console.log("FCM Token :", token);

                // ENVOIE ce token au serveur ASP.NET pour le stocker (via Ajax par exemple)
            })
            .catch((err) => {
                console.error("Erreur de permission FCM", err);
            });

        // Gérer la réception de la notification
        messaging.onMessage((payload) => {
            console.log("Notification reçue :", payload);
            new Notification(payload.notification.title, {
                body: payload.notification.body,
                icon: payload.notification.icon
            });
        });
    </script>

    <!-- Leaflet JS -->
    <!-- JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>

   <script>
       var map = L.map('map').setView([-3.38, 29.37], 8);
       L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
           attribution: '&copy; Linked Community Burundi'
       }).addTo(map);

       var marker;

       function updateLatLngInputs(lat, lng) {
           document.getElementById('<%= txtLatitude.ClientID %>').value = lat.toFixed(6);
        document.getElementById('<%= txtLongitude.ClientID %>').value = lng.toFixed(6);
        document.getElementById('<%= hdnLatitude.ClientID %>').value = lat.toFixed(6);
        document.getElementById('<%= hdnLongitude.ClientID %>').value = lng.toFixed(6);
   }

   function getCoordinates() {
       var adresse = document.getElementById('<%= txt_adress.ClientID %>').value;

       $.get("https://nominatim.openstreetmap.org/search", {
           q: adresse,
           format: "json"
       }, function (data) {
           if (data.length > 0) {
               let lat = parseFloat(data[0].lat);
               let lon = parseFloat(data[0].lon);

               updateLatLngInputs(lat, lon);

               map.setView([lat, lon], 13);

               if (marker) {
                   marker.setLatLng([lat, lon])
                         .bindPopup("Position trouvée").openPopup();
               } else {
                   marker = L.marker([lat, lon], { draggable: true }).addTo(map)
                             .bindPopup("Position trouvée").openPopup();
                   marker.on('dragend', function (e) {
                       var pos = e.target.getLatLng();
                       updateLatLngInputs(pos.lat, pos.lng);
                   });
               }
           } else {
               alert("Adresse introuvable.");
           }
       });
   }

   window.onload = function () {
       var latVal = parseFloat(document.getElementById('<%= txtLatitude.ClientID %>').value);
       var lngVal = parseFloat(document.getElementById('<%= txtLongitude.ClientID %>').value);
           if (!isNaN(latVal) && !isNaN(lngVal)) {
               map.setView([latVal, lngVal], 14);
               marker = L.marker([latVal, lngVal], { draggable: true }).addTo(map)
                   .bindPopup("Position existante").openPopup();
               marker.on('dragend', function (e) {
                   var pos = e.target.getLatLng();
                   updateLatLngInputs(pos.lat, pos.lng);
               });
           }
       };

       map.on('click', function (e) {
           var lat = e.latlng.lat;
           var lng = e.latlng.lng;

           updateLatLngInputs(lat, lng);

           if (marker) {
               marker.setLatLng(e.latlng)
                   .bindPopup("Position choisie").openPopup();
           } else {
               marker = L.marker(e.latlng, { draggable: true }).addTo(map)
                   .bindPopup("Position choisie").openPopup();
               marker.on('dragend', function (e) {
                   var pos = e.target.getLatLng();
                   updateLatLngInputs(pos.lat, pos.lng);
               });
           }
       });
   </script>
</asp:Content>
