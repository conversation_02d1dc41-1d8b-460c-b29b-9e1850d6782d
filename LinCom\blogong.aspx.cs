﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
namespace LinCom
{
    public partial class blogong : System.Web.UI.Page
    {

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IPoste objpost = new PosteImp();
        Post_Class post = new Post_Class();
        Post_Class pos = new Post_Class();
        IDomainePost objdompost = new DomainePostImp();
        DomainePost_Class dompost = new DomainePost_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();
        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        ICommonCode co = new CommonCode();
        //   UrlPartage = $"{Request.Url.GetLeftPart(UriPartial.Authority)}/post/" + ep.name,

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                affich();

            }
        }
        void affich()
        {
           
            objpost.Chargement_GDV(listpost, -1, -1, "article", "publié", 1);
            objactdom.ChargerDomaines(listcategorie,"article");
            // objc.Chargement_GDVL(listinst, 2);
        }
        
        protected void lvArticles_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                // Récupérer la ligne de données
                var row = (DataRowView)e.Item.DataItem;

                HiddenField hf = (HiddenField)e.Item.FindControl("hfPostId");
                long postId = Convert.ToInt64(hf.Value);

                // Trouver le contrôle enfant listdomai
                var listdomai = (ListView)e.Item.FindControl("listdomai");

                objdompost.ChargerListView(listdomai, postId, -1, 1, "");
            }
        }

        protected void listv_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            long index = Convert.ToInt64(e.CommandArgument);
            if (e.CommandName == "view")
            {
                Response.Redirect("~/detailassociation.aspx?IDCOOP=" + index);

            }
        }
       public string GetRelativeDate(DateTime date)
        {
            return co.GetRelativeDate(date);
        }

        public string TronquerTexte(object texteObj,int longueurMax)
        {
            return co.TronquerTexte(texteObj, longueurMax);
        }
        protected void listpost_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "view" || e.CommandName== "viewblog")
            {
                objpost.AfficherDetailsname(index, -1, 1, pos);
                post.number_of_view = pos.number_of_view + 1;
                objpost.MiseajourData(post, pos.PostId, -1, 0);

                Response.Redirect("~/details.aspx?name=" + index);



            }
        }

        protected void listevent_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "viewevent" || e.CommandName == "viewevent1")
            {

                objpost.AfficherDetailsname(index, -1, 1, pos);
                post.number_of_view = pos.number_of_view + 1;
                objpost.MiseajourData(post, pos.PostId, -1, 0);

                Response.Redirect("~/event.aspx?name=" + index);



            }
        }

        protected void listcategorie_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            long index =Convert.ToInt64( e.CommandArgument);
            if (e.CommandName == "viewdom")
            {
                objpost.Chargement_GDV(listpost,index,-1,"article","publié",2);
                


            }
        }

        protected void btnsearch_ServerClick(object sender, EventArgs e)
        {
            if (txtsearch.Value == "")
                objpost.Chargement_GDV(listpost, -1, -1, "article", "publié", 1);
            else objpost.searchListview(listpost,-1,-1,"article","publié",txtsearch.Value,0);

        }
    }
}