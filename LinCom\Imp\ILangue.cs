﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ILangue
    {
        void AfficherDetails(int idLangue, Langue_Class langueClass);
        int Ajouter(Langue_Class langueClass);
      //  void ChargerLangues(GridView gdv, bool actifSeulement = true);
        void ChargerLangues(GridView gdv);
        int Modifier(Langue_Class langueClass, int id);
        int Supprimer(int idLangue);
        string GetTexte(string codeCle, string langue);
    }
}
