﻿using LinCom.Class;
using LinCom.file;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.Services.Description;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class NotificationImp : INotification
    {
        private Notification notif = new Notification();

        public void EnvoyerNotificationAction(long idAuteur, string titreSujet,string contenu, string lienSujet,string name,string statut)
        {
            using (Connection con = new Connection())
            {
                var membres = con.Membres
                    .Where(m => m.MembreId != idAuteur && m.AccepteNotification==1)
                    .ToList();

                foreach (var membre in membres)
                {
                    var notif = new Notification
                    {
                        MembreId = membre.MembreId,
                        Titre = titreSujet,
                        Message = contenu,
                        DateNotification = DateTime.Now,
                        UrlRedirection = lienSujet,
                        Lu = 0,
                        name = name,
                        statut = statut,

                    };
                    con.Notifications.Add(notif);
                }

                con.SaveChanges();
            }
        }
        public void EnvoyerNotificationActionOrganisation(long idAuteur, string titreSujet, string contenu, string lienSujet, string name, string statut)
        {
            using (Connection con = new Connection())
            {
                var membres = con.MembresOrganisations
                    .Where(m => m.MembreId != idAuteur)
                    .ToList();

                foreach (var membre in membres)
                {
                    var notif = new Notification
                    {
                        MembreId = membre.MembreId,
                        Titre = titreSujet,
                        Message = contenu,
                        DateNotification = DateTime.Now,
                        UrlRedirection = lienSujet,
                        Lu = 0,
                        name = name,
                        statut = statut,

                    };
                    con.Notifications.Add(notif);
                }

                con.SaveChanges();
            }
        }

        // ✅ 1. Afficher les détails d'une notification
        public void AfficherDetails(long notificationId, Notification_Class notification)
        {
            try
            {
                using (Connection con = new Connection())
                {
                    var notif = con.Notifications.FirstOrDefault(x => x.NotificationId == notificationId);

                    if (notif != null)
                    {
                        notification.NotificationId = notif.NotificationId;
                        notification.MembreId = notif.MembreId;
                        notification.Titre = notif.Titre;
                        notification.Message = notif.Message;
                        notification.DateNotification = notif.DateNotification;
                        notification.UrlRedirection = notif.UrlRedirection;
                        notification.Lu = notif.Lu;
                        notification.name = notif.name;
                        notification.statut = notif.statut;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception
                throw new Exception("Erreur lors de l'affichage des détails de la notification.", ex);
            } 
        
        }

        // ✅ 2. Ajouter une notification
        public int Ajouter(Notification_Class n)
        {
           
                using (Connection con = new Connection())
                {
                    notif.MembreId = n.MembreId;
                    notif.Titre = n.Titre;
                    notif.Message = n.Message;
                    notif.DateNotification = n.DateNotification ?? DateTime.Now;
                    notif.UrlRedirection = n.UrlRedirection;
                    notif.Lu = 0;
                    notif.name = n.name;
                    notif.statut = n.statut;

                    try
                    {
                        con.Notifications.Add(notif);
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
              
        }
        }

        // ✅ 3. Modifier une notification
        public int Modifier(Notification_Class n)
        {
            using (Connection con = new Connection())
            {
               
                    notif.Titre = n.Titre;
                    notif.Message = n.Message;
                    notif.DateNotification = n.DateNotification ?? DateTime.Now;
                    notif.UrlRedirection = n.UrlRedirection;
                    notif.Lu = 0;
                    notif.name = n.name;
                    notif.statut = n.statut;
                    try
                    {

                        if (con.SaveChanges() == 1)
                        {

                            con.Notifications.Add(notif);
                            con.Entry(notif).State = EntityState.Modified;
                            return  1;
                        }
                        else
                            return  0;
                    }
                    catch
                    {
                        return  0;
                    }
               
            } }

        // ✅ 4. Supprimer une notification
        public int Supprimer(long notificationId)
        {
            using (Connection con = new Connection())
            {

                notif = con.Notifications.Where(x => x.NotificationId == notificationId).First();

                if (con.Entry(notif).State == EntityState.Detached)
                    con.Notifications.Attach(notif);

                con.Notifications.Remove(notif);
                con.SaveChanges();

                return 1;
            }
        }

        // ✅ 5. Charger les notifications d’un membre
        public void ChargerNotificationsGdv(GridView gdv, long membreId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var obj = (from ep in con.Notifications

                               join m in con.Membres on ep.MembreId equals m.MembreId
                               where ep.MembreId == membreId
                               orderby ep.DateNotification descending
                               select new
                               {
                                   id = ep.NotificationId,
                                   Membre = m.Nom + " " + m.Prenom,
                                   Titre=ep.Titre,
                                   Message = ep.Message,
                                   Contenu = ep.Message,
                                   Lu = ep.Lu,
                                   Estlu = ep.Lu,
                                   Photomembre = m.PhotoProfil,
                                   DateNotification = ep.DateNotification,
                                   UrlRedirection = ep.UrlRedirection,

                                   Url = ep.UrlRedirection,
                                   name = ep.name,
                                   Type = ep.name,
                                   statut = ep.statut,
                                   MembreId = ep.MembreId,

                               }).ToList();

                    gdv.DataSource = obj;
                    gdv.DataBind();
                }
                catch (Exception ex)
                {
                    throw new Exception("Erreur lors du chargement des notifications.", ex);
                }
            }
        }
        public void ChargerNotificationsRepeater(Repeater gdv, long membreId,int lu, string statut,int cd)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    if (cd==0)
                    {
                        var obj = (from ep in con.Notifications

                                   join m in con.Membres on ep.MembreId equals m.MembreId
                                   where ep.MembreId == membreId
                                   orderby ep.DateNotification descending
                                   select new
                                   {
                                       id = ep.NotificationId,
                                       Membre = m.Nom + " " + m.Prenom,
                                       Titre = ep.Titre,
                                       Message = ep.Message,
                                       Contenu = ep.Message,
                                       Lu = ep.Lu,
                                       Estlu = ep.Lu,
                                       Photomembre = m.PhotoProfil,
                                       DateNotification = ep.DateNotification,
                                       UrlRedirection = ep.UrlRedirection,

                                       Url = ep.UrlRedirection,
                                       name = ep.name,
                                       Type = ep.name,
                                       statut = ep.statut,
                                       MembreId = ep.MembreId,

                                   }).ToList();

                        gdv.DataSource = obj;
                        gdv.DataBind();
                    }
                    else if (cd==1)
                    {
                        var obj = (from ep in con.Notifications

                                   join m in con.Membres on ep.MembreId equals m.MembreId
                                   where ep.MembreId == membreId && ep.Lu==lu
                                   orderby ep.DateNotification descending
                                   select new
                                   {
                                       id = ep.NotificationId,
                                       Membre = m.Nom + " " + m.Prenom,
                                       Titre = ep.Titre,
                                       Message = ep.Message,
                                       Contenu = ep.Message,
                                       Lu = ep.Lu,
                                       Estlu = ep.Lu,
                                       Photomembre = m.PhotoProfil,
                                       DateNotification = ep.DateNotification,
                                       UrlRedirection = ep.UrlRedirection,

                                       Url = ep.UrlRedirection,
                                       name = ep.name,
                                       Type = ep.name,
                                       statut = ep.statut,
                                       MembreId = ep.MembreId,

                                   }).ToList();

                        gdv.DataSource = obj;
                        gdv.DataBind();
                    }
                       
                   
                }
                catch (Exception ex)
                {
                    throw new Exception("Erreur lors du chargement des notifications.", ex);
                }
            }
        }
        public void ChargerNotificationsListview(ListView gdv, long membreId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var obj = (from ep in con.Notifications

                               join m in con.Membres on ep.MembreId equals m.MembreId
                               where ep.MembreId == membreId
                               orderby ep.DateNotification descending
                               select new
                               {
                                   id = ep.NotificationId,
                                   Membre = m.Nom + " " + m.Prenom,
                                   Titre = ep.Titre,
                                   Message = ep.Message,
                                   Contenu = ep.Message,
                                   Lu = ep.Lu,
                                   Estlu = ep.Lu,
                                   Photomembre = m.PhotoProfil,
                                   DateNotification = ep.DateNotification,
                                   UrlRedirection = ep.UrlRedirection,

                                   Url = ep.UrlRedirection,
                                   name = ep.name,
                                   Type = ep.name,
                                   statut = ep.statut,
                                   MembreId = ep.MembreId,

                               }).ToList();

                    gdv.DataSource = obj;
                    gdv.DataBind();
                }
                catch (Exception ex)
                {
                    throw new Exception("Erreur lors du chargement des notifications.", ex);
                }
            }
        }

        // ✅ 6. Marquer une notification comme lue
        public int MarquerCommeLue(long notificationId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var notif = con.Notifications.Find(notificationId);
                    if (notif != null && notif.Lu == 0)
                    {
                        notif.Lu = 1;
                        return con.SaveChanges();
                    }
                    return 0;
                }
                catch (Exception ex)
                {
                    throw new Exception("Erreur lors de la mise à jour du statut de lecture.", ex);
                }
            }
        }
        // ✅ 7. Charger notifications par type (statut)
        public void ChargerNotificationsParType(GridView gdv, string type)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var list = con.Notifications
                                 .Where(n => n.statut == type)
                                 .OrderByDescending(n => n.DateNotification)
                                 .ToList();
                    gdv.DataSource = list;
                    gdv.DataBind();
                }
                catch (Exception ex)
                {
                    throw new Exception("Erreur lors du chargement par type.", ex);
                }
            }
        }

        // ✅ 8. Envoyer une notification à plusieurs membres
        public int EnvoyerNotificationGroupe(Notification_Class model, long[] membreIds)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    foreach (var membreId in membreIds)
                    {
                        var notif = new Notification
                        {
                            MembreId = membreId,
                            Titre = model.Titre,
                            Message = model.Message,
                            DateNotification = DateTime.Now,
                            UrlRedirection = model.UrlRedirection,
                            Lu = 0,
                            name = model.name,
                            statut = model.statut
                        };
                        con.Notifications.Add(notif);
                    }
                    return con.SaveChanges();
                }
                catch (Exception ex)
                {
                    throw new Exception("Erreur lors de l’envoi de notifications de groupe.", ex);
                }
            }
        }

        // ✅ 9. Charger les statistiques de notification
        public void ChargerStatistiques(Repeater rpt, long membreId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var stats = con.Notifications
                                  .Where(n => n.MembreId == membreId)
                                  .GroupBy(n => n.statut)
                                  .Select(g => new
                                  {
                                      Type = g.Key,
                                      Total = g.Count()
                                  })
                                  .ToList();
                    rpt.DataSource = stats;
                    rpt.DataBind();
                }
                catch (Exception ex)
                {
                    throw new Exception("Erreur lors du chargement des statistiques.", ex);
                }
            }
        }

        // ✅ 10. Purger les anciennes notifications
        public void PurgerAnciennesNotifications(int joursConservation)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var limite = DateTime.Now.AddDays(-joursConservation);
                    var anciennes = con.Notifications
                                      .Where(n => n.DateNotification < limite)
                                      .ToList();
                    con.Notifications.RemoveRange(anciennes);
                    con.SaveChanges();
                }
                catch (Exception ex)
                {
                    throw new Exception("Erreur lors de la purge des anciennes notifications.", ex);
                }
            }
        }

        // ✅ MÉTHODES SUPPLÉMENTAIRES UTILES

        // ✅ 11. Marquer toutes les notifications comme lues pour un membre
        public int MarquerToutCommeLues(long membreId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var notifs = con.Notifications.Where(n => n.MembreId == membreId && n.Lu == 0).ToList();
                    foreach (var n in notifs)
                    {
                        n.Lu = 1;
                    }
                    return con.SaveChanges();
                }
                catch (Exception ex)
                {
                    throw new Exception("Erreur lors du marquage global comme lu.", ex);
                }
            }
        }

        // ✅ 12. Compter les notifications non lues
        public int CompterNonLues(long membreId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    return con.Notifications.Count(n => n.MembreId == membreId && n.Lu == 0);
                }
                catch (Exception ex)
                {
                    throw new Exception("Erreur lors du comptage des notifications non lues.", ex);
                }
            }
        }
    }
}