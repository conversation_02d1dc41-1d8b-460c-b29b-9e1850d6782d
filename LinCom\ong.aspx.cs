﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.file;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class ong : System.Web.UI.Page
    {

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IPoste objpost = new PosteImp();
        Post_Class post = new Post_Class();
        Post_Class pos = new Post_Class();
        IDomainePost objdompost = new DomainePostImp();
        DomainePost_Class dompost = new DomainePost_Class();
        IDomaineIntervention objdomi=new DomaineInterventionImp();
        DomaineIntervention_Class domi=new DomaineIntervention_Class();

        IDomaineInterventionOrganisation objdomorg=new DomaineInterventionOrganisationImp();
        DomaineInterventionOrganisation_Class domorg=new DomaineInterventionOrganisation_Class();

        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();
        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();

        private int PageSize = 2;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                affich();
               
            }

        }
        void affich()
        {
            objorg.Chargement_GDVL(listinst, "organisation", 1);
            objdomi.ChargerDomainedisponible(drpddomai);
            
            //objpost.Chargement_GDV(listpost, -1, -1, "article", "publié", 1);
            //objpost.Chargement_GDV(listevent, -1, -1, "evenement", "publié", 1);
            //objpart.Chargement_GDV(listpartenaire, "publié");

           
        }
        protected void listinst_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                // Récupérer l'organisation courante
              //  DataRowView drv = (DataRowView)e.Item.DataItem;
              //  long organisationId = Convert.ToInt64(drv["id"]);

                dynamic organisation = e.Item.DataItem;
                long id = organisation.id;


                // Trouver le Repeater des domaines
                Repeater rptDomaines = (Repeater)e.Item.FindControl("rptDomaines");
                if (rptDomaines != null)
                {
                    objdomorg.ChargerRepeaterDomainesInterventionOrganisation(rptDomaines,id);
                   
                }
            }
        }


        protected void btnsearch_ServerClick(object sender, EventArgs e)
        {
            if (txtnom.Value.Trim() =="" && txtville.Value.Trim()=="" && drpddomai.SelectedValue=="-1")
            {
                objorg.Chargement_GDVL(listinst, "organisation", 1);
            }
            else
            {
                string codeNomSigle = txtnom.Value.Trim();
                string codeVille = txtville.Value.Trim();
                int iddom = Convert.ToInt32(drpddomai.SelectedValue);

                objorg.searchListview(listinst, codeNomSigle, codeVille, iddom, 0, "actif");

            }

        }

        protected void listinst_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "viewong")
            {

                objpost.AfficherDetailsname(index, -1, 1, pos);
                post.number_of_view = pos.number_of_view + 1;
                objpost.MiseajourData(post, pos.PostId, -1, 0);

                Response.Redirect("~/org.aspx?name=" + index);



            }
        }
    }
}