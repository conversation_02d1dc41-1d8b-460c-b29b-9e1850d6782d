﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class LangueImp : ILangue
    {
        private Langue lan = new Langue();
        int msg;

        public void AfficherDetails(int idLangue, Langue_Class langueClass)
        {
            using (Connection con = new Connection())
            {
                var l = con.Langues.FirstOrDefault(x => x.LangueId == idLangue);
                if (l != null)
                {
                    langueClass.LangueId = l.LangueId;
                    langueClass.Code = l.Code;
                    langueClass.Nom = l.Nom;
                    langueClass.Langu = l.Langu;
                    langueClass.statut = l.statut;


    }
            }
        }

        public int Ajouter(Langue_Class langueClass)
        {
            using (Connection con = new Connection())
            {
               
                lan.Code = langueClass.Code;
                lan.Nom = langueClass.Nom;
                lan.Langu = langueClass.Langu;
                lan.statut = langueClass.statut;


                try
                {
                    con.Langues.Add(lan);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public string GetTexte(string codeCle, string langue)
        {
            using (Connection con = new Connection())
            {
                var texte = con.Langues
                              .FirstOrDefault(t => t.Code== codeCle && t.Langu == langue);

                return texte != null ? texte.Nom : codeCle; // fallback
            }
        }

        public void ChargerLangues(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var query = from l in con.Langues
                            select new
                            {
                               id = l.LangueId,
                                Code = l.Code,
                                Nom = l.Nom,
                                Langu = l.Langu,
                                statut = l.statut,
            }
            ;


                gdv.DataSource = query.OrderBy(x => x.Nom).ToList();
                gdv.DataBind();
            }
        }

        public void chargerLangues(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Langues select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner la langue";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.LangueId.ToString();
                        item.Text = data.Nom;
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }


        public int Modifier(Langue_Class langueClass, int id)
        {
            using (Connection con = new Connection())
            {
                var l = con.Langues.FirstOrDefault(x => x.LangueId == id);
                if (l != null)
                {
                    l.Code = langueClass.Code;
                    l.Nom = langueClass.Nom;
                    l.Langu = langueClass.Langu;
                    l.statut = langueClass.statut;


                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            con.Langues.Add(l);
                            con.Entry(l).State = EntityState.Modified;
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg ;
            }
        }

      

        public int Supprimer(int idLangue)
        {
            using (Connection con = new Connection())
            {
                var l = con.Langues.FirstOrDefault(x => x.LangueId == idLangue);
                if (l != null)
                {

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}