using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace LinCom.Classe
{
    /// <summary>
    /// Gestionnaire des émojis pour les messages
    /// </summary>
    public class EmojiManager
    {
        /// <summary>
        /// Dictionnaire des émojis avec leurs codes
        /// </summary>
        private static readonly Dictionary<string, string> EmojisMap = new Dictionary<string, string>
        {
            // Émojis de base
            {":)", "😊"}, {":-)", "😊"}, {":D", "😃"}, {":-D", "😃"},
            {":(", "😢"}, {":-(", "😢"}, {":P", "😛"}, {":-P", "😛"},
            {";)", "😉"}, {";-)", "😉"}, {":o", "😮"}, {":-o", "😮"},
            {":O", "😲"}, {":-O", "😲"}, {":|", "😐"}, {":-|", "😐"},
            {":/", "😕"}, {":-/", "😕"}, {":\\", "😕"}, {":-\\", "😕"},
            
            // Émojis avec mots-clés
            {":smile:", "😊"}, {":happy:", "😊"}, {":grin:", "😃"},
            {":sad:", "😢"}, {":cry:", "😭"}, {":laugh:", "😂"},
            {":wink:", "😉"}, {":kiss:", "😘"}, {":love:", "😍"},
            {":angry:", "😠"}, {":mad:", "😡"}, {":cool:", "😎"},
            {":surprised:", "😲"}, {":shocked:", "😱"}, {":confused:", "😕"},
            {":neutral:", "😐"}, {":sleepy:", "😴"}, {":sick:", "🤒"},
            
            // Émojis d'objets et symboles
            {":heart:", "❤️"}, {":broken_heart:", "💔"}, {":star:", "⭐"},
            {":fire:", "🔥"}, {":thumbs_up:", "👍"}, {":thumbs_down:", "👎"},
            {":clap:", "👏"}, {":ok:", "👌"}, {":peace:", "✌️"},
            {":muscle:", "💪"}, {":pray:", "🙏"}, {":point_right:", "👉"},
            
            // Émojis d'activités
            {":coffee:", "☕"}, {":beer:", "🍺"}, {":pizza:", "🍕"},
            {":cake:", "🎂"}, {":gift:", "🎁"}, {":party:", "🎉"},
            {":music:", "🎵"}, {":phone:", "📱"}, {":computer:", "💻"},
            {":car:", "🚗"}, {":plane:", "✈️"}, {":home:", "🏠"},
            
            // Émojis de temps et nature
            {":sun:", "☀️"}, {":moon:", "🌙"}, {":cloud:", "☁️"},
            {":rain:", "🌧️"}, {":snow:", "❄️"}, {":tree:", "🌳"},
            {":flower:", "🌸"}, {":cat:", "🐱"}, {":dog:", "🐶"},
            {":bird:", "🐦"}, {":fish:", "🐟"}, {":butterfly:", "🦋"}
        };

        /// <summary>
        /// Catégories d'émojis pour l'interface utilisateur
        /// </summary>
        public static readonly Dictionary<string, List<EmojiInfo>> CategoriesEmojis = new Dictionary<string, List<EmojiInfo>>
        {
            ["Visages"] = new List<EmojiInfo>
            {
                new EmojiInfo("😊", ":smile:", "Sourire"),
                new EmojiInfo("😃", ":grin:", "Grand sourire"),
                new EmojiInfo("😢", ":sad:", "Triste"),
                new EmojiInfo("😭", ":cry:", "Pleurer"),
                new EmojiInfo("😂", ":laugh:", "Rire"),
                new EmojiInfo("😉", ":wink:", "Clin d'œil"),
                new EmojiInfo("😘", ":kiss:", "Bisou"),
                new EmojiInfo("😍", ":love:", "Amoureux"),
                new EmojiInfo("😠", ":angry:", "Colère"),
                new EmojiInfo("😎", ":cool:", "Cool"),
                new EmojiInfo("😲", ":surprised:", "Surpris"),
                new EmojiInfo("😴", ":sleepy:", "Endormi")
            },
            ["Gestes"] = new List<EmojiInfo>
            {
                new EmojiInfo("👍", ":thumbs_up:", "Pouce en haut"),
                new EmojiInfo("👎", ":thumbs_down:", "Pouce en bas"),
                new EmojiInfo("👏", ":clap:", "Applaudir"),
                new EmojiInfo("👌", ":ok:", "OK"),
                new EmojiInfo("✌️", ":peace:", "Paix"),
                new EmojiInfo("💪", ":muscle:", "Muscle"),
                new EmojiInfo("🙏", ":pray:", "Prier"),
                new EmojiInfo("👉", ":point_right:", "Pointer")
            },
            ["Objets"] = new List<EmojiInfo>
            {
                new EmojiInfo("❤️", ":heart:", "Cœur"),
                new EmojiInfo("⭐", ":star:", "Étoile"),
                new EmojiInfo("🔥", ":fire:", "Feu"),
                new EmojiInfo("🎁", ":gift:", "Cadeau"),
                new EmojiInfo("🎉", ":party:", "Fête"),
                new EmojiInfo("📱", ":phone:", "Téléphone"),
                new EmojiInfo("💻", ":computer:", "Ordinateur"),
                new EmojiInfo("🚗", ":car:", "Voiture")
            },
            ["Nourriture"] = new List<EmojiInfo>
            {
                new EmojiInfo("☕", ":coffee:", "Café"),
                new EmojiInfo("🍺", ":beer:", "Bière"),
                new EmojiInfo("🍕", ":pizza:", "Pizza"),
                new EmojiInfo("🎂", ":cake:", "Gâteau")
            },
            ["Nature"] = new List<EmojiInfo>
            {
                new EmojiInfo("☀️", ":sun:", "Soleil"),
                new EmojiInfo("🌙", ":moon:", "Lune"),
                new EmojiInfo("🌳", ":tree:", "Arbre"),
                new EmojiInfo("🌸", ":flower:", "Fleur"),
                new EmojiInfo("🐱", ":cat:", "Chat"),
                new EmojiInfo("🐶", ":dog:", "Chien")
            }
        };

        /// <summary>
        /// Convertit les codes d'émojis en émojis dans un texte
        /// </summary>
        public static string ConvertirEmojis(string texte)
        {
            if (string.IsNullOrEmpty(texte))
                return texte;

            string resultat = texte;

            // Convertir les codes d'émojis (:code:)
            foreach (var emoji in EmojisMap)
            {
                resultat = resultat.Replace(emoji.Key, emoji.Value);
            }

            return resultat;
        }

        /// <summary>
        /// Convertit les émojis en codes dans un texte (pour le stockage)
        /// </summary>
        public static string ConvertirEnCodes(string texte)
        {
            if (string.IsNullOrEmpty(texte))
                return texte;

            string resultat = texte;

            // Convertir les émojis en codes
            foreach (var emoji in EmojisMap)
            {
                resultat = resultat.Replace(emoji.Value, emoji.Key);
            }

            return resultat;
        }

        /// <summary>
        /// Compte le nombre d'émojis dans un texte
        /// </summary>
        public static int CompterEmojis(string texte)
        {
            if (string.IsNullOrEmpty(texte))
                return 0;

            int count = 0;
            foreach (var emoji in EmojisMap.Values)
            {
                count += Regex.Matches(texte, Regex.Escape(emoji)).Count;
            }

            return count;
        }

        /// <summary>
        /// Extrait tous les émojis d'un texte
        /// </summary>
        public static List<string> ExtraireEmojis(string texte)
        {
            var emojis = new List<string>();

            if (string.IsNullOrEmpty(texte))
                return emojis;

            foreach (var emoji in EmojisMap.Values)
            {
                var matches = Regex.Matches(texte, Regex.Escape(emoji));
                foreach (Match match in matches)
                {
                    emojis.Add(match.Value);
                }
            }

            return emojis;
        }

        /// <summary>
        /// Valide si un texte contient uniquement des émojis autorisés
        /// </summary>
        public static bool ValiderEmojis(string texte)
        {
            if (string.IsNullOrEmpty(texte))
                return true;

            // Vérifier si tous les émojis dans le texte sont dans notre liste autorisée
            var emojisTexte = ExtraireEmojis(texte);
            var emojisAutorises = EmojisMap.Values.ToList();

            return emojisTexte.All(e => emojisAutorises.Contains(e));
        }

        /// <summary>
        /// Génère le HTML pour le sélecteur d'émojis
        /// </summary>
        public static string GenererSelecteurEmojis()
        {
            var html = "<div class='emoji-picker' id='emojiPicker' style='display:none;'>";
            
            foreach (var categorie in CategoriesEmojis)
            {
                html += $"<div class='emoji-category'>";
                html += $"<h4>{categorie.Key}</h4>";
                html += "<div class='emoji-grid'>";
                
                foreach (var emoji in categorie.Value)
                {
                    html += $"<span class='emoji-item' data-code='{emoji.Code}' title='{emoji.Description}'>{emoji.Symbole}</span>";
                }
                
                html += "</div></div>";
            }
            
            html += "</div>";
            return html;
        }

        /// <summary>
        /// Recherche des émojis par mot-clé
        /// </summary>
        public static List<EmojiInfo> RechercherEmojis(string motCle)
        {
            var resultats = new List<EmojiInfo>();

            if (string.IsNullOrEmpty(motCle))
                return resultats;

            motCle = motCle.ToLower();

            foreach (var categorie in CategoriesEmojis.Values)
            {
                foreach (var emoji in categorie)
                {
                    if (emoji.Code.ToLower().Contains(motCle) || 
                        emoji.Description.ToLower().Contains(motCle))
                    {
                        resultats.Add(emoji);
                    }
                }
            }

            return resultats;
        }

        /// <summary>
        /// Obtient les émojis les plus utilisés (simulation - à implémenter avec base de données)
        /// </summary>
        public static List<EmojiInfo> ObtenirEmojisPopulaires(int limite = 10)
        {
            // Pour l'instant, retourner les émojis de base les plus communs
            var emojisPopulaires = new List<EmojiInfo>
            {
                new EmojiInfo("😊", ":smile:", "Sourire"),
                new EmojiInfo("😂", ":laugh:", "Rire"),
                new EmojiInfo("❤️", ":heart:", "Cœur"),
                new EmojiInfo("👍", ":thumbs_up:", "Pouce en haut"),
                new EmojiInfo("😢", ":sad:", "Triste"),
                new EmojiInfo("😉", ":wink:", "Clin d'œil"),
                new EmojiInfo("🔥", ":fire:", "Feu"),
                new EmojiInfo("🎉", ":party:", "Fête"),
                new EmojiInfo("😍", ":love:", "Amoureux"),
                new EmojiInfo("👏", ":clap:", "Applaudir")
            };

            return emojisPopulaires.Take(limite).ToList();
        }
    }

    /// <summary>
    /// Informations sur un émoji
    /// </summary>
    public class EmojiInfo
    {
        public string Symbole { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }

        public EmojiInfo(string symbole, string code, string description)
        {
            Symbole = symbole;
            Code = code;
            Description = description;
        }
    }
}
