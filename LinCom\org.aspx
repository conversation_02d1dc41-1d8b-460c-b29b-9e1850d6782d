﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="org.aspx.cs" Inherits="LinCom.org" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h2 runat="server" id="txtnm">Organisations des Jeunes d'impact social</h2>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="ong.aspx">ONG Local</a></li>
                    <li class="current"><span runat="server" id="txtnm1">Liste des Organisations</span></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="container py-5">

        <!-- BANNIÈRE -->
        <div class="bg-light p-4 rounded shadow-sm d-flex align-items-center mb-5" style="background: linear-gradient(to right, #eef2f3, #8e9eab);">
            <div class="me-4">
                <img src="file/organ/24.png" runat="server" id="imgorg" alt="Logo" class="rounded-circle border-0 shadow" style="width: 120px; height: 120px; object-fit: cover;">
            </div>
            <div>
                <h2 class="fw-bold mb-1" runat="server" id="txtsigle">OID</h2>
                <p class="mb-2" runat="server" id="txtnm2">Organisation Internationale pour le Développement</p>
                <div>
                    <a href="#" class="btn btn-sm btn-outline-dark me-2" runat="server" id="txtsiteweb" target="_blank"><i class="bi bi-globe"></i><span runat="server" id="txtsiteweb2">Site web</span></a>
                    <a href="#" class="btn btn-sm btn-outline-primary me-1" runat="server" id="txtfacebook" target="_blank"><i class="bi bi-facebook"></i></a>
                    <a href="#" class="btn btn-sm btn-outline-info me-1" runat="server" id="txttwitter" target="_blank"><i class="bi bi-twitter"></i></a>
                    <a href="#" class="btn btn-sm btn-outline-danger me-1" runat="server" id="txtyoutube" target="_blank"><i class="bi bi-youtube"></i></a>
                    <a href="#" class="btn btn-sm btn-outline-success" runat="server" id="txtlinkedin" target="_blank"><i class="bi bi-linkedin"></i></a>
                </div>
            </div>
        </div>

        <!-- INFOS GÉNÉRALES -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <h4 class="text-uppercase" style="color: #008374;"><i class="bi bi-info-circle-fill me-2"></i>Informations générales</h4>
                <div class="row">
                    <div class="col-md-6"><strong>Téléphone :</strong><span runat="server" id="txtphone"> +243 900 000 111</span></div>
                    <div class="col-md-6"><strong>Email :</strong> <span runat="server" id="txtemail"><EMAIL></span></div>
                    <div class="col-md-6"><strong>Adresse :</strong> <span runat="server" id="txtadresse">45, Av. du Progrès, Kinshasa</span></div>
                    <div class="col-md-6"><strong>Site :</strong> <a href="#" runat="server" id="txtsiteweb1" target="_blank">www.oid.org</a></div>
                    <div class="col-12 mt-2">
                        <strong>Description :</strong>
                        <p runat="server" id="txtdescription">
                            ONG œuvrant pour le développement durable en Afrique.

                        </p>

                    </div>
                </div>
            </div>
        </div>
        <!-- LOCALISATION GÉOGRAPHIQUE -->
<div class="card shadow-sm mb-4">
    <div class="card-body">
        <h4 style="color: #008374;"><i class="bi bi-geo-alt-fill me-2"></i>Localisation sur la carte</h4>
        <div id="map" style="height: 400px;" class="rounded shadow-sm"></div>
    </div>
</div>

        <!-- VISION / MISSION -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <h4 style="color: #008374;"><i class="bi bi-eye-fill me-2"></i>Vision & Mission</h4>
                <p><strong>Vision :</strong> <span runat="server" id="txtvision">des sociétés résilientes grâce à une coopération inclusive.</span> </p>
                <p><strong>Mission :</strong> <span runat="server" id="txtmission">Développer des projets dans l’éducation, santé, agriculture, et technologie.</span></p>
            </div>
        </div>

        <!-- DOMAINES -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <h4 style="color: #008374;"><i class="bi bi-layers-fill me-2"></i>Domaines d’intervention</h4>
                <div>
                    <asp:ListView ID="listdomaine" runat="server">
                        <ItemTemplate>
                            <span class="badge bg-success me-2 mb-2"><%# Eval("libelle") %></span>
                        </ItemTemplate>
                    </asp:ListView>
                  
                </div>
            </div>
        </div>

        <!-- PROJETS -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <h4 style="color: #008374;"><i class="bi bi-briefcase-fill me-2"></i>Projets</h4>
                <div class="row">
                    <asp:ListView ID="listprojet" runat="server">
                        <ItemTemplate>
                            <div class="col-md-4 mb-3">
                                <div class="border rounded p-3 h-100 bg-light">
                                    <h5 class="text-dark">📘 <%# Eval("Titre") %></h5>
                                    <p class="mb-0 small"><%# Eval("summery") %></p>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:ListView>
                   
                </div>
            </div>
        </div>

        <!-- ACTIVITÉS -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <h4 style="color: #008374;"><i class="bi bi-calendar2-check-fill me-2"></i>Activités</h4>
                <div class="row g-3">
                    <asp:ListView ID="listactivite" runat="server" OnItemCommand="listactivite_ItemCommand">
                        <ItemTemplate>
                            <div class="col-md-6">
                                <div class="border rounded p-3 bg-white shadow-sm h-100">
                                    <h5><%# Eval("Titre") %></h5>
                                    <p class="text-muted small"><%# Eval("summery") %></p>
                                    <asp:LinkButton  class="btn btn-sm btn-outline-primary" runat="server" CommandName="view" CommandArgument='<%# Eval("name") %>'>Lire Plus<i class="bi bi-arrow-right"></i></asp:LinkButton>
                       
                                    <a href="#" >Lire Plus</a>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:ListView>
                   
                </div>
            </div>
        </div>

        <!-- SERVICES ET PRODUITS -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <h4 style="color: #008374;"><i class="bi bi-bag-check-fill me-2"></i>Services & Produits</h4>
                <div class="row g-3">
                    <asp:ListView ID="listservice" runat="server">
                        <ItemTemplate>
                            <div class="col-md-6">
                                <div class="p-3 border rounded bg-light h-100">
                                    <h6 class="fw-bold"><%# Eval("Titre") %></h6>
                                    <p class="small mb-0"><%# Eval("summery") %></p>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:ListView>
                  
                </div>
            </div>
        </div>
        <!-- MEMBRES -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <h4 style="color: #008374;"><i class="bi bi-people-fill me-2"></i>Membres</h4>
                <div class="row g-4">
                    <asp:ListView ID="listmembre" runat="server">
                        <ItemTemplate>
                            <div class="col-6 col-md-3 text-center">
                                <img src='<%# HttpUtility.HtmlEncode( string.Concat("../file/membr/",Eval("photo"))) %>' class="rounded-circle shadow-sm mb-2" style="width: 100px; height: 100px; object-fit: cover;">
                                <h6 class="mb-0"><%# Eval("NomComplet") %></h6>
                                <small class="text-muted"><%# Eval("poste") %></small>
                            </div>
                        </ItemTemplate>
                    </asp:ListView>
                   
                </div>
            </div>
        </div>

        <!-- PARTENAIRES -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <h4 style="color: #008374;"><i class="bi bi-handshake-fill me-2"></i>Partenaires</h4>
                <div class="row g-3 text-center align-items-center">
                      <asp:ListView ID="listpartenaire" runat="server">
      <ItemTemplate>
                    <div class="col-6 col-md-2">
                        <img  src='<%# HttpUtility.HtmlEncode( string.Concat("../file/partnerorg/",Eval("logo"))) %>' alt='<%# Eval("Nom") %>' class="img-fluid rounded shadow-sm" style="max-height: 60px;">
                    </div>
          </ItemTemplate>
                          </asp:ListView>
                   
                </div>
            </div>
        </div>

        <!-- FORMULAIRE DE CONTACT -->
        <div class="card shadow-sm mb-5">
            <div class="card-body">
                <h4 style="color: #008374;"><i class="bi bi-envelope-fill me-2"></i>Contactez-nous</h4>
                
                    <div class="row g-3">
                        <div class="col-md-6">
                            <input type="text" name="nom" class="form-control" placeholder="Votre nom" required>
                        </div>
                        <div class="col-md-6">
                            <input type="email" name="email" class="form-control" placeholder="Votre email" required>
                        </div>
                        <div class="col-12">
                            <input type="text" name="sujet" class="form-control" placeholder="Sujet" required>
                        </div>
                        <div class="col-12">
                            <textarea name="message" rows="5" class="form-control" placeholder="Votre message..." required></textarea>
                        </div>
                        <div class="col-12 text-end">
                            <button type="submit" class="btn" style="background-color: #008374; color: white;">Envoyer</button>
                        </div>
                    </div>
               
            </div>
        </div>

    </main>
    <!-- Leaflet JS -->
<!-- JS -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>


   <script>
       var latitude = <%= Latitude.ToString(System.Globalization.CultureInfo.InvariantCulture) %>;
       var longitude = <%= Longitude.ToString(System.Globalization.CultureInfo.InvariantCulture) %>;
       if (!isNaN(latitude) && !isNaN(longitude)) {
           // Afficher la carte
           var map = L.map('map').setView([latitude, longitude], 13);

           L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
               attribution: '&copy; Linked Community Burundi'
           }).addTo(map);

           L.marker([latitude, longitude]).addTo(map)
               .bindPopup("<strong><%= txtnm2.InnerText %></strong><br/><%= txtadresse.InnerText %>")
      .openPopup();
       } else {
           console.warn("Coordonnées GPS non disponibles.");
       }

  
   </script>


</asp:Content>
