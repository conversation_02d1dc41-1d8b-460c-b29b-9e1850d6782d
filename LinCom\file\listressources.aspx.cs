﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class listressources : System.Web.UI.Page
    {
        private int info;
        int formationId;
        Ressources_Class res = new Ressources_Class();
        Ressources_Class ress = new Ressources_Class();
        DomaineRessource_Class domaineFormationClass = new DomaineRessource_Class();

        IRessource obj = new RessourceImp();

        ICommonCode co = new CommonCode();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        DomaineRessource_Class actco = new DomaineRessource_Class();
        IDomaineRessource objactco = new DomaineRessourceImp();
        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;
        static string nsco;
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
            
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
                    rolid = Convert.ToInt32(role.Value);//roleconnecte

                }
                else Response.Redirect("~/login.aspx");

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;

                ChargerRessources();
            }
        }

        private void ChargerRessources()
        {
            obj.Chargement_GDV(gdv,idorg,"",0);
        }

        protected void gdv_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            long index =Convert.ToInt64( e.CommandArgument);

            if (e.CommandName == "view")
            {
                Response.Redirect("ressources.aspx?id=" + index);
            }
            else if (e.CommandName == "delete")
            {
                try
                {
                    obj.AfficherDetails(Convert.ToInt64(index), idorg,0,ress);
                    int result = obj.supprimer(index,idorg);
                    if (result > 0)
                    {
                        // Recharger la liste après suppression
                        ChargerRessources();
                       
                    }
                    else
                    {
                        // Une erreur s'est produite lors de la suppression
                        // Vous pourriez ajouter un message d'erreur ici si nécessaire
                    }
                }
                catch (Exception ex)
                {
                    // Gérer l'exception
                    // Vous pourriez ajouter un message d'erreur ici si nécessaire
                }
            }
        }
    }
}