using System;

namespace LinCom.Class
{
    public class Notification_Class
    {
        public long NotificationId { get; set; }
        public Nullable<long> MembreId { get; set; }
        public string Titre { get; set; }
        public string Message { get; set; }
        public Nullable<System.DateTime> DateNotification { get; set; }
        public string UrlRedirection { get; set; }
        public Nullable<int> Lu { get; set; }
        public string name { get; set; }
        public string statut { get; set; }
    }
}
