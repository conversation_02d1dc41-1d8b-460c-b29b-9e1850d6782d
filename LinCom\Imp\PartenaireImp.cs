﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class PartenaireImp : IPartenaire
    {
        int msg;
        Partenaire p = new Partenaire();
        public int Ajout(Partenaire_Class add)
        {
            using (Connection con = new Connection())
            {
                p.Nom = add.Nom;
                p.Contact = add.Contact;
                p.logo = add.logo;
                p.statut = add.statut;
                p.etat = add.etat;
                p.Description = add.Description;
                p.lienwebsite = add.lienwebsite;
                p.Email = add.Email;

                try
                {
                    con.Partenaires.Add(p);
                    if (con.SaveChanges() == 1)
                    {
                        con.Partenaires.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }

        public void afficherDetails(int code, Partenaire_Class pr)
        {
            using (Connection con = new Connection())
            {
                p = con.Partenaires.Where(x => x.PartenaireId == code).FirstOrDefault();

                pr.PartenaireId = p.PartenaireId;
                pr.Nom = p.Nom;
                pr.Contact = p.Contact;
                pr.logo = p.logo;
                pr.statut = p.statut;
                pr.etat = p.etat;
                pr.Description = p.Description;
                pr.lienwebsite = p.lienwebsite;
                pr.Email = p.Email;

            }
        }

        public void Chargement_GDV(GridView GV_apv, string statu)
        {
            using (Connection con = new Connection())
            {

                var obj = (from p in con.Partenaires

                           select new
                           {
                               id = p.PartenaireId,
                               Nom = p.Nom,
                               Contact = p.Contact,
                               logo = p.logo,
                               statut = p.statut,
                               etat = p.etat,
                               Description = p.Description,
                               lienwebsite = p.lienwebsite,
                               Email = p.Email,



                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }

        }

        public void Chargement_GDV(ListView GV_apv, string statu)
        {
            using (Connection con = new Connection())
            {

                var obj = (from p in con.Partenaires
                           where p.statut == statu
                           select new
                           {
                               id = p.PartenaireId,
                               Nom = p.Nom,
                               Contact = p.Contact,
                               logo = p.logo,
                               statut = p.statut,
                               etat = p.etat,
                               Description = p.Description,
                               lienwebsite = p.lienwebsite,
                               email = p.Email,


                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }

        }

        public int count(string statu)
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                var b = (from l in con.Partenaires
                         where l.statut == statu
                         select l).Count();
                n = b;
            }
            return n;
        }

        public int edit(Partenaire_Class add, int id)
        {
            using (Connection con = new Connection())
            {
                p = con.Partenaires.Where(x => x.PartenaireId == id).FirstOrDefault();

                try
                {
                    p.Nom = add.Nom;
                    p.Contact = add.Contact;
                    p.logo = add.logo;
                    p.statut = add.statut;
                    p.etat = add.etat;
                    p.Description = add.Description;
                    p.lienwebsite = add.lienwebsite;
                    p.Email = add.Email;

                    if (con.SaveChanges() == 1)
                    {
                        con.Partenaires.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;

                }
                catch (Exception e)
                {

                }
                return msg;
            }

        }


        public void search(GridView GV_apv, string code, string statu)
        {
            using (Connection con = new Connection())
            {

                var obj = (from p in con.Partenaires
                           where (p.Nom.Contains(code)) && p.statut == statu
                           select new
                           {
                               id = p.PartenaireId,
                               Nom = p.Nom,
                               Contact = p.Contact,
                               logo = p.logo,
                               statut = p.statut,
                               etat = p.etat,
                               Description = p.Description,
                               lienwebsite = p.lienwebsite,
                               email = p.Email,

                           }).ToList();


                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }

        }

        public int supprimer(int id)
        {
            using (Connection con = new Connection())
            {

                p = con.Partenaires.Where(x => x.PartenaireId == id).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.Partenaires.Attach(p);

                con.Partenaires.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }
    }
}