﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace LinCom.Classe
{
    public class Post_Class
    {
        public long PostId { get; set; }
        public string Titre { get; set; }
        public string Contenu { get; set; }
        public Nullable<int> CategoriePostId { get; set; }
        public Nullable<long> MembreId { get; set; }
        public string DatePublication { get; set; }
        public string summery { get; set; }
        public string author { get; set; }
        public string photo { get; set; }
        public string video { get; set; }
        public Nullable<int> number_of_view { get; set; }
        public Nullable<int> like { get; set; }
        public Nullable<int> dislike { get; set; }
        public string starttime { get; set; }
        public string eventduration { get; set; }
        public string eventplace { get; set; }
        public string whoattend { get; set; }
        public string qualificationattend { get; set; }
        public string langueevent { get; set; }
        public string externevent { get; set; }
        public string MOIS { get; set; }
        public string ANNEE { get; set; }
        public string lien_isncription { get; set; }
        public string pdf { get; set; }
        public string name { get; set; }
        public Nullable<System.DateTime> DateCreation { get; set; }
        public Nullable<System.DateTime> DateModification { get; set; }
        public string EstPublie { get; set; }
        public string EstPublieEvent { get; set; }
        public string statut { get; set; }
        public string etat { get; set; }
        public Nullable<long> OrganisationId { get; set; }
    }
}