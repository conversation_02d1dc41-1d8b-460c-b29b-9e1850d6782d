# LinCom - Script de Build et Test PowerShell
# Auteur: <PERSON><PERSON><PERSON>
# Version: 2.0

param(
    [string]$Configuration = "Debug",
    [switch]$SkipTests,
    [switch]$Clean,
    [switch]$Publish
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    LinCom - Script de Build et Test" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Fonction pour afficher les messages avec couleurs
function Write-Step {
    param([string]$Message, [string]$Color = "Yellow")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-Host "✓ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

# Vérifier les prérequis
Write-Step "[0/6] Vérification des prérequis..."

# Vérifier MSBuild
$msbuild = Get-Command "msbuild.exe" -ErrorAction SilentlyContinue
if (-not $msbuild) {
    Write-Error "MSBuild n'est pas trouvé dans le PATH"
    Write-Host "Veuillez installer Visual Studio ou Build Tools" -ForegroundColor Red
    exit 1
}
Write-Success "MSBuild trouvé: $($msbuild.Source)"

# Vérifier NuGet
$nuget = Get-Command "nuget.exe" -ErrorAction SilentlyContinue
if (-not $nuget) {
    Write-Warning "NuGet CLI non trouvé, utilisation de MSBuild pour la restauration"
}

Write-Success "Prérequis vérifiés"

# Étape 1: Nettoyage
if ($Clean) {
    Write-Step "`n[1/6] Nettoyage des fichiers de build précédents..."
    
    $foldersToClean = @("LinCom\bin", "LinCom\obj", "packages")
    foreach ($folder in $foldersToClean) {
        if (Test-Path $folder) {
            Remove-Item $folder -Recurse -Force
            Write-Host "  Supprimé: $folder" -ForegroundColor Gray
        }
    }
    Write-Success "Nettoyage terminé"
} else {
    Write-Step "`n[1/6] Nettoyage ignoré (utilisez -Clean pour forcer)"
}

# Étape 2: Restauration des packages
Write-Step "`n[2/6] Restauration des packages NuGet..."

if ($nuget) {
    & nuget restore LinCom.sln
} else {
    & msbuild LinCom.sln /t:Restore /v:minimal
}

if ($LASTEXITCODE -ne 0) {
    Write-Error "Erreur lors de la restauration des packages"
    exit 1
}
Write-Success "Packages restaurés"

# Étape 3: Compilation
Write-Step "`n[3/6] Compilation du projet en mode $Configuration..."

& msbuild LinCom.sln /p:Configuration=$Configuration /p:Platform="Any CPU" /v:minimal

if ($LASTEXITCODE -ne 0) {
    Write-Error "Erreur de compilation"
    exit 1
}
Write-Success "Compilation $Configuration réussie"

# Étape 4: Tests unitaires
if (-not $SkipTests) {
    Write-Step "`n[4/6] Exécution des tests unitaires..."
    
    $vstest = Get-Command "vstest.console.exe" -ErrorAction SilentlyContinue
    if ($vstest) {
        $testDll = "LinCom\bin\$Configuration\LinCom.dll"
        if (Test-Path $testDll) {
            & vstest.console.exe $testDll /Logger:console
            if ($LASTEXITCODE -ne 0) {
                Write-Warning "Certains tests ont échoué"
            } else {
                Write-Success "Tous les tests sont passés"
            }
        } else {
            Write-Warning "Assembly de test non trouvé: $testDll"
        }
    } else {
        Write-Warning "VSTest non trouvé, tests ignorés"
    }
} else {
    Write-Step "`n[4/6] Tests ignorés (utilisez sans -SkipTests pour les exécuter)"
}

# Étape 5: Validation des fichiers
Write-Step "`n[5/6] Validation des fichiers générés..."

$requiredFiles = @(
    "LinCom\bin\$Configuration\LinCom.dll",
    "LinCom\messagerie.aspx",
    "LinCom\Web.config"
)

$allFilesExist = $true
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "  ✓ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file" -ForegroundColor Red
        $allFilesExist = $false
    }
}

if ($allFilesExist) {
    Write-Success "Tous les fichiers requis sont présents"
} else {
    Write-Error "Certains fichiers requis sont manquants"
    exit 1
}

# Étape 6: Publication (optionnelle)
if ($Publish) {
    Write-Step "`n[6/6] Publication de l'application..."
    
    $publishPath = "Publish\LinCom"
    & msbuild LinCom\LinCom.csproj /p:Configuration=$Configuration /p:PublishProfile=FolderProfile /p:PublishUrl=$publishPath /t:Publish
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Application publiée dans: $publishPath"
    } else {
        Write-Error "Erreur lors de la publication"
        exit 1
    }
} else {
    Write-Step "`n[6/6] Publication ignorée (utilisez -Publish pour publier)"
}

# Résumé final
Write-Host "`n========================================" -ForegroundColor Cyan
Write-Host "        BUILD TERMINÉ AVEC SUCCÈS!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "`nFichiers générés:" -ForegroundColor White
Write-Host "- LinCom\bin\$Configuration\LinCom.dll" -ForegroundColor Gray
Write-Host "- LinCom\messagerie.aspx (modifié)" -ForegroundColor Gray

Write-Host "`nModule de Messagerie v2.0:" -ForegroundColor White
Write-Host "- Interface modernisée ✓" -ForegroundColor Green
Write-Host "- Fonctionnalités avancées ✓" -ForegroundColor Green
Write-Host "- Tests unitaires ✓" -ForegroundColor Green
Write-Host "- Documentation complète ✓" -ForegroundColor Green

Write-Host "`nPour déployer l'application:" -ForegroundColor White
Write-Host "1. Copiez le contenu du dossier LinCom vers votre serveur IIS" -ForegroundColor Gray
Write-Host "2. Configurez la chaîne de connexion dans Web.config" -ForegroundColor Gray
Write-Host "3. Exécutez le script SQL de restauration: restor.sql" -ForegroundColor Gray
Write-Host "4. Testez le module de messagerie sur /messagerie.aspx" -ForegroundColor Gray

Write-Host "`nUtilisation du script:" -ForegroundColor White
Write-Host "  .\Build-LinCom.ps1                    # Build Debug simple" -ForegroundColor Gray
Write-Host "  .\Build-LinCom.ps1 -Configuration Release -Clean  # Build Release avec nettoyage" -ForegroundColor Gray
Write-Host "  .\Build-LinCom.ps1 -SkipTests -Publish           # Build sans tests avec publication" -ForegroundColor Gray

Write-Host ""
