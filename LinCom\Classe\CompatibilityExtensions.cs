using System;

namespace LinCom.Classe
{
    /// <summary>
    /// Extensions pour la compatibilité avec C# 7.3
    /// </summary>
    public static class CompatibilityExtensions
    {
        /// <summary>
        /// Extension pour remplacer l'opérateur range [..n] de C# 8.0
        /// </summary>
        public static string SubstringFromStart(this string str, int length)
        {
            if (string.IsNullOrEmpty(str))
                return str;
                
            if (length >= str.Length)
                return str;
                
            return str.Substring(0, length);
        }

        /// <summary>
        /// Extension pour remplacer l'opérateur range [n..] de C# 8.0
        /// </summary>
        public static string SubstringFromIndex(this string str, int startIndex)
        {
            if (string.IsNullOrEmpty(str))
                return str;
                
            if (startIndex >= str.Length)
                return string.Empty;
                
            return str.Substring(startIndex);
        }

        /// <summary>
        /// Extension pour remplacer l'opérateur range [start..end] de C# 8.0
        /// </summary>
        public static string SubstringRange(this string str, int startIndex, int endIndex)
        {
            if (string.IsNullOrEmpty(str))
                return str;
                
            if (startIndex >= str.Length || endIndex <= startIndex)
                return string.Empty;
                
            int length = Math.Min(endIndex - startIndex, str.Length - startIndex);
            return str.Substring(startIndex, length);
        }

        /// <summary>
        /// Méthode utilitaire pour formater les chaînes de manière sécurisée
        /// </summary>
        public static string SafeFormat(string template, params object[] args)
        {
            try
            {
                return string.Format(template, args);
            }
            catch (FormatException)
            {
                return template; // Retourner le template original en cas d'erreur
            }
        }

        /// <summary>
        /// Méthode utilitaire pour vérifier si une chaîne contient une sous-chaîne (insensible à la casse)
        /// </summary>
        public static bool ContainsIgnoreCase(this string source, string value)
        {
            if (source == null || value == null)
                return false;
                
            return source.IndexOf(value, StringComparison.OrdinalIgnoreCase) >= 0;
        }

        /// <summary>
        /// Méthode utilitaire pour tronquer une chaîne de manière sécurisée
        /// </summary>
        public static string TruncateSafe(this string str, int maxLength, string suffix = "...")
        {
            if (string.IsNullOrEmpty(str))
                return str;
                
            if (str.Length <= maxLength)
                return str;
                
            int truncateLength = maxLength - suffix.Length;
            if (truncateLength <= 0)
                return suffix.SubstringFromStart(maxLength);
                
            return str.SubstringFromStart(truncateLength) + suffix;
        }

        /// <summary>
        /// Méthode utilitaire pour nettoyer les chaînes de caractères dangereux
        /// </summary>
        public static string CleanString(this string str)
        {
            if (string.IsNullOrEmpty(str))
                return str;
                
            // Supprimer les caractères de contrôle
            var cleanedChars = new char[str.Length];
            int cleanedIndex = 0;
            
            foreach (char c in str)
            {
                if (!char.IsControl(c) || c == '\t' || c == '\n' || c == '\r')
                {
                    cleanedChars[cleanedIndex++] = c;
                }
            }
            
            return new string(cleanedChars, 0, cleanedIndex);
        }

        /// <summary>
        /// Méthode utilitaire pour valider les emails de manière basique
        /// </summary>
        public static bool IsValidEmail(this string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;
                
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Méthode utilitaire pour convertir en titre (première lettre majuscule)
        /// </summary>
        public static string ToTitleCase(this string str)
        {
            if (string.IsNullOrEmpty(str))
                return str;
                
            if (str.Length == 1)
                return str.ToUpper();
                
            return char.ToUpper(str[0]) + str.SubstringFromIndex(1).ToLower();
        }
    }
}
