-- =============================================
-- Script d'optimisation pour le module de messagerie
-- Version: 2.0
-- Date: 2024
-- Description: Création d'index et optimisations pour améliorer les performances
-- =============================================

USE [LincomDB]
GO

-- =============================================
-- INDEX POUR LA TABLE MESSAGES
-- =============================================

-- Index pour optimiser la recherche par conversation
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Messages_ConversationId_DateEnvoi')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Messages_ConversationId_DateEnvoi] 
    ON [dbo].[Messages] ([ConversationId] ASC, [DateEnvoi] DESC)
    INCLUDE ([MessageId], [SenderId], [Contenu], [AttachmentUrl], [name])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    PRINT 'Index IX_Messages_ConversationId_DateEnvoi créé avec succès'
END
ELSE
    PRINT 'Index IX_Messages_ConversationId_DateEnvoi existe déjà'
GO

-- Index pour optimiser la recherche par expéditeur
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Messages_SenderId_DateEnvoi')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Messages_SenderId_DateEnvoi] 
    ON [dbo].[Messages] ([SenderId] ASC, [DateEnvoi] DESC)
    INCLUDE ([MessageId], [ConversationId], [Contenu])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    PRINT 'Index IX_Messages_SenderId_DateEnvoi créé avec succès'
END
ELSE
    PRINT 'Index IX_Messages_SenderId_DateEnvoi existe déjà'
GO

-- Index pour optimiser la recherche par date (nettoyage)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Messages_DateEnvoi')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Messages_DateEnvoi] 
    ON [dbo].[Messages] ([DateEnvoi] ASC)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    PRINT 'Index IX_Messages_DateEnvoi créé avec succès'
END
ELSE
    PRINT 'Index IX_Messages_DateEnvoi existe déjà'
GO

-- =============================================
-- INDEX POUR LA TABLE MESSAGESTATUS
-- =============================================

-- Index pour optimiser la recherche des messages non lus
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MessageStatus_UserId_IsRead')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MessageStatus_UserId_IsRead] 
    ON [dbo].[MessageStatus] ([UserId] ASC, [IsRead] ASC)
    INCLUDE ([MessageId], [ReadAt])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    PRINT 'Index IX_MessageStatus_UserId_IsRead créé avec succès'
END
ELSE
    PRINT 'Index IX_MessageStatus_UserId_IsRead existe déjà'
GO

-- Index pour optimiser la recherche par message
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MessageStatus_MessageId_UserId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MessageStatus_MessageId_UserId] 
    ON [dbo].[MessageStatus] ([MessageId] ASC, [UserId] ASC)
    INCLUDE ([IsRead], [ReadAt])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    PRINT 'Index IX_MessageStatus_MessageId_UserId créé avec succès'
END
ELSE
    PRINT 'Index IX_MessageStatus_MessageId_UserId existe déjà'
GO

-- =============================================
-- INDEX POUR LA TABLE CONVERSATIONS
-- =============================================

-- Index pour optimiser la recherche par type de conversation
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Conversations_IsGroup_CreatedAt')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Conversations_IsGroup_CreatedAt] 
    ON [dbo].[Conversations] ([IsGroup] ASC, [CreatedAt] DESC)
    INCLUDE ([ConversationId], [Sujet])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    PRINT 'Index IX_Conversations_IsGroup_CreatedAt créé avec succès'
END
ELSE
    PRINT 'Index IX_Conversations_IsGroup_CreatedAt existe déjà'
GO

-- =============================================
-- INDEX POUR LA TABLE PARTICIPANTCONVERSATIONS
-- =============================================

-- Index pour optimiser la recherche des conversations d'un membre
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ParticipantConversations_MembreId_JoinedAt')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_ParticipantConversations_MembreId_JoinedAt] 
    ON [dbo].[ParticipantConversations] ([MembreId] ASC, [JoinedAt] DESC)
    INCLUDE ([ConversationId])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    PRINT 'Index IX_ParticipantConversations_MembreId_JoinedAt créé avec succès'
END
ELSE
    PRINT 'Index IX_ParticipantConversations_MembreId_JoinedAt existe déjà'
GO

-- Index pour optimiser la recherche des participants d'une conversation
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ParticipantConversations_ConversationId_MembreId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_ParticipantConversations_ConversationId_MembreId] 
    ON [dbo].[ParticipantConversations] ([ConversationId] ASC, [MembreId] ASC)
    INCLUDE ([JoinedAt])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    PRINT 'Index IX_ParticipantConversations_ConversationId_MembreId créé avec succès'
END
ELSE
    PRINT 'Index IX_ParticipantConversations_ConversationId_MembreId existe déjà'
GO

-- =============================================
-- INDEX POUR LA TABLE MEMBRES (pour la recherche)
-- =============================================

-- Index pour optimiser la recherche de membres
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Membres_Nom_Prenom_Email_Statut')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Membres_Nom_Prenom_Email_Statut] 
    ON [dbo].[Membres] ([statut] ASC, [Nom] ASC, [Prenom] ASC)
    INCLUDE ([MembreId], [Email], [PhotoProfil])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    PRINT 'Index IX_Membres_Nom_Prenom_Email_Statut créé avec succès'
END
ELSE
    PRINT 'Index IX_Membres_Nom_Prenom_Email_Statut existe déjà'
GO

-- =============================================
-- VUES POUR OPTIMISER LES REQUÊTES COMPLEXES
-- =============================================

-- Vue pour les conversations récentes avec dernier message
IF OBJECT_ID('dbo.vw_ConversationsRecentes', 'V') IS NOT NULL
    DROP VIEW dbo.vw_ConversationsRecentes
GO

CREATE VIEW dbo.vw_ConversationsRecentes
AS
SELECT 
    c.ConversationId,
    c.Sujet,
    c.IsGroup,
    c.CreatedAt,
    m.Contenu AS DernierMessage,
    m.DateEnvoi AS DateDernierMessage,
    mb.Nom + ' ' + mb.Prenom AS DernierExpediteur,
    mb.PhotoProfil AS PhotoDernierExpediteur
FROM Conversations c
LEFT JOIN (
    SELECT 
        ConversationId,
        Contenu,
        DateEnvoi,
        SenderId,
        ROW_NUMBER() OVER (PARTITION BY ConversationId ORDER BY DateEnvoi DESC) as rn
    FROM Messages
) m ON c.ConversationId = m.ConversationId AND m.rn = 1
LEFT JOIN Membres mb ON m.SenderId = mb.MembreId
GO

PRINT 'Vue vw_ConversationsRecentes créée avec succès'

-- Vue pour les statistiques de messagerie
IF OBJECT_ID('dbo.vw_StatistiquesMessagerie', 'V') IS NOT NULL
    DROP VIEW dbo.vw_StatistiquesMessagerie
GO

CREATE VIEW dbo.vw_StatistiquesMessagerie
AS
SELECT 
    mb.MembreId,
    mb.Nom + ' ' + mb.Prenom AS NomComplet,
    ISNULL(stats.MessagesEnvoyes, 0) AS MessagesEnvoyes,
    ISNULL(stats.MessagesRecus, 0) AS MessagesRecus,
    ISNULL(stats.ConversationsActives, 0) AS ConversationsActives,
    ISNULL(stats.MessagesNonLus, 0) AS MessagesNonLus,
    stats.DerniereActivite
FROM Membres mb
LEFT JOIN (
    SELECT 
        pc.MembreId,
        SUM(CASE WHEN m.SenderId = pc.MembreId THEN 1 ELSE 0 END) AS MessagesEnvoyes,
        SUM(CASE WHEN m.SenderId != pc.MembreId THEN 1 ELSE 0 END) AS MessagesRecus,
        COUNT(DISTINCT pc.ConversationId) AS ConversationsActives,
        SUM(CASE WHEN ms.IsRead = 0 AND m.SenderId != pc.MembreId THEN 1 ELSE 0 END) AS MessagesNonLus,
        MAX(m.DateEnvoi) AS DerniereActivite
    FROM ParticipantConversations pc
    LEFT JOIN Messages m ON pc.ConversationId = m.ConversationId
    LEFT JOIN MessageStatus ms ON m.MessageId = ms.MessageId AND ms.UserId = pc.MembreId
    GROUP BY pc.MembreId
) stats ON mb.MembreId = stats.MembreId
WHERE mb.statut = 'actif'
GO

PRINT 'Vue vw_StatistiquesMessagerie créée avec succès'

-- =============================================
-- PROCÉDURES STOCKÉES POUR LES OPÉRATIONS COURANTES
-- =============================================

-- Procédure pour nettoyer les anciens messages
IF OBJECT_ID('dbo.sp_NettoyerAnciennesMessages', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_NettoyerAnciennesMessages
GO

CREATE PROCEDURE dbo.sp_NettoyerAnciennesMessages
    @JoursConservation INT = 365
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @DateLimite DATETIME = DATEADD(DAY, -@JoursConservation, GETDATE())
    DECLARE @MessagesSupprimes INT = 0
    
    BEGIN TRANSACTION
    
    BEGIN TRY
        -- Supprimer les statuts des anciens messages
        DELETE FROM MessageStatus 
        WHERE MessageId IN (
            SELECT MessageId FROM Messages 
            WHERE DateEnvoi < @DateLimite
        )
        
        -- Supprimer les anciens messages
        DELETE FROM Messages 
        WHERE DateEnvoi < @DateLimite
        
        SET @MessagesSupprimes = @@ROWCOUNT
        
        COMMIT TRANSACTION
        
        PRINT 'Nettoyage terminé. ' + CAST(@MessagesSupprimes AS VARCHAR(10)) + ' messages supprimés.'
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        PRINT 'Erreur lors du nettoyage: ' + @ErrorMessage
        
        THROW
    END CATCH
END
GO

PRINT 'Procédure sp_NettoyerAnciennesMessages créée avec succès'

-- =============================================
-- MISE À JOUR DES STATISTIQUES
-- =============================================

-- Mettre à jour les statistiques pour tous les index créés
UPDATE STATISTICS dbo.Messages
UPDATE STATISTICS dbo.MessageStatus  
UPDATE STATISTICS dbo.Conversations
UPDATE STATISTICS dbo.ParticipantConversations
UPDATE STATISTICS dbo.Membres

PRINT 'Statistiques mises à jour avec succès'

-- =============================================
-- RÉSUMÉ DES OPTIMISATIONS
-- =============================================

PRINT '============================================='
PRINT 'OPTIMISATIONS APPLIQUÉES AVEC SUCCÈS:'
PRINT '- 7 index créés pour améliorer les performances'
PRINT '- 2 vues créées pour simplifier les requêtes complexes'
PRINT '- 1 procédure stockée pour le nettoyage automatique'
PRINT '- Statistiques mises à jour'
PRINT '============================================='
PRINT 'Le module de messagerie est maintenant optimisé!'
