﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class role : System.Web.UI.Page
    {
        private int info;
        RoleMembre_Class prov = new RoleMembre_Class();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre obj = new RoleMembreImp();

        IPermission objperm = new PermissionImp();
        Permission_Class perm = new Permission_Class();

        ICommonCode co = new CommonCode();

        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();

        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;
        static string nsco;


        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];
            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
                    rolid = Convert.ToInt32(role.Value);//roleconnecte

                }
                else Response.Redirect("~/login.aspx");

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;

                afficher();
                initial_msg();
                if (nsco == null)
                {
                    btnEnregistrer.InnerText = "Enregistrer le Nouveau Role";
                    // Response.Redirect("~/sima/province.aspx/");
                }
                else btnEnregistrer.InnerText = "Modifier le Role";


            }
        }
        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;

        }
       public void ReinitialiserFormulaire()
        {
            txtnm.Value ="" ;
            drpdstatut.SelectedValue="-1";
            txtdescription.Value="";
        }

        public void AjouterRole()
        {
            try
            {
                if (txtnm.Value == "" || drpdstatut.SelectedValue=="-1" || drpdtype.SelectedValue=="-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }
                else
                {
                    prov.NomRole = txtnm.Value;
                    prov.statut = drpdstatut.SelectedValue;
                    prov.OrganisationId = idorg;
                    prov.name = co.GenerateSlug(txtnm.Value+""+nameorg);
                    prov.TYPE = drpdtype.SelectedValue;
                    prov.CDROLE = co.GenerateSlug(txtnm.Value + "" + nameorg);
                    prov.Description = txtdescription.Value;


                    info = obj.Ajouter(prov);

                    if (info == 1)
                    {
                        div_msg_succes.Visible = true;
                        msg_succes.InnerText = "Le role a été enregistré avec succès";
                        ReinitialiserFormulaire();
                    }
                    else
                    {
                        div_msg_error.Visible = true;
                        msg_error.InnerText = "Erreur lors de l'enregistrement du role d'utilisateur";
                    }


                }

            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }

            //LabelMsg.Text = obj.ajouterProvince(prov).ToString();
        }

        public void ModifierRole()
        {
            try
            {
                if (txtnm.Value == "" || drpdstatut.SelectedValue == "-1" || drpdtype.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }
                else
                {

                    prov.NomRole = txtnm.Value;
                    prov.statut = drpdstatut.SelectedValue;
                    prov.name = co.GenerateSlug(txtnm.Value + "" + nameorg);
                    prov.TYPE = drpdtype.SelectedValue;
                    prov.CDROLE = co.GenerateSlug(txtnm.Value + "" + nameorg);
                    prov.Description = txtdescription.Value;

                    info = obj.Modifier(prov, Convert.ToInt32(nsco), idorg);
                    if (info == 1)
                    {
                        div_msg_succes.Visible = true;
                        msg_succes.InnerText = "Le role a été modifié avec succès";
                        ReinitialiserFormulaire();
                    }
                    else
                    {
                        div_msg_error.Visible = true;
                        msg_error.InnerText = "Erreur lors de la modification du role d'utilisateur";
                    }

                }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }


        void afficher()
        {
            if (nsco != null)
            {
                obj.AfficherDetails(Convert.ToInt32(nsco),idorg,"",0,prov);
                txtnm.Value = prov.NomRole;
                drpdtype.SelectedValue = prov.TYPE;
                drpdstatut.SelectedValue = prov.statut;
                txtdescription.Value = prov.Description;

            }

        }

        protected void btn_enreg_ServerClick(object sender, EventArgs e)
        {
            // Vérifier la validation côté serveur
            if (!Page.IsValid)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Veuillez corriger les erreurs avant de continuer.";
                return;
            }
            if (nsco == null)
            {
                AjouterRole();

            }
            else
                ModifierRole();
        }
    }
}