﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class ProjetImp: IProjet
    {
        int msg;
        private Projet projet = new Projet();

        public void AfficherDetails(long projetId, Projet_Class projetClass)
        {
            using (Connection con = new Connection())
            {
                var p = con.Projets.FirstOrDefault(x => x.ProjetId == projetId);
                if (p != null)
                {
                    projetClass.ProjetId = p.ProjetId;
                    projetClass.Titre = p.Titre;
                    projetClass.Description = p.Description;
                    projetClass.DateDebut = p.DateDebut;
                    projetClass.DateFin = p.DateFin;
                    projetClass.OrganisationId = p.OrganisationId;
                    projetClass.name = p.name;
                }
            }
        }

        public int Ajouter(Projet_Class projetClass)
        {
            using (Connection con = new Connection())
            {
                projet.Titre = projetClass.Titre;
                projet.Description = projetClass.Description;
                projet.DateDebut = projetClass.DateDebut;
                projet.DateFin = projetClass.DateFin;
                projet.OrganisationId = projetClass.OrganisationId;
                projet.name = projetClass.name;

                try
                {
                    con.Projets.Add(projet);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public int AjouterMembreEquipe(long projetId, long membreId, string role)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si le projet existe
                var projet = con.Projets.FirstOrDefault(p => p.ProjetId == projetId);
                if (projet == null)
                    return msg = 0;

                // Vérifier si le membre existe
                var membre = con.Membres.FirstOrDefault(m => m.MembreId == membreId);
                if (membre == null)
                    return msg = 0;

                // Vérifier si l'organisation du projet existe
                if (projet.OrganisationId.HasValue)
                {
                    // Vérifier si le membre n'est pas déjà associé à l'organisation
                    var membreOrg = con.MembresOrganisations.FirstOrDefault(mo =>
                        mo.OrganisationId == projet.OrganisationId.Value &&
                        mo.MembreId == membreId);

                    if (membreOrg == null)
                    {
                        // Ajouter le membre à l'organisation
                        var newMembreOrg = new MembresOrganisation
                        {
                            MembreId = membreId,
                            OrganisationId = projet.OrganisationId.Value,
                            Poste = role,
                            DateAdhesion = DateTime.Now,
                            statut = "actif",
                            name = "Membre de projet"
                        };

                        con.MembresOrganisations.Add(newMembreOrg);
                    }
                }

                try
                {
                    if (con.SaveChanges() >= 0)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

      
        public void ChargerEquipeProjet(GridView gdv, long projetId)
        {
            using (Connection con = new Connection())
            {
                var projet = con.Projets.FirstOrDefault(p => p.ProjetId == projetId);
                if (projet != null && projet.OrganisationId.HasValue)
                {
                    // Récupérer les membres de l'organisation associée au projet
                    var membres = from mo in con.MembresOrganisations
                                  join m in con.Membres on mo.MembreId equals m.MembreId
                                  where mo.OrganisationId == projet.OrganisationId.Value
                                  select new
                                  {
                                      m.MembreId,
                                      m.Nom,
                                      m.Prenom,
                                      m.Email,
                                      Poste = mo.Poste,
                                      DateAdhesion = mo.DateAdhesion,
                                      Statut = mo.statut
                                  };

                    gdv.DataSource = membres.OrderBy(m => m.Nom).ToList();
                    gdv.DataBind();
                }
                else
                {
                    // Si le projet n'a pas d'organisation associée, afficher les informations du projet
                    var info = new[] { new {
                        ProjetId = projet.ProjetId,
                        Titre = projet.Titre,
                        Description = projet.Description,
                        DateDebut = projet.DateDebut,
                        DateFin = projet.DateFin,
                        Message = "Aucune équipe associée à ce projet"
                    }};

                    gdv.DataSource = info;
                    gdv.DataBind();
                }
            }
        }

        public void ChargerGridView(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var query = from p in con.Projets
                            join o in con.Organisations on p.OrganisationId equals o.OrganisationId into organisations
                            from org in organisations.DefaultIfEmpty()
                            select new
                            {
                                p.ProjetId,
                                p.Titre,
                                p.Description,
                                Organisation = org != null ? org.Nom : "Non défini",
                                p.DateDebut,
                                p.DateFin,
                                p.name,
                                p.OrganisationId
                            };

      

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }

        public void chargerProjet(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Projets select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner le Projet";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.ProjetId.ToString();
                        item.Text = data.name;
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }


        public int Modifier(Projet_Class projetClass)
        {
            using (Connection con = new Connection())
            {
                var p = con.Projets.FirstOrDefault(x => x.ProjetId == projetClass.ProjetId);
                if (p != null)
                {
                    p.Titre = projetClass.Titre;
                    p.Description = projetClass.Description;
                    p.DateDebut = projetClass.DateDebut;
                    p.DateFin = projetClass.DateFin;
                    p.OrganisationId = projetClass.OrganisationId;
                    p.name = projetClass.name;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int RetirerMembreEquipe(long projetId, long membreId)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si le projet existe
                var projet = con.Projets.FirstOrDefault(p => p.ProjetId == projetId);
                if (projet == null || !projet.OrganisationId.HasValue)
                    return msg = 0;

                // Trouver le membre dans l'organisation du projet
                var membreOrg = con.MembresOrganisations.FirstOrDefault(mo =>
                    mo.OrganisationId == projet.OrganisationId.Value &&
                    mo.MembreId == membreId);

                if (membreOrg != null)
                {
                    // Mettre à jour le statut du membre dans l'organisation
                    membreOrg.statut = "inactif";

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }

                return msg = 0;
            }
        }

        public int Supprimer(long projetId)
        {
            using (Connection con = new Connection())
            {
                var p = con.Projets.FirstOrDefault(x => x.ProjetId == projetId);
                if (p != null)
                {
                    con.Projets.Remove(p);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}