﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IRessource
    {
        int Ajout(Ressources_Class add);
        void Chargement_GDV(GridView GV_apv, long idorg, string name, int cd);
        void Chargement_Listview(ListView GV_apv, long idorg, int iddom, long idres, string name, string statut, int cd);
        void search(GridView GV_apv, string code);
        void AfficherDetails(long id, long idorg, int cd, Ressources_Class pr);
        void AfficherDetails(string name, long idorg, int cd, Ressources_Class pr);
        int edit(Ressources_Class add, long id, long idorg);
        int supprimer(long id, long idorg);
        void chargerRessourc(DropDownList ddw, long idorg, string name, string intitule, int cd);
        int count();
        int MiseajourData(Ressources_Class add, long id, long idorg, int cd);
    }
}
