﻿<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="listressources.aspx.cs" Inherits="LinCom.file.listressources" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
                    <!-- Breadcomb area Start-->
<div class="breadcomb-area">
	<div class="container">
		<div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
				<div class="breadcomb-list">
					<div class="row">
						<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
							<div class="breadcomb-wp">
								<div class="breadcomb-icon">
									<i class="notika-icon notika-form"></i>
								</div>
								<div class="breadcomb-ctn">
									<h2>Liste des Ressources</h2>
								</div>
							</div>
						</div>
						<div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
							<div class="breadcomb-report">

								<a data-toggle="tooltip" data-placement="left"  href="ressources.aspx" title="Clique sur ce button pour creer une nouvelle" class="btn">Nouvelle Ressources</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- Breadcomb area End-->
	  <!-- Data Table area Start-->
 <div class="normal-table-area">
      <div class="container">
         <div class="row">

            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
               <div class="data-table-list">
                   <div class="table-responsive">
                       <asp:GridView ID="gdv" class="table table-striped" runat="server" CssClass="datatbemp"
                         AutoGenerateColumns="False" EmptyDataText="Aucune Donnée Trouvée pour votre Recherche"
                         GridLines="None" width="100%" OnRowCommand="gdv_RowCommand"> 
                            <AlternatingRowStyle BackColor="#DCDCDC" />
                            <Columns>
                                <asp:TemplateField HeaderText="Titre" FooterText="Titre">
                                    <ItemTemplate>
                                        <asp:Label ID="lb_titre" runat="server" Text='<%# Eval("titre") %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                               
                                <asp:TemplateField HeaderText="Type" FooterText="Type">
                                    <ItemTemplate>
                                        <asp:Label ID="lb_type" runat="server" Text='<%# Eval("typeressources") %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Date Publication" FooterText="Date Publication">
                                    <ItemTemplate>
                                        <asp:Label ID="lb_date" runat="server" Text='<%# Eval("DatePublication", "{0:dd/MM/yyyy}") %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                               
                                <asp:TemplateField HeaderText="Action" FooterText="Action">
                                    <ItemTemplate>
                                        <asp:Button ID="btnEdit" BackColor="Green" class="btn btn-info btn-fix" CommandName="view" CommandArgument='<%# Eval("id") %>' runat="server" Text="Edit" />
                                        <asp:Button class="btn btn-danger notika-btn-danger" BackColor="Red" ID="btn_del" runat="server" Text="Delete" CommandName="delete" CommandArgument='<%# Eval("id") %>' OnClientClick=" return confirm('Voulez-Vous vraiment supprimer??')" />
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <EditRowStyle Font-Bold="True"></EditRowStyle>
                            <EmptyDataRowStyle HorizontalAlign="Center" Font-Names="century" Font-Size="X-Large"></EmptyDataRowStyle>
                       </asp:GridView>
                   </div>
               </div>
           </div>
         </div>
     </div>
 </div>
 <!-- Data Table area End-->

     <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.js"></script>
      <script>
          $(document).ready(function () {
              $(".datatbemp").prepend($("<thead></thead>").append($(this).find("tr:first"))).DataTable();
          });
      </script>
</asp:Content>
