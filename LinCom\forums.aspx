﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="forums.aspx.cs" Inherits="LinCom.forums" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Bannière -->
    <div class="page-title position-relative" runat="server" id="sectblogdetail">
        <img src="assets/img/mentorat/banner-mentorat.jpg" runat="server" id="imgmento" class="img-fluid w-100" style="height: 320px; object-fit: cover;" />
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="fw-bold display-5" runat="server" id="txttitle">Forum</h1>
            <p class="lead">Rejoignez un réseau dynamique pour apprendre, guider et grandir.</p>
        </div>
    </div>
    <main class="container py-4">
        <!-- Sujet du forum -->
        <div class="card shadow-sm mb-4 border-start border-4" style="border-color: #008374;">
            <div class="card-body">
                <h3 class="card-title text-primary-emphasis mb-3" runat="server" id="txttitle1">Titre du sujet</h3>
                <div class="d-flex align-items-center mb-3">
                    <img src="~/images/skills.png" runat="server" id="imgauteur" alt="Auteur" class="rounded-circle me-2" width="40" height="40">
                    <div>
                        <strong runat="server" id="txtauteur">Auteur Nom Prénom</strong><br>
                        <small class="text-muted" runat="server" id="txtdatepub">Posté le 6 juin 2025 à 14:30</small>
                    </div>
                </div>
                <p class="card-text fs-5" runat="server" id="txtdescription">
                    Voici le contenu complet de la question posée par le jeune. Le texte peut contenir plusieurs paragraphes expliquant le problème ou la discussion souhaitée.
                </p>
                <div class="d-flex justify-content-end gap-3">
                    <button class="btn btn-outline-success btn-sm"><i class="bi bi-hand-thumbs-up"></i>J’aime</button>
                    <a href="#form-reponse" class="btn btn-outline-primary btn-sm"><i class="bi bi-reply"></i>Répondre</a>
                      <asp:HiddenField ID="hfUrlPage" runat="server" />

                    <button class="btn btn-outline-secondary btn-sm"><i class="bi bi-share"></i>Partager</button>
                </div>
            </div>
        </div>

        <!-- Liste des réponses -->
        <div class="mb-4">
            <h5 class="text-secondary mb-3">Réponses (<span runat="server" id="txtcommentpost">3</span>)</h5>
             
            <!-- Réponse unique -->
            <asp:ListView ID="listcomment" runat="server">
                <EmptyItemTemplate>Aucune donnée pour le moment.</EmptyItemTemplate>
                <ItemTemplate>
                    <div class="card mb-3 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-2">
                                <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/",Eval("Photomembre"))) %>' class="rounded-circle me-2" width="36" height="36" alt="Répondant">
                                <div>
                                    <strong><%# Eval("Nommembre") %></strong><br>
                                    <small class="text-muted">Répondu le <%# Convert.ToDateTime( Eval("DateReply")).ToString("dd/MM/yyyy") %>,  <%# GetRelativeDate(Convert.ToDateTime(Eval("DateReply"))) %></small>
                                </div>
                            </div>
                            <p class="mb-0">
                                <%# Eval("Contenu") %>
                            </p>
                        </div>
                    </div>
                </ItemTemplate>
            </asp:ListView>
            <!-- Répéter ce bloc pour chaque réponse -->
        </div>

        <!-- Formulaire de réponse -->
        <div class="card shadow-sm" id="form-reponse">
            <div class="card-body">
                <h5 class="card-title text-success mb-3">Ajouter une réponse</h5>
                <textarea runat="server" id="txtmessage" rows="4" class="form-control mb-3" placeholder="Écrivez votre réponse ici..."></textarea>
              
                <button id="btnEnvoyer" runat="server" class="btn btn-success" onserverclick="bntcomment_Click">Poster la réponse</button>
            </div>
        </div>
    </main>

</asp:Content>
