﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.file;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class forums : System.Web.UI.Page
    {
        private int info;
        Membre_Class membreClass = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objMembre = new MembreImp();
        IProvince objProvince = new ProvinceImp();
        ICommune objCommune = new CommuneImp();
        ICommonCode co = new CommonCode();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();

        ISujetForum obj = new SujetForumImp();
        SujetForum_Class pos = new SujetForum_Class();
        SujetForum_Class post = new SujetForum_Class();
        SujetForum_Class p = new SujetForum_Class();

        ReplyForum_Class com = new ReplyForum_Class();
        IReplyForum objcom = new ReplyForumImp();

        DomainePost_Class actco = new DomainePost_Class();
        IDomainePost objactco = new DomainePostImp();

        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();

        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();

        CategoriePost_Class catpost = new CategoriePost_Class();
        ICategoryPost objcatpost = new CategoriePostImp();
        INotification objnotif = new NotificationImp();
        Notification_Class notif = new Notification_Class();

        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;

        static string slug;
        protected void Page_Load(object sender, EventArgs e)
        {

          
                slug = Request.QueryString["name"];

                if (slug == null)
                {
                    Response.Redirect("~/espaceforum.aspx");
                }
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte

                }

            if (!IsPostBack)
            {
                //if (slug != null)
                //{
                initial_msg();

                hfUrlPage.Value = Request.Url.AbsoluteUri;
                //LoadString();

                //}
                //else Response.Redirect("~/news.aspx");



            }
        }

        void commentaire()
        {

            try
            {
                if (ide > 0)
                {
                    if (txtmessage.Value == "")
                    {
                        Response.Write("<script LANGUAGE=JavaScript>alert('Veuillez renseigner votre réponse.!!!')</script>");

                    }
                    else
                    {


                        obj.AfficherDetails(-1, -1, slug, "ouvert", 1, p);//appel du post

                        com.SujetForumId = Convert.ToInt64(p.SujetForumId);
                        com.MembreId = ide;
                        com.Contenu = txtmessage.Value;
                        com.DateReply = DateTime.Now;
                        com.name = "reponse";
                        com.EstSolution = 0;
                        com.NombreLikes = 0;
                        com.NombreDislikes = 0;
                        com.NombreVues = 0;
                        com.statut = "publié";
                        com.etat = "";


                        info = objcom.Ajout(com);

                        if (info > 0)
                        {
                            objcom.ChargementListview(listcomment, p.SujetForumId, -1, slug, "publié", 0);
                            //  txtcomment.InnerText = objcom.count(-1, Convert.ToInt64(p.PostId), 0) + " Commentaire(s)";
                            txtcommentpost.InnerText = objcom.count(Convert.ToInt64(p.SujetForumId), -1, 0, "publié") + " Commentaire(s)";
                            EnvoieNotification();
                            txtmessage.Value = "";
                           
                            Response.Write("<script LANGUAGE=JavaScript>alert('Votre commentaire a été bien envoyé')</script>");

                        }
                        else
                        {
                            Response.Write("<script LANGUAGE=JavaScript>alert('Problème Technique.!!!')</script>");

                        }

                    }
                }
                else Response.Redirect("~/login.aspx");

               
            }
            catch (Exception e)
            {

            }
        }
        void EnvoieNotification()
        {
            obj.AfficherDetails(-1, -1, slug, "ouvert", 1, p);//appel du post
            // Après avoir sauvé la nouvelle question dans la base
            objMembre.AfficherDetails(ide,mem);
            long auteur = ide;
            string titreSujet = p.Titre;
            string contenu = txtmessage.Value;
            string name = "Answer-Forum: " + mem.Nom+ " " + mem.Prenom +" a repondu";
            string statut = "envoyé";
            string liensujet = $"{HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority)}/forums.aspx?name=" + co.GenerateSlug(slug);

            Task.Run(() => objnotif.EnvoyerNotificationAction(auteur, titreSujet, contenu, liensujet, name, statut));


        }
        //protected void OnPagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        //{
        //    (listblog.FindControl("DataPager1") as DataPager).SetPageProperties(e.StartRowIndex, e.MaximumRows, false);
        //    obj.Chargement_GDV(listblog, 0, "blog", 7);
        //}
        protected void listpost_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            //string index = e.CommandArgument.ToString();
            //if (e.CommandName == "view")
            //{

            //    obj.AfficherDetailsname(index, -1, 1, pos);
            //    post.number_of_view = pos.number_of_view + 1;
            //    obj.MiseajourData(post, pos.PostId, -1, 0);

            //    Response.Redirect("~/mentorat.aspx?name=" + index);
            //}
        }
        public string GetRelativeDate(DateTime date)
        {
            return co.GetRelativeDate(date);
        }



        void initial_msg()
        {

            //blog
         
           // obj.Chargement(listprogram, -1, -1, "programmemontorat", "publié", 1);
            //obj.Chargement_GDV(listprogram, -1, -1, "programmementorat", "fnuap", 1);
         
            //blog
            //  obj.Chargement_GDV(listblog, 0, "blog", 3);
            //event
            // obj.Chargement_GDV(listevent, 0, "blog", 4);
            if (!string.IsNullOrEmpty(slug))
            {

                // obj.afficherDetails(Convert.ToInt32(nscno), "blog", pc);
                obj.AfficherDetails(-1,-1,slug,"ouvert", 1, p);//appel du post

                objcom.ChargementListview(listcomment,p.SujetForumId,-1,slug,"publié",0);
                objMembre.AfficherDetails(p.MembreId,mem);
                // objcom.Chargement_GDV(listcomment, 0, "", Convert.ToInt64(nscno), 0);
                // obj.Chargement_GDV(listv1, (int)pc.IDCAT, 1);
                txttitle.InnerText = p.Titre;
                imgauteur.Src = "~/file/membr/" + mem.PhotoProfil;
                txtauteur.InnerText = mem.Nom + " " + mem.Prenom;
                txtdatepub.InnerText = Convert.ToDateTime(p.DateCreation).ToString("dd/MM/yyyy") + ", " + co.GetRelativeDate(Convert.ToDateTime(p.DateCreation));
                txtdescription.InnerHtml = p.Contenu;
                txtcommentpost.InnerText = objcom.count(Convert.ToInt64(p.SujetForumId), -1, 0, "publié").ToString();

                txttitle1.InnerText = p.Titre;
               
            }
        }
        protected void btn_srch_Click(object sender, EventArgs e)
        {
            //if (txt_srch.Value == "")
            //    obj.Chargement_GDV(listv, 0, "blog", 5);
            //else obj.searchlist(listv, "blog", 0, 0, txt_srch.Value);
        }

        protected void voirtouract_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/news.aspx");
        }
        protected void voirtouractblog_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/newsupcome.aspx");
        }
        protected void bntcomment_Click(object sender, EventArgs e)
        {
            commentaire();
            //nbrecomment.InnerText = objcom.count("", Convert.ToInt32(nscno), 0) + " Commentaire(s)";

        }


        protected void listcategorie_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            //int index = Convert.ToInt32(e.CommandArgument);
            //if (e.CommandName == "viewcat")
            //{
            //    princip.Visible = false;
            //    second.Visible = true;

            //    obj.Chargement_GDV(listblog, index, "blog", 1);

            //}
        }

        protected void btnenreg_ServerClick(object sender, EventArgs e)
        {
            if (slug != null && ide > 0)
            {
                commentaire();
            }
            else Response.Redirect("~/login.aspx");
        }
    }
}