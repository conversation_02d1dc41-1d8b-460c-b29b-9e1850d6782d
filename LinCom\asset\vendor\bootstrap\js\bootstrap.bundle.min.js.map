{"version": 3, "sources": ["../../js/src/dom/selector-engine.js", "../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/base-component.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/@popperjs/core/lib/enums.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../node_modules/@popperjs/core/lib/utils/math.js", "../../node_modules/@popperjs/core/lib/utils/within.js", "../../node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../node_modules/@popperjs/core/lib/utils/getVariation.js", "../../node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../node_modules/@popperjs/core/lib/modifiers/flip.js", "../../node_modules/@popperjs/core/lib/modifiers/hide.js", "../../node_modules/@popperjs/core/lib/modifiers/offset.js", "../../node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../node_modules/@popperjs/core/lib/createPopper.js", "../../node_modules/@popperjs/core/lib/utils/debounce.js", "../../node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../node_modules/@popperjs/core/lib/popper-lite.js", "../../node_modules/@popperjs/core/lib/popper.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["SelectorEngine", "find", "selector", "element", "document", "documentElement", "concat", "Element", "prototype", "querySelectorAll", "call", "findOne", "querySelector", "children", "filter", "child", "matches", "parents", "ancestor", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "getUID", "prefix", "Math", "floor", "random", "getElementById", "getSelector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "getElementFromSelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "j<PERSON>y", "getElement", "length", "emulateTransitionEnd", "duration", "called", "emulatedDuration", "addEventListener", "listener", "removeEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "toString", "match", "toLowerCase", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "style", "elementStyle", "parentNodeStyle", "display", "visibility", "isDisabled", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "execute", "elementMap", "Map", "Data", "set", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "remove", "delete", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "customEventsRegex", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "handler", "delegationSelector", "uidEventList", "i", "len", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "getTypeEvent", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFn", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "this", "handlers", "previousFn", "replace", "dom<PERSON><PERSON>s", "target", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "on", "one", "inNamespace", "isNamespace", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "removeNamespacedHandlers", "slice", "keyHandlers", "trigger", "args", "isNative", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "BaseComponent", "constructor", "_element", "DATA_KEY", "dispose", "EVENT_KEY", "getOwnPropertyNames", "propertyName", "_queueCallback", "isAnimated", "[object Object]", "VERSION", "Error", "<PERSON><PERSON>", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "closest", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "each", "data", "alertInstance", "handle<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "Carousel", "super", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "order", "_handleSwipe", "absDeltax", "abs", "direction", "_keydown", "_addTouchEventListeners", "start", "pointerType", "touches", "clientX", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "indexOf", "_getItemByOrder", "activeElement", "isNext", "isPrev", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "eventDirectionName", "targetIndex", "fromIndex", "_setActiveIndicatorElement", "activeIndicator", "indicators", "parseInt", "elementInterval", "defaultInterval", "directionOrOrder", "_directionToOrder", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "_orderToDirection", "triggerSlidEvent", "completeCallBack", "action", "ride", "carouselInterface", "slideIndex", "dataApiClickHandler", "carousels", "parent", "Collapse", "_isTransitioning", "_triggerArray", "id", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "bottom", "right", "basePlacements", "variationPlacements", "reduce", "acc", "placement", "placements", "modifierPhases", "getNodeName", "nodeName", "getWindow", "node", "ownerDocument", "defaultView", "isHTMLElement", "HTMLElement", "isShadowRoot", "applyStyles$1", "enabled", "phase", "_ref", "state", "elements", "styles", "assign", "effect", "_ref2", "initialStyles", "popper", "options", "strategy", "margin", "arrow", "reference", "hasOwnProperty", "attribute", "requires", "getBasePlacement", "width", "height", "x", "y", "getLayoutRect", "clientRect", "offsetWidth", "rootNode", "isSameNode", "host", "isTableElement", "getDocumentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "getOffsetParent", "isFirefox", "userAgent", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "getContainingBlock", "getMainAxisFromPlacement", "max", "min", "round", "within", "mathMax", "mathMin", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "arrow$1", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "padding", "rects", "toPaddingObject", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "axisProp", "centerOffset", "_options$element", "requiresIfExists", "unsetSides", "mapToStyles", "_Object$assign2", "popperRect", "offsets", "gpuAcceleration", "adaptive", "roundOffsets", "_ref3", "dpr", "devicePixelRatio", "roundOffsetsByDPR", "_ref3$x", "_ref3$y", "hasX", "hasY", "sideX", "sideY", "win", "heightProp", "widthProp", "_Object$assign", "commonStyles", "computeStyles$1", "_ref4", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "data-popper-placement", "passive", "eventListeners", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "update", "hash", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getWindowScroll", "pageXOffset", "pageYOffset", "getWindowScrollBarX", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "listScrollParents", "list", "_element$ownerDocumen", "getScrollParent", "isBody", "visualViewport", "updatedList", "rectToClientRect", "getClientRectFromMixedType", "clippingParent", "html", "getViewportRect", "clientTop", "clientLeft", "getInnerBoundingClientRect", "winScroll", "scrollWidth", "scrollHeight", "getDocumentRect", "getVariation", "computeOffsets", "variation", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$boundary", "boundary", "_options$rootBoundary", "rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "referenceElement", "clippingClientRect", "mainClippingParents", "clippingParents", "clipperElement", "getClippingParents", "firstClippingParent", "clippingRect", "accRect", "getClippingRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "flip$1", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "oppositePlacement", "getExpandedFallbackPlacements", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "_basePlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "_loop", "_i", "fittingPlacement", "reset", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide$2", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "data-popper-reference-hidden", "data-popper-escaped", "offset$1", "_options$offset", "invertDistance", "skidding", "distance", "distanceAndSkiddingToXY", "_data$state$placement", "popperOffsets$1", "preventOverflow$1", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMin", "tetherMax", "preventedOffset", "_mainSide", "_altSide", "_offset", "_min", "_max", "_preventedOffset", "getCompositeRect", "elementOrVirtualElement", "isFixed", "isOffsetParentAnElement", "DEFAULT_OPTIONS", "modifiers", "areValidElements", "_len", "arguments", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "pending", "orderedModifiers", "effectCleanupFns", "isDestroyed", "setOptions", "cleanupModifierEffects", "merged", "map", "visited", "result", "modifier", "dep", "depModifier", "orderModifiers", "current", "existing", "m", "_ref3$options", "cleanupFn", "forceUpdate", "_state$elements", "_state$orderedModifie", "_state$orderedModifie2", "Promise", "resolve", "then", "undefined", "destroy", "onFirstUpdate", "createPopper", "computeStyles", "applyStyles", "flip", "REGEXP_KEYDOWN", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "popperConfig", "autoClose", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "getParentFromElement", "<PERSON><PERSON>", "_getPopperConfig", "isDisplayStatic", "focus", "_completeHide", "_getPlacement", "parentDropdown", "isEnd", "getPropertyValue", "_getOffset", "popperData", "defaultBsPopperConfig", "_selectMenuItem", "items", "dropdownInterface", "toggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "clickEvent", "isActive", "stopPropagation", "getToggleButton", "clearMenus", "getInstance", "click", "dataApiKeydownHandler", "getWidth", "documentWidth", "innerWidth", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "actualValue", "styleProp", "scrollbarWidth", "_resetElementAttributes", "removeProperty", "clickCallback", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "className", "append<PERSON><PERSON><PERSON>", "backdropTransitionDuration", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_isShown", "_ignoreBackdropClick", "_isAnimated", "showEvent", "scrollBarHide", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "_enforceFocus", "_triggerBackdropTransition", "_resetAdjustments", "scrollBarReset", "currentTarget", "isModalOverflowing", "modalTransitionDuration", "getScrollBarWidth", "isBodyOverflowing", "paddingLeft", "paddingRight", "<PERSON><PERSON><PERSON>", "_enforceFocusOnElement", "blur", "allReadyOpen", "el", "uriAttrs", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeValue", "regExp", "attrRegex", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "*", "area", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "updateAttachment", "dataKey", "_getDelegateConfig", "_handlePopperPlacementChange", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "tabClass", "token", "tClass", "Popover", "_getContent", "method", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "item", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "navItem", "spy", "Tab", "listElement", "itemSelector", "hideEvent", "complete", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "_onInteraction", "isInteracting"], "mappings": ";;;;;0OAaA,MAEMA,EAAiB,CACrBC,KAAI,CAACC,EAAUC,EAAUC,SAASC,kBACzB,GAAGC,UAAUC,QAAQC,UAAUC,iBAAiBC,KAAKP,EAASD,IAGvES,QAAO,CAACT,EAAUC,EAAUC,SAASC,kBAC5BE,QAAQC,UAAUI,cAAcF,KAAKP,EAASD,GAGvDW,SAAQ,CAACV,EAASD,IACT,GAAGI,UAAUH,EAAQU,UACzBC,OAAOC,GAASA,EAAMC,QAAQd,IAGnCe,QAAQd,EAASD,GACf,MAAMe,EAAU,GAEhB,IAAIC,EAAWf,EAAQgB,WAEvB,KAAOD,GAAYA,EAASE,WAAaC,KAAKC,cArBhC,IAqBgDJ,EAASE,UACjEF,EAASF,QAAQd,IACnBe,EAAQM,KAAKL,GAGfA,EAAWA,EAASC,WAGtB,OAAOF,GAGTO,KAAKrB,EAASD,GACZ,IAAIuB,EAAWtB,EAAQuB,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAAST,QAAQd,GACnB,MAAO,CAACuB,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAAKxB,EAASD,GACZ,IAAIyB,EAAOxB,EAAQyB,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKX,QAAQd,GACf,MAAO,CAACyB,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KC1CLC,EAASC,IACb,GACEA,GAAUC,KAAKC,MArBH,IAqBSD,KAAKE,gBACnB7B,SAAS8B,eAAeJ,IAEjC,OAAOA,GAGHK,EAAchC,IAClB,IAAID,EAAWC,EAAQiC,aAAa,kBAEpC,IAAKlC,GAAyB,MAAbA,EAAkB,CACjC,IAAImC,EAAWlC,EAAQiC,aAAa,QAMpC,IAAKC,IAAcA,EAASC,SAAS,OAASD,EAASE,WAAW,KAChE,OAAO,KAILF,EAASC,SAAS,OAASD,EAASE,WAAW,OACjDF,EAAY,IAAGA,EAASG,MAAM,KAAK,IAGrCtC,EAAWmC,GAAyB,MAAbA,EAAmBA,EAASI,OAAS,KAG9D,OAAOvC,GAGHwC,EAAyBvC,IAC7B,MAAMD,EAAWiC,EAAYhC,GAE7B,OAAID,GACKE,SAASQ,cAAcV,GAAYA,EAGrC,MAGHyC,EAAyBxC,IAC7B,MAAMD,EAAWiC,EAAYhC,GAE7B,OAAOD,EAAWE,SAASQ,cAAcV,GAAY,MAGjD0C,EAAmCzC,IACvC,IAAKA,EACH,OAAO,EAIT,IAAI0C,mBAAEA,EAAFC,gBAAsBA,GAAoBC,OAAOC,iBAAiB7C,GAEtE,MAAM8C,EAA0BC,OAAOC,WAAWN,GAC5CO,EAAuBF,OAAOC,WAAWL,GAG/C,OAAKG,GAA4BG,GAKjCP,EAAqBA,EAAmBL,MAAM,KAAK,GACnDM,EAAkBA,EAAgBN,MAAM,KAAK,GArFf,KAuFtBU,OAAOC,WAAWN,GAAsBK,OAAOC,WAAWL,KAPzD,GAULO,EAAuBlD,IAC3BA,EAAQmD,cAAc,IAAIC,MA1FL,mBA6FjBC,EAAYC,MACXA,GAAsB,iBAARA,UAIO,IAAfA,EAAIC,SACbD,EAAMA,EAAI,SAGmB,IAAjBA,EAAIrC,UAGduC,EAAaF,GACbD,EAAUC,GACLA,EAAIC,OAASD,EAAI,GAAKA,EAGZ,iBAARA,GAAoBA,EAAIG,OAAS,EACnC5D,EAAeW,QAAQ8C,GAGzB,KAGHI,EAAuB,CAAC1D,EAAS2D,KACrC,IAAIC,GAAS,EACb,MACMC,EAAmBF,EADD,EAQxB3D,EAAQ8D,iBA/Ha,iBA0HrB,SAASC,IACPH,GAAS,EACT5D,EAAQgE,oBA5HW,gBA4HyBD,MAI9CE,WAAW,KACJL,GACHV,EAAqBlD,IAEtB6D,IAGCK,EAAkB,CAACC,EAAeC,EAAQC,KAC9CC,OAAOC,KAAKF,GAAaG,QAAQC,IAC/B,MAAMC,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAAStB,EAAUsB,GAAS,UAvI5CrB,OADSA,EAwIsDqB,GAtIzD,GAAErB,EAGL,GAAGuB,SAAStE,KAAK+C,GAAKwB,MAAM,eAAe,GAAGC,cALxCzB,IAAAA,EA0IX,IAAK,IAAI0B,OAAON,GAAeO,KAAKL,GAClC,MAAM,IAAIM,UACP,GAAEf,EAAcgB,0BAA0BV,qBAA4BG,yBAAiCF,UAM1GU,EAAYpF,IAChB,IAAKA,EACH,OAAO,EAGT,GAAIA,EAAQqF,OAASrF,EAAQgB,YAAchB,EAAQgB,WAAWqE,MAAO,CACnE,MAAMC,EAAezC,iBAAiB7C,GAChCuF,EAAkB1C,iBAAiB7C,EAAQgB,YAEjD,MAAgC,SAAzBsE,EAAaE,SACU,SAA5BD,EAAgBC,SACY,WAA5BF,EAAaG,WAGjB,OAAO,GAGHC,EAAa1F,IACZA,GAAWA,EAAQiB,WAAaC,KAAKC,gBAItCnB,EAAQ2F,UAAUC,SAAS,mBAIC,IAArB5F,EAAQ6F,SACV7F,EAAQ6F,SAGV7F,EAAQ8F,aAAa,aAAoD,UAArC9F,EAAQiC,aAAa,aAG5D8D,EAAiB/F,IACrB,IAAKC,SAASC,gBAAgB8F,aAC5B,OAAO,KAIT,GAAmC,mBAAxBhG,EAAQiG,YAA4B,CAC7C,MAAMC,EAAOlG,EAAQiG,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIlG,aAAmBmG,WACdnG,EAIJA,EAAQgB,WAIN+E,EAAe/F,EAAQgB,YAHrB,MAMLoF,EAAO,OAEPC,EAASrG,GAAWA,EAAQsG,aAE5BC,EAAY,KAChB,MAAMC,OAAEA,GAAW5D,OAEnB,OAAI4D,IAAWvG,SAASwG,KAAKX,aAAa,qBACjCU,EAGF,MAWHE,EAAQ,IAAuC,QAAjCzG,SAASC,gBAAgByG,IAEvCC,EAAqBC,IAVAC,IAAAA,EAAAA,EAWN,KACjB,MAAMC,EAAIR,IAEV,GAAIQ,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,mBApBQ,YAAxBnH,SAASsH,WACXtH,SAAS6D,iBAAiB,mBAAoBgD,GAE9CA,KAuBEU,EAAUV,IACU,mBAAbA,GACTA,KCtPEW,EAAa,IAAIC,IAEvB,IAAAC,EAAe,CACbC,IAAI5H,EAAS6H,EAAKC,GACXL,EAAWM,IAAI/H,IAClByH,EAAWG,IAAI5H,EAAS,IAAI0H,KAG9B,MAAMM,EAAcP,EAAWQ,IAAIjI,GAI9BgI,EAAYD,IAAIF,IAA6B,IAArBG,EAAYE,KAMzCF,EAAYJ,IAAIC,EAAKC,GAJnBK,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKN,EAAYzD,QAAQ,QAOhI0D,IAAG,CAACjI,EAAS6H,IACPJ,EAAWM,IAAI/H,IACVyH,EAAWQ,IAAIjI,GAASiI,IAAIJ,IAG9B,KAGTU,OAAOvI,EAAS6H,GACd,IAAKJ,EAAWM,IAAI/H,GAClB,OAGF,MAAMgI,EAAcP,EAAWQ,IAAIjI,GAEnCgI,EAAYQ,OAAOX,GAGM,IAArBG,EAAYE,MACdT,EAAWe,OAAOxI,KCtCxB,MAAMyI,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAoB,4BACpBC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WASF,SAASC,EAAYpJ,EAASqJ,GAC5B,OAAQA,GAAQ,GAAEA,MAAQR,OAAiB7I,EAAQ6I,UAAYA,IAGjE,SAASS,EAAStJ,GAChB,MAAMqJ,EAAMD,EAAYpJ,GAKxB,OAHAA,EAAQ6I,SAAWQ,EACnBT,EAAcS,GAAOT,EAAcS,IAAQ,GAEpCT,EAAcS,GAuCvB,SAASE,EAAYC,EAAQC,EAASC,EAAqB,MACzD,MAAMC,EAAerF,OAAOC,KAAKiF,GAEjC,IAAK,IAAII,EAAI,EAAGC,EAAMF,EAAalG,OAAQmG,EAAIC,EAAKD,IAAK,CACvD,MAAME,EAAQN,EAAOG,EAAaC,IAElC,GAAIE,EAAMC,kBAAoBN,GAAWK,EAAMJ,qBAAuBA,EACpE,OAAOI,EAIX,OAAO,KAGT,SAASE,EAAgBC,EAAmBR,EAASS,GACnD,MAAMC,EAAgC,iBAAZV,EACpBM,EAAkBI,EAAaD,EAAeT,EAEpD,IAAIW,EAAYC,EAAaJ,GAO7B,OANiBf,EAAanB,IAAIqC,KAGhCA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASE,EAAWtK,EAASiK,EAAmBR,EAASS,EAAcK,GACrE,GAAiC,iBAAtBN,IAAmCjK,EAC5C,OAUF,GAPKyJ,IACHA,EAAUS,EACVA,EAAe,MAKbjB,EAAkBhE,KAAKgF,GAAoB,CAC7C,MAAMO,EAASrD,GACN,SAAU2C,GACf,IAAKA,EAAMW,eAAkBX,EAAMW,gBAAkBX,EAAMY,iBAAmBZ,EAAMY,eAAe9E,SAASkE,EAAMW,eAChH,OAAOtD,EAAG5G,KAAKoK,KAAMb,IAKvBI,EACFA,EAAeM,EAAON,GAEtBT,EAAUe,EAAOf,GAIrB,MAAOU,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBR,EAASS,GACvFV,EAASF,EAAStJ,GAClB4K,EAAWpB,EAAOY,KAAeZ,EAAOY,GAAa,IACrDS,EAAatB,EAAYqB,EAAUb,EAAiBI,EAAaV,EAAU,MAEjF,GAAIoB,EAGF,YAFAA,EAAWN,OAASM,EAAWN,QAAUA,GAK3C,MAAMlB,EAAMD,EAAYW,EAAiBE,EAAkBa,QAAQrC,EAAgB,KAC7EtB,EAAKgD,EA5Fb,SAAoCnK,EAASD,EAAUoH,GACrD,OAAO,SAASsC,EAAQK,GACtB,MAAMiB,EAAc/K,EAAQM,iBAAiBP,GAE7C,IAAK,IAAIiL,OAAEA,GAAWlB,EAAOkB,GAAUA,IAAWL,KAAMK,EAASA,EAAOhK,WACtE,IAAK,IAAI4I,EAAImB,EAAYtH,OAAQmG,KAC/B,GAAImB,EAAYnB,KAAOoB,EAQrB,OAPAlB,EAAMY,eAAiBM,EAEnBvB,EAAQc,QAEVU,EAAaC,IAAIlL,EAAS8J,EAAMqB,KAAMpL,EAAUoH,GAG3CA,EAAGiE,MAAMJ,EAAQ,CAAClB,IAM/B,OAAO,MAyEPuB,CAA2BrL,EAASyJ,EAASS,GAzGjD,SAA0BlK,EAASmH,GACjC,OAAO,SAASsC,EAAQK,GAOtB,OANAA,EAAMY,eAAiB1K,EAEnByJ,EAAQc,QACVU,EAAaC,IAAIlL,EAAS8J,EAAMqB,KAAMhE,GAGjCA,EAAGiE,MAAMpL,EAAS,CAAC8J,KAkG1BwB,CAAiBtL,EAASyJ,GAE5BtC,EAAGuC,mBAAqBS,EAAaV,EAAU,KAC/CtC,EAAG4C,gBAAkBA,EACrB5C,EAAGoD,OAASA,EACZpD,EAAG0B,SAAWQ,EACduB,EAASvB,GAAOlC,EAEhBnH,EAAQ8D,iBAAiBsG,EAAWjD,EAAIgD,GAG1C,SAASoB,EAAcvL,EAASwJ,EAAQY,EAAWX,EAASC,GAC1D,MAAMvC,EAAKoC,EAAYC,EAAOY,GAAYX,EAASC,GAE9CvC,IAILnH,EAAQgE,oBAAoBoG,EAAWjD,EAAIqE,QAAQ9B,WAC5CF,EAAOY,GAAWjD,EAAG0B,WAe9B,SAASwB,EAAaP,GAGpB,OADAA,EAAQA,EAAMgB,QAAQpC,EAAgB,IAC/BI,EAAagB,IAAUA,EAGhC,MAAMmB,EAAe,CACnBQ,GAAGzL,EAAS8J,EAAOL,EAASS,GAC1BI,EAAWtK,EAAS8J,EAAOL,EAASS,GAAc,IAGpDwB,IAAI1L,EAAS8J,EAAOL,EAASS,GAC3BI,EAAWtK,EAAS8J,EAAOL,EAASS,GAAc,IAGpDgB,IAAIlL,EAASiK,EAAmBR,EAASS,GACvC,GAAiC,iBAAtBD,IAAmCjK,EAC5C,OAGF,MAAOmK,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBR,EAASS,GACvFyB,EAAcvB,IAAcH,EAC5BT,EAASF,EAAStJ,GAClB4L,EAAc3B,EAAkB7H,WAAW,KAEjD,QAA+B,IAApB2H,EAAiC,CAE1C,IAAKP,IAAWA,EAAOY,GACrB,OAIF,YADAmB,EAAcvL,EAASwJ,EAAQY,EAAWL,EAAiBI,EAAaV,EAAU,MAIhFmC,GACFtH,OAAOC,KAAKiF,GAAQhF,QAAQqH,KAhDlC,SAAkC7L,EAASwJ,EAAQY,EAAW0B,GAC5D,MAAMC,EAAoBvC,EAAOY,IAAc,GAE/C9F,OAAOC,KAAKwH,GAAmBvH,QAAQwH,IACrC,GAAIA,EAAW7J,SAAS2J,GAAY,CAClC,MAAMhC,EAAQiC,EAAkBC,GAEhCT,EAAcvL,EAASwJ,EAAQY,EAAWN,EAAMC,gBAAiBD,EAAMJ,uBA0CrEuC,CAAyBjM,EAASwJ,EAAQqC,EAAc5B,EAAkBiC,MAAM,MAIpF,MAAMH,EAAoBvC,EAAOY,IAAc,GAC/C9F,OAAOC,KAAKwH,GAAmBvH,QAAQ2H,IACrC,MAAMH,EAAaG,EAAYrB,QAAQnC,EAAe,IAEtD,IAAKgD,GAAe1B,EAAkB9H,SAAS6J,GAAa,CAC1D,MAAMlC,EAAQiC,EAAkBI,GAEhCZ,EAAcvL,EAASwJ,EAAQY,EAAWN,EAAMC,gBAAiBD,EAAMJ,wBAK7E0C,QAAQpM,EAAS8J,EAAOuC,GACtB,GAAqB,iBAAVvC,IAAuB9J,EAChC,OAAO,KAGT,MAAM+G,EAAIR,IACJ6D,EAAYC,EAAaP,GACzB6B,EAAc7B,IAAUM,EACxBkC,EAAWpD,EAAanB,IAAIqC,GAElC,IAAImC,EACAC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CIhB,GAAe5E,IACjBwF,EAAcxF,EAAE3D,MAAM0G,EAAOuC,GAE7BtF,EAAE/G,GAASoM,QAAQG,GACnBC,GAAWD,EAAYK,uBACvBH,GAAkBF,EAAYM,gCAC9BH,EAAmBH,EAAYO,sBAG7BR,GACFK,EAAM1M,SAAS8M,YAAY,cAC3BJ,EAAIK,UAAU5C,EAAWoC,GAAS,IAElCG,EAAM,IAAIM,YAAYnD,EAAO,CAC3B0C,QAAAA,EACAU,YAAY,SAKI,IAATb,GACT/H,OAAOC,KAAK8H,GAAM7H,QAAQqD,IACxBvD,OAAO6I,eAAeR,EAAK9E,EAAK,CAC9BI,IAAG,IACMoE,EAAKxE,OAMhB6E,GACFC,EAAIS,iBAGFX,GACFzM,EAAQmD,cAAcwJ,GAGpBA,EAAID,uBAA2C,IAAhBH,GACjCA,EAAYa,iBAGPT,IChUX,MAAMU,EACJC,YAAYtN,IACVA,EAAUwD,EAAWxD,MAMrB2K,KAAK4C,SAAWvN,EAChB2H,EAAKC,IAAI+C,KAAK4C,SAAU5C,KAAK2C,YAAYE,SAAU7C,OAGrD8C,UACE9F,EAAKY,OAAOoC,KAAK4C,SAAU5C,KAAK2C,YAAYE,UAC5CvC,EAAaC,IAAIP,KAAK4C,SAAU5C,KAAK2C,YAAYI,WAEjDpJ,OAAOqJ,oBAAoBhD,MAAMnG,QAAQoJ,IACvCjD,KAAKiD,GAAgB,OAIzBC,eAAe/G,EAAU9G,EAAS8N,GAAa,GAC7C,IAAKA,EAEH,YADAtG,EAAQV,GAIV,MAAMpE,EAAqBD,EAAiCzC,GAC5DiL,EAAaS,IAAI1L,EAAS,gBAAiB,IAAMwH,EAAQV,IAEzDpD,EAAqB1D,EAAS0C,GAKdqL,mBAAC/N,GACjB,OAAO2H,EAAKM,IAAIjI,EAAS2K,KAAK6C,UAGdQ,qBAChB,MA1CY,QA6CC/G,kBACb,MAAM,IAAIgH,MAAM,uEAGCT,sBACjB,MAAQ,MAAK7C,KAAK1D,KAGAyG,uBAClB,MAAQ,IAAG/C,KAAK6C,UClCpB,MAAMU,UAAcb,EAGHpG,kBACb,MAzBS,QA8BXkH,MAAMnO,GACJ,MAAMoO,EAAcpO,EAAU2K,KAAK0D,gBAAgBrO,GAAW2K,KAAK4C,SAC7De,EAAc3D,KAAK4D,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAY5B,kBAIxC/B,KAAK6D,eAAeJ,GAKtBC,gBAAgBrO,GACd,OAAOwC,EAAuBxC,IAAYA,EAAQyO,QAAS,UAG7DF,mBAAmBvO,GACjB,OAAOiL,EAAamB,QAAQpM,EAzCX,kBA4CnBwO,eAAexO,GACbA,EAAQ2F,UAAU4C,OAvCE,QAyCpB,MAAMuF,EAAa9N,EAAQ2F,UAAUC,SA1CjB,QA2CpB+E,KAAKkD,eAAe,IAAMlD,KAAK+D,gBAAgB1O,GAAUA,EAAS8N,GAGpEY,gBAAgB1O,GACVA,EAAQgB,YACVhB,EAAQgB,WAAW2N,YAAY3O,GAGjCiL,EAAamB,QAAQpM,EAvDH,mBA4DE+N,uBAAC3J,GACrB,OAAOuG,KAAKiE,MAAK,WACf,IAAIC,EAAOlH,EAAKM,IAAI0C,KArET,YAuENkE,IACHA,EAAO,IAAIX,EAAMvD,OAGJ,UAAXvG,GACFyK,EAAKzK,GAAQuG,SAKCoD,qBAACe,GACnB,OAAO,SAAUhF,GACXA,GACFA,EAAMsD,iBAGR0B,EAAcX,MAAMxD,QAW1BM,EAAaQ,GAAGxL,SA1Fc,0BAJL,4BA8FyCiO,EAAMa,cAAc,IAAIb,IAS1FtH,EAAmBsH,GC9FnB,MAAMc,UAAe3B,EAGJpG,kBACb,MArBS,SA0BXgI,SAEEtE,KAAK4C,SAAS2B,aAAa,eAAgBvE,KAAK4C,SAAS5H,UAAUsJ,OAvB7C,WA4BFlB,uBAAC3J,GACrB,OAAOuG,KAAKiE,MAAK,WACf,IAAIC,EAAOlH,EAAKM,IAAI0C,KAlCT,aAoCNkE,IACHA,EAAO,IAAIG,EAAOrE,OAGL,WAAXvG,GACFyK,EAAKzK,SCrDb,SAAS+K,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQrM,OAAOqM,GAAKvK,WACf9B,OAAOqM,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASC,EAAiBxH,GACxB,OAAOA,EAAIiD,QAAQ,SAAUwE,GAAQ,IAAGA,EAAIvK,eD4C9CkG,EAAaQ,GAAGxL,SA7Cc,2BAFD,4BA+CyC6J,IACpEA,EAAMsD,iBAEN,MAAMmC,EAASzF,EAAMkB,OAAOyD,QAlDD,6BAoD3B,IAAII,EAAOlH,EAAKM,IAAIsH,EA1DL,aA2DVV,IACHA,EAAO,IAAIG,EAAOO,IAGpBV,EAAKI,WAUPrI,EAAmBoI,GC7DnB,MAAMQ,EAAc,CAClBC,iBAAiBzP,EAAS6H,EAAKlD,GAC7B3E,EAAQkP,aAAc,WAAUG,EAAiBxH,GAAQlD,IAG3D+K,oBAAoB1P,EAAS6H,GAC3B7H,EAAQ2P,gBAAiB,WAAUN,EAAiBxH,KAGtD+H,kBAAkB5P,GAChB,IAAKA,EACH,MAAO,GAGT,MAAM6P,EAAa,GAUnB,OARAvL,OAAOC,KAAKvE,EAAQ8P,SACjBnP,OAAOkH,GAAOA,EAAIzF,WAAW,OAC7BoC,QAAQqD,IACP,IAAIkI,EAAUlI,EAAIiD,QAAQ,MAAO,IACjCiF,EAAUA,EAAQC,OAAO,GAAGjL,cAAgBgL,EAAQ7D,MAAM,EAAG6D,EAAQtM,QACrEoM,EAAWE,GAAWZ,EAAcnP,EAAQ8P,QAAQjI,MAGjDgI,GAGTI,iBAAgB,CAACjQ,EAAS6H,IACjBsH,EAAcnP,EAAQiC,aAAc,WAAUoN,EAAiBxH,KAGxEqI,OAAOlQ,GACL,MAAMmQ,EAAOnQ,EAAQoQ,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAMpQ,SAASwG,KAAK6J,UAC9BC,KAAMJ,EAAKI,KAAOtQ,SAASwG,KAAK+J,aAIpCC,SAASzQ,IACA,CACLqQ,IAAKrQ,EAAQ0Q,UACbH,KAAMvQ,EAAQ2Q,cCpCdC,EAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,EAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAGHE,EAAa,OACbC,EAAa,OACbC,EAAiB,OACjBC,EAAkB,QA2CxB,MAAMC,UAAiBnE,EACrBC,YAAYtN,EAASoE,GACnBqN,MAAMzR,GAEN2K,KAAK+G,OAAS,KACd/G,KAAKgH,UAAY,KACjBhH,KAAKiH,eAAiB,KACtBjH,KAAKkH,WAAY,EACjBlH,KAAKmH,YAAa,EAClBnH,KAAKoH,aAAe,KACpBpH,KAAKqH,YAAc,EACnBrH,KAAKsH,YAAc,EAEnBtH,KAAKuH,QAAUvH,KAAKwH,WAAW/N,GAC/BuG,KAAKyH,mBAAqBvS,EAAeW,QA3BjB,uBA2B8CmK,KAAK4C,UAC3E5C,KAAK0H,gBAAkB,iBAAkBpS,SAASC,iBAAmBoS,UAAUC,eAAiB,EAChG5H,KAAK6H,cAAgBhH,QAAQ5I,OAAO6P,cAEpC9H,KAAK+H,qBAKW9B,qBAChB,OAAOA,EAGM3J,kBACb,MAtGS,WA2GXzF,OACOmJ,KAAKmH,YACRnH,KAAKgI,OAAOvB,GAIhBwB,mBAGO3S,SAAS4S,QAAUzN,EAAUuF,KAAK4C,WACrC5C,KAAKnJ,OAITH,OACOsJ,KAAKmH,YACRnH,KAAKgI,OAAOtB,GAIhBL,MAAMlH,GACCA,IACHa,KAAKkH,WAAY,GAGfhS,EAAeW,QAxEI,2CAwEwBmK,KAAK4C,YAClDrK,EAAqByH,KAAK4C,UAC1B5C,KAAKmI,OAAM,IAGbC,cAAcpI,KAAKgH,WACnBhH,KAAKgH,UAAY,KAGnBmB,MAAMhJ,GACCA,IACHa,KAAKkH,WAAY,GAGflH,KAAKgH,YACPoB,cAAcpI,KAAKgH,WACnBhH,KAAKgH,UAAY,MAGfhH,KAAKuH,SAAWvH,KAAKuH,QAAQrB,WAAalG,KAAKkH,YACjDlH,KAAKqI,kBAELrI,KAAKgH,UAAYsB,aACdhT,SAASiT,gBAAkBvI,KAAKiI,gBAAkBjI,KAAKnJ,MAAM2R,KAAKxI,MACnEA,KAAKuH,QAAQrB,WAKnBuC,GAAGC,GACD1I,KAAKiH,eAAiB/R,EAAeW,QAzGZ,wBAyG0CmK,KAAK4C,UACxE,MAAM+F,EAAc3I,KAAK4I,cAAc5I,KAAKiH,gBAE5C,GAAIyB,EAAQ1I,KAAK+G,OAAOjO,OAAS,GAAK4P,EAAQ,EAC5C,OAGF,GAAI1I,KAAKmH,WAEP,YADA7G,EAAaS,IAAIf,KAAK4C,SAxIR,mBAwI8B,IAAM5C,KAAKyI,GAAGC,IAI5D,GAAIC,IAAgBD,EAGlB,OAFA1I,KAAKqG,aACLrG,KAAKmI,QAIP,MAAMU,EAAQH,EAAQC,EACpBlC,EACAC,EAEF1G,KAAKgI,OAAOa,EAAO7I,KAAK+G,OAAO2B,IAKjClB,WAAW/N,GAMT,OALAA,EAAS,IACJwM,KACAxM,GAELF,EAlMS,WAkMaE,EAAQ+M,GACvB/M,EAGTqP,eACE,MAAMC,EAAY9R,KAAK+R,IAAIhJ,KAAKsH,aAEhC,GAAIyB,GAjMgB,GAkMlB,OAGF,MAAME,EAAYF,EAAY/I,KAAKsH,YAEnCtH,KAAKsH,YAAc,EAEd2B,GAILjJ,KAAKgI,OAAOiB,EAAY,EAAIrC,EAAkBD,GAGhDoB,qBACM/H,KAAKuH,QAAQpB,UACf7F,EAAaQ,GAAGd,KAAK4C,SAvLJ,sBAuL6BzD,GAASa,KAAKkJ,SAAS/J,IAG5C,UAAvBa,KAAKuH,QAAQlB,QACf/F,EAAaQ,GAAGd,KAAK4C,SA1LD,yBA0L6BzD,GAASa,KAAKqG,MAAMlH,IACrEmB,EAAaQ,GAAGd,KAAK4C,SA1LD,yBA0L6BzD,GAASa,KAAKmI,MAAMhJ,KAGnEa,KAAKuH,QAAQhB,OAASvG,KAAK0H,iBAC7B1H,KAAKmJ,0BAITA,0BACE,MAAMC,EAAQjK,KACRa,KAAK6H,eAtKU,QAsKQ1I,EAAMkK,aAvKZ,UAuKgDlK,EAAMkK,YAE/DrJ,KAAK6H,gBACf7H,KAAKqH,YAAclI,EAAMmK,QAAQ,GAAGC,SAFpCvJ,KAAKqH,YAAclI,EAAMoK,SAMvBC,EAAOrK,IAEXa,KAAKsH,YAAcnI,EAAMmK,SAAWnK,EAAMmK,QAAQxQ,OAAS,EACzD,EACAqG,EAAMmK,QAAQ,GAAGC,QAAUvJ,KAAKqH,aAG9BoC,EAAMtK,KACNa,KAAK6H,eArLU,QAqLQ1I,EAAMkK,aAtLZ,UAsLgDlK,EAAMkK,cACzErJ,KAAKsH,YAAcnI,EAAMoK,QAAUvJ,KAAKqH,aAG1CrH,KAAK8I,eACsB,UAAvB9I,KAAKuH,QAAQlB,QASfrG,KAAKqG,QACDrG,KAAKoH,cACPsC,aAAa1J,KAAKoH,cAGpBpH,KAAKoH,aAAe9N,WAAW6F,GAASa,KAAKmI,MAAMhJ,GApQ5B,IAoQ6Da,KAAKuH,QAAQrB,YAIrGhR,EAAeC,KApNO,qBAoNiB6K,KAAK4C,UAAU/I,QAAQ8P,IAC5DrJ,EAAaQ,GAAG6I,EArOI,wBAqOuBC,GAAKA,EAAEnH,oBAGhDzC,KAAK6H,eACPvH,EAAaQ,GAAGd,KAAK4C,SA3OA,0BA2O6BzD,GAASiK,EAAMjK,IACjEmB,EAAaQ,GAAGd,KAAK4C,SA3OF,wBA2O6BzD,GAASsK,EAAItK,IAE7Da,KAAK4C,SAAS5H,UAAU6O,IAjOG,mBAmO3BvJ,EAAaQ,GAAGd,KAAK4C,SAnPD,yBAmP6BzD,GAASiK,EAAMjK,IAChEmB,EAAaQ,GAAGd,KAAK4C,SAnPF,wBAmP6BzD,GAASqK,EAAKrK,IAC9DmB,EAAaQ,GAAGd,KAAK4C,SAnPH,uBAmP6BzD,GAASsK,EAAItK,KAIhE+J,SAAS/J,GACH,kBAAkB7E,KAAK6E,EAAMkB,OAAOyJ,WA3RrB,cA+Rf3K,EAAMjC,KACRiC,EAAMsD,iBACNzC,KAAKgI,OAAOpB,IAhSM,eAiSTzH,EAAMjC,MACfiC,EAAMsD,iBACNzC,KAAKgI,OAAOrB,KAIhBiC,cAAcvT,GAKZ,OAJA2K,KAAK+G,OAAS1R,GAAWA,EAAQgB,WAC/BnB,EAAeC,KArPC,iBAqPmBE,EAAQgB,YAC3C,GAEK2J,KAAK+G,OAAOgD,QAAQ1U,GAG7B2U,gBAAgBnB,EAAOoB,GACrB,MAAMC,EAASrB,IAAUpC,EACnB0D,EAAStB,IAAUnC,EACnBiC,EAAc3I,KAAK4I,cAAcqB,GACjCG,EAAgBpK,KAAK+G,OAAOjO,OAAS,EAG3C,IAFuBqR,GAA0B,IAAhBxB,GAAuBuB,GAAUvB,IAAgByB,KAE5DpK,KAAKuH,QAAQjB,KACjC,OAAO2D,EAGT,MACMI,GAAa1B,GADLwB,GAAU,EAAI,IACcnK,KAAK+G,OAAOjO,OAEtD,OAAsB,IAAfuR,EACLrK,KAAK+G,OAAO/G,KAAK+G,OAAOjO,OAAS,GACjCkH,KAAK+G,OAAOsD,GAGhBC,mBAAmBxK,EAAeyK,GAChC,MAAMC,EAAcxK,KAAK4I,cAAc9I,GACjC2K,EAAYzK,KAAK4I,cAAc1T,EAAeW,QAjR3B,wBAiRyDmK,KAAK4C,WAEvF,OAAOtC,EAAamB,QAAQzB,KAAK4C,SA3ShB,oBA2SuC,CACtD9C,cAAAA,EACAmJ,UAAWsB,EACX5M,KAAM8M,EACNhC,GAAI+B,IAIRE,2BAA2BrV,GACzB,GAAI2K,KAAKyH,mBAAoB,CAC3B,MAAMkD,EAAkBzV,EAAeW,QA9RrB,UA8R8CmK,KAAKyH,oBAErEkD,EAAgB3P,UAAU4C,OAxSN,UAySpB+M,EAAgB3F,gBAAgB,gBAEhC,MAAM4F,EAAa1V,EAAeC,KA7Rb,mBA6RsC6K,KAAKyH,oBAEhE,IAAK,IAAIxI,EAAI,EAAGA,EAAI2L,EAAW9R,OAAQmG,IACrC,GAAI7G,OAAOyS,SAASD,EAAW3L,GAAG3H,aAAa,oBAAqB,MAAQ0I,KAAK4I,cAAcvT,GAAU,CACvGuV,EAAW3L,GAAGjE,UAAU6O,IA/SR,UAgThBe,EAAW3L,GAAGsF,aAAa,eAAgB,QAC3C,QAMR8D,kBACE,MAAMhT,EAAU2K,KAAKiH,gBAAkB/R,EAAeW,QA/S7B,wBA+S2DmK,KAAK4C,UAEzF,IAAKvN,EACH,OAGF,MAAMyV,EAAkB1S,OAAOyS,SAASxV,EAAQiC,aAAa,oBAAqB,IAE9EwT,GACF9K,KAAKuH,QAAQwD,gBAAkB/K,KAAKuH,QAAQwD,iBAAmB/K,KAAKuH,QAAQrB,SAC5ElG,KAAKuH,QAAQrB,SAAW4E,GAExB9K,KAAKuH,QAAQrB,SAAWlG,KAAKuH,QAAQwD,iBAAmB/K,KAAKuH,QAAQrB,SAIzE8B,OAAOgD,EAAkB3V,GACvB,MAAMwT,EAAQ7I,KAAKiL,kBAAkBD,GAC/Bf,EAAgB/U,EAAeW,QAjUZ,wBAiU0CmK,KAAK4C,UAClEsI,EAAqBlL,KAAK4I,cAAcqB,GACxCkB,EAAc9V,GAAW2K,KAAKgK,gBAAgBnB,EAAOoB,GAErDmB,EAAmBpL,KAAK4I,cAAcuC,GACtCE,EAAYxK,QAAQb,KAAKgH,WAEzBkD,EAASrB,IAAUpC,EACnB6E,EAAuBpB,EA/UR,sBADF,oBAiVbqB,EAAiBrB,EA/UH,qBACA,qBA+UdK,EAAqBvK,KAAKwL,kBAAkB3C,GAElD,GAAIsC,GAAeA,EAAYnQ,UAAUC,SAtVnB,UAwVpB,YADA+E,KAAKmH,YAAa,GAKpB,GADmBnH,KAAKsK,mBAAmBa,EAAaZ,GACzCxI,iBACb,OAGF,IAAKkI,IAAkBkB,EAErB,OAGFnL,KAAKmH,YAAa,EAEdkE,GACFrL,KAAKqG,QAGPrG,KAAK0K,2BAA2BS,GAChCnL,KAAKiH,eAAiBkE,EAEtB,MAAMM,EAAmB,KACvBnL,EAAamB,QAAQzB,KAAK4C,SA7XZ,mBA6XkC,CAC9C9C,cAAeqL,EACflC,UAAWsB,EACX5M,KAAMuN,EACNzC,GAAI2C,KAIR,GAAIpL,KAAK4C,SAAS5H,UAAUC,SAtXP,SAsXmC,CACtDkQ,EAAYnQ,UAAU6O,IAAI0B,GAE1B7P,EAAOyP,GAEPlB,EAAcjP,UAAU6O,IAAIyB,GAC5BH,EAAYnQ,UAAU6O,IAAIyB,GAE1B,MAAMI,EAAmB,KACvBP,EAAYnQ,UAAU4C,OAAO0N,EAAsBC,GACnDJ,EAAYnQ,UAAU6O,IAjYJ,UAmYlBI,EAAcjP,UAAU4C,OAnYN,SAmYgC2N,EAAgBD,GAElEtL,KAAKmH,YAAa,EAElB7N,WAAWmS,EAAkB,IAG/BzL,KAAKkD,eAAewI,EAAkBzB,GAAe,QAErDA,EAAcjP,UAAU4C,OA5YJ,UA6YpBuN,EAAYnQ,UAAU6O,IA7YF,UA+YpB7J,KAAKmH,YAAa,EAClBsE,IAGEJ,GACFrL,KAAKmI,QAIT8C,kBAAkBhC,GAChB,MAAK,CAACrC,EAAiBD,GAAgBnP,SAASyR,GAI5ClN,IACKkN,IAActC,EAAiBD,EAAaD,EAG9CwC,IAActC,EAAiBF,EAAaC,EAP1CuC,EAUXuC,kBAAkB3C,GAChB,MAAK,CAACpC,EAAYC,GAAYlP,SAASqR,GAInC9M,IACK8M,IAAUnC,EAAaC,EAAiBC,EAG1CiC,IAAUnC,EAAaE,EAAkBD,EAPvCkC,EAYazF,yBAAC/N,EAASoE,GAChC,IAAIyK,EAAOlH,EAAKM,IAAIjI,EAleP,eAmeTkS,EAAU,IACTtB,KACApB,EAAYI,kBAAkB5P,IAGb,iBAAXoE,IACT8N,EAAU,IACLA,KACA9N,IAIP,MAAMkS,EAA2B,iBAAXlS,EAAsBA,EAAS8N,EAAQnB,MAM7D,GAJKlC,IACHA,EAAO,IAAI2C,EAASxR,EAASkS,IAGT,iBAAX9N,EACTyK,EAAKuE,GAAGhP,QACH,GAAsB,iBAAXkS,EAAqB,CACrC,QAA4B,IAAjBzH,EAAKyH,GACd,MAAM,IAAIpR,UAAW,oBAAmBoR,MAG1CzH,EAAKyH,UACIpE,EAAQrB,UAAYqB,EAAQqE,OACrC1H,EAAKmC,QACLnC,EAAKiE,SAIa/E,uBAAC3J,GACrB,OAAOuG,KAAKiE,MAAK,WACf4C,EAASgF,kBAAkB7L,KAAMvG,MAIX2J,2BAACjE,GACzB,MAAMkB,EAASxI,EAAuBmI,MAEtC,IAAKK,IAAWA,EAAOrF,UAAUC,SA9dT,YA+dtB,OAGF,MAAMxB,EAAS,IACVoL,EAAYI,kBAAkB5E,MAC9BwE,EAAYI,kBAAkBjF,OAE7B8L,EAAa9L,KAAK1I,aAAa,oBAEjCwU,IACFrS,EAAOyM,UAAW,GAGpBW,EAASgF,kBAAkBxL,EAAQ5G,GAE/BqS,GACF9O,EAAKM,IAAI+C,EA7hBE,eA6hBgBoI,GAAGqD,GAGhC3M,EAAMsD,kBAUVnC,EAAaQ,GAAGxL,SA9fc,6BAkBF,sCA4eyCuR,EAASkF,qBAE9EzL,EAAaQ,GAAG7I,OAjgBa,4BAigBgB,KAC3C,MAAM+T,EAAY9W,EAAeC,KA9eR,6BAgfzB,IAAK,IAAI8J,EAAI,EAAGC,EAAM8M,EAAUlT,OAAQmG,EAAIC,EAAKD,IAC/C4H,EAASgF,kBAAkBG,EAAU/M,GAAIjC,EAAKM,IAAI0O,EAAU/M,GAhjB/C,kBA2jBjBhD,EAAmB4K,GC7jBnB,MAKMZ,EAAU,CACd3B,QAAQ,EACR2H,OAAQ,IAGJzF,EAAc,CAClBlC,OAAQ,UACR2H,OAAQ,oBA0BV,MAAMC,UAAiBxJ,EACrBC,YAAYtN,EAASoE,GACnBqN,MAAMzR,GAEN2K,KAAKmM,kBAAmB,EACxBnM,KAAKuH,QAAUvH,KAAKwH,WAAW/N,GAC/BuG,KAAKoM,cAAgBlX,EAAeC,KACjC,sCAAiC6K,KAAK4C,SAASyJ,qDACJrM,KAAK4C,SAASyJ,QAG5D,MAAMC,EAAapX,EAAeC,KAnBT,+BAqBzB,IAAK,IAAI8J,EAAI,EAAGC,EAAMoN,EAAWxT,OAAQmG,EAAIC,EAAKD,IAAK,CACrD,MAAMsN,EAAOD,EAAWrN,GAClB7J,EAAWwC,EAAuB2U,GAClCC,EAAgBtX,EAAeC,KAAKC,GACvCY,OAAOyW,GAAaA,IAAczM,KAAK4C,UAEzB,OAAbxN,GAAqBoX,EAAc1T,SACrCkH,KAAK0M,UAAYtX,EACjB4K,KAAKoM,cAAc3V,KAAK8V,IAI5BvM,KAAK2M,QAAU3M,KAAKuH,QAAQ0E,OAASjM,KAAK4M,aAAe,KAEpD5M,KAAKuH,QAAQ0E,QAChBjM,KAAK6M,0BAA0B7M,KAAK4C,SAAU5C,KAAKoM,eAGjDpM,KAAKuH,QAAQjD,QACftE,KAAKsE,SAMS2B,qBAChB,OAAOA,EAGM3J,kBACb,MAjFS,WAsFXgI,SACMtE,KAAK4C,SAAS5H,UAAUC,SAlER,QAmElB+E,KAAK8M,OAEL9M,KAAK+M,OAITA,OACE,GAAI/M,KAAKmM,kBAAoBnM,KAAK4C,SAAS5H,UAAUC,SA1EjC,QA2ElB,OAGF,IAAI+R,EACAC,EAEAjN,KAAK2M,UACPK,EAAU9X,EAAeC,KA1EN,qBA0E6B6K,KAAK2M,SAClD3W,OAAOuW,GAC6B,iBAAxBvM,KAAKuH,QAAQ0E,OACfM,EAAKjV,aAAa,oBAAsB0I,KAAKuH,QAAQ0E,OAGvDM,EAAKvR,UAAUC,SAvFJ,aA0FC,IAAnB+R,EAAQlU,SACVkU,EAAU,OAId,MAAME,EAAYhY,EAAeW,QAAQmK,KAAK0M,WAC9C,GAAIM,EAAS,CACX,MAAMG,EAAiBH,EAAQ7X,KAAKoX,GAAQW,IAAcX,GAG1D,GAFAU,EAAcE,EAAiBnQ,EAAKM,IAAI6P,EAvH7B,eAuHyD,KAEhEF,GAAeA,EAAYd,iBAC7B,OAKJ,GADmB7L,EAAamB,QAAQzB,KAAK4C,SAhH7B,oBAiHDb,iBACb,OAGEiL,GACFA,EAAQnT,QAAQuT,IACVF,IAAcE,GAChBlB,EAASmB,kBAAkBD,EAAY,QAGpCH,GACHjQ,EAAKC,IAAImQ,EA1IF,cA0IwB,QAKrC,MAAME,EAAYtN,KAAKuN,gBAEvBvN,KAAK4C,SAAS5H,UAAU4C,OA5HA,YA6HxBoC,KAAK4C,SAAS5H,UAAU6O,IA5HE,cA8H1B7J,KAAK4C,SAASlI,MAAM4S,GAAa,EAE7BtN,KAAKoM,cAActT,QACrBkH,KAAKoM,cAAcvS,QAAQxE,IACzBA,EAAQ2F,UAAU4C,OAjIG,aAkIrBvI,EAAQkP,aAAa,iBAAiB,KAI1CvE,KAAKwN,kBAAiB,GAEtB,MAYMC,EAAc,UADSH,EAAU,GAAG9S,cAAgB8S,EAAU/L,MAAM,IAG1EvB,KAAKkD,eAdY,KACflD,KAAK4C,SAAS5H,UAAU4C,OA1IA,cA2IxBoC,KAAK4C,SAAS5H,UAAU6O,IA5IF,WADJ,QA+IlB7J,KAAK4C,SAASlI,MAAM4S,GAAa,GAEjCtN,KAAKwN,kBAAiB,GAEtBlN,EAAamB,QAAQzB,KAAK4C,SAxJX,sBA8Ja5C,KAAK4C,UAAU,GAC7C5C,KAAK4C,SAASlI,MAAM4S,GAAgBtN,KAAK4C,SAAS6K,GAAhB,KAGpCX,OACE,GAAI9M,KAAKmM,mBAAqBnM,KAAK4C,SAAS5H,UAAUC,SA9JlC,QA+JlB,OAIF,GADmBqF,EAAamB,QAAQzB,KAAK4C,SAtK7B,oBAuKDb,iBACb,OAGF,MAAMuL,EAAYtN,KAAKuN,gBAEvBvN,KAAK4C,SAASlI,MAAM4S,GAAgBtN,KAAK4C,SAAS6C,wBAAwB6H,GAAxC,KAElC5R,EAAOsE,KAAK4C,UAEZ5C,KAAK4C,SAAS5H,UAAU6O,IA3KE,cA4K1B7J,KAAK4C,SAAS5H,UAAU4C,OA7KA,WADJ,QAgLpB,MAAM8P,EAAqB1N,KAAKoM,cAActT,OAC9C,GAAI4U,EAAqB,EACvB,IAAK,IAAIzO,EAAI,EAAGA,EAAIyO,EAAoBzO,IAAK,CAC3C,MAAMwC,EAAUzB,KAAKoM,cAAcnN,GAC7BsN,EAAO1U,EAAuB4J,GAEhC8K,IAASA,EAAKvR,UAAUC,SAtLZ,UAuLdwG,EAAQzG,UAAU6O,IApLC,aAqLnBpI,EAAQ8C,aAAa,iBAAiB,IAK5CvE,KAAKwN,kBAAiB,GAStBxN,KAAK4C,SAASlI,MAAM4S,GAAa,GAEjCtN,KAAKkD,eATY,KACflD,KAAKwN,kBAAiB,GACtBxN,KAAK4C,SAAS5H,UAAU4C,OA/LA,cAgMxBoC,KAAK4C,SAAS5H,UAAU6O,IAjMF,YAkMtBvJ,EAAamB,QAAQzB,KAAK4C,SAtMV,uBA2MY5C,KAAK4C,UAAU,GAG/C4K,iBAAiBG,GACf3N,KAAKmM,iBAAmBwB,EAK1BnG,WAAW/N,GAOT,OANAA,EAAS,IACJwM,KACAxM,IAEE6K,OAASzD,QAAQpH,EAAO6K,QAC/B/K,EA5OS,WA4OaE,EAAQ+M,GACvB/M,EAGT8T,gBACE,OAAOvN,KAAK4C,SAAS5H,UAAUC,SAvNrB,SAAA,QACC,SAyNb2R,aACE,IAAIX,OAAEA,GAAWjM,KAAKuH,QAEtB0E,EAASpT,EAAWoT,GAEpB,MAAM7W,EAAY,+CAA0C6W,MAY5D,OAVA/W,EAAeC,KAAKC,EAAU6W,GAC3BpS,QAAQxE,IACP,MAAMuY,EAAW/V,EAAuBxC,GAExC2K,KAAK6M,0BACHe,EACA,CAACvY,MAIA4W,EAGTY,0BAA0BxX,EAASwY,GACjC,IAAKxY,IAAYwY,EAAa/U,OAC5B,OAGF,MAAMgV,EAASzY,EAAQ2F,UAAUC,SAxPb,QA0PpB4S,EAAahU,QAAQ0S,IACfuB,EACFvB,EAAKvR,UAAU4C,OAzPM,aA2PrB2O,EAAKvR,UAAU6O,IA3PM,aA8PvB0C,EAAKhI,aAAa,gBAAiBuJ,KAMf1K,yBAAC/N,EAASoE,GAChC,IAAIyK,EAAOlH,EAAKM,IAAIjI,EA5RP,eA6Rb,MAAMkS,EAAU,IACXtB,KACApB,EAAYI,kBAAkB5P,MACX,iBAAXoE,GAAuBA,EAASA,EAAS,IAWtD,IARKyK,GAAQqD,EAAQjD,QAA4B,iBAAX7K,GAAuB,YAAYa,KAAKb,KAC5E8N,EAAQjD,QAAS,GAGdJ,IACHA,EAAO,IAAIgI,EAAS7W,EAASkS,IAGT,iBAAX9N,EAAqB,CAC9B,QAA4B,IAAjByK,EAAKzK,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CyK,EAAKzK,MAIa2J,uBAAC3J,GACrB,OAAOuG,KAAKiE,MAAK,WACfiI,EAASmB,kBAAkBrN,KAAMvG,OAWvC6G,EAAaQ,GAAGxL,SA/Sc,6BAWD,+BAoSyC,SAAU6J,IAEjD,MAAzBA,EAAMkB,OAAOyJ,SAAoB3K,EAAMY,gBAAmD,MAAjCZ,EAAMY,eAAe+J,UAChF3K,EAAMsD,iBAGR,MAAMsL,EAAclJ,EAAYI,kBAAkBjF,MAC5C5K,EAAWwC,EAAuBoI,MACf9K,EAAeC,KAAKC,GAE5ByE,QAAQxE,IACvB,MAAM6O,EAAOlH,EAAKM,IAAIjI,EA5UT,eA6Ub,IAAIoE,EACAyK,GAEmB,OAAjBA,EAAKyI,SAAkD,iBAAvBoB,EAAY9B,SAC9C/H,EAAKqD,QAAQ0E,OAAS8B,EAAY9B,OAClC/H,EAAKyI,QAAUzI,EAAK0I,cAGtBnT,EAAS,UAETA,EAASsU,EAGX7B,EAASmB,kBAAkBhY,EAASoE,QAWxCwC,EAAmBiQ,GCjYZ,IAAIxG,GAAM,MACNsI,GAAS,SACTC,GAAQ,QACRrI,GAAO,OAEPsI,GAAiB,CAACxI,GAAKsI,GAAQC,GAAOrI,IAOtCuI,GAAmCD,GAAeE,QAAO,SAAUC,EAAKC,GACjF,OAAOD,EAAI7Y,OAAO,CAAC8Y,EAAAA,SAAyBA,EAAAA,WAC3C,IACQC,GAA0B,GAAG/Y,OAAO0Y,GAAgB,CAX7C,SAWqDE,QAAO,SAAUC,EAAKC,GAC3F,OAAOD,EAAI7Y,OAAO,CAAC8Y,EAAWA,EAAAA,SAAyBA,EAAAA,WACtD,IAaQE,GAAiB,CAXJ,aACN,OACK,YAEC,aACN,OACK,YAEE,cACN,QACK,cC7BT,SAASC,GAAYpZ,GAClC,OAAOA,GAAWA,EAAQqZ,UAAY,IAAItU,cAAgB,KCD7C,SAASuU,GAAUC,GAChC,GAAY,MAARA,EACF,OAAO3W,OAGT,GAAwB,oBAApB2W,EAAK1U,WAAkC,CACzC,IAAI2U,EAAgBD,EAAKC,cACzB,OAAOA,GAAgBA,EAAcC,aAAwB7W,OAG/D,OAAO2W,ECRT,SAASlW,GAAUkW,GAEjB,OAAOA,aADUD,GAAUC,GAAMnZ,SACImZ,aAAgBnZ,QAGvD,SAASsZ,GAAcH,GAErB,OAAOA,aADUD,GAAUC,GAAMI,aACIJ,aAAgBI,YAGvD,SAASC,GAAaL,GAEpB,MAA0B,oBAAfpT,aAKJoT,aADUD,GAAUC,GAAMpT,YACIoT,aAAgBpT,YCyDvD,IAAA0T,GAAe,CACb7S,KAAM,cACN8S,SAAS,EACTC,MAAO,QACP5S,GA5EF,SAAqB6S,GACnB,IAAIC,EAAQD,EAAKC,MACjB3V,OAAOC,KAAK0V,EAAMC,UAAU1V,SAAQ,SAAUwC,GAC5C,IAAI3B,EAAQ4U,EAAME,OAAOnT,IAAS,GAC9B6I,EAAaoK,EAAMpK,WAAW7I,IAAS,GACvChH,EAAUia,EAAMC,SAASlT,GAExB0S,GAAc1Z,IAAaoZ,GAAYpZ,KAO5CsE,OAAO8V,OAAOpa,EAAQqF,MAAOA,GAC7Bf,OAAOC,KAAKsL,GAAYrL,SAAQ,SAAUwC,GACxC,IAAIrC,EAAQkL,EAAW7I,IAET,IAAVrC,EACF3E,EAAQ2P,gBAAgB3I,GAExBhH,EAAQkP,aAAalI,GAAgB,IAAVrC,EAAiB,GAAKA,WAwDvD0V,OAlDF,SAAgBC,GACd,IAAIL,EAAQK,EAAML,MACdM,EAAgB,CAClBC,OAAQ,CACN/J,SAAUwJ,EAAMQ,QAAQC,SACxBnK,KAAM,IACNF,IAAK,IACLsK,OAAQ,KAEVC,MAAO,CACLnK,SAAU,YAEZoK,UAAW,IASb,OAPAvW,OAAO8V,OAAOH,EAAMC,SAASM,OAAOnV,MAAOkV,EAAcC,QACzDP,EAAME,OAASI,EAEXN,EAAMC,SAASU,OACjBtW,OAAO8V,OAAOH,EAAMC,SAASU,MAAMvV,MAAOkV,EAAcK,OAGnD,WACLtW,OAAOC,KAAK0V,EAAMC,UAAU1V,SAAQ,SAAUwC,GAC5C,IAAIhH,EAAUia,EAAMC,SAASlT,GACzB6I,EAAaoK,EAAMpK,WAAW7I,IAAS,GAGvC3B,EAFkBf,OAAOC,KAAK0V,EAAME,OAAOW,eAAe9T,GAAQiT,EAAME,OAAOnT,GAAQuT,EAAcvT,IAE7E+R,QAAO,SAAU1T,EAAOZ,GAElD,OADAY,EAAMZ,GAAY,GACXY,IACN,IAEEqU,GAAc1Z,IAAaoZ,GAAYpZ,KAI5CsE,OAAO8V,OAAOpa,EAAQqF,MAAOA,GAC7Bf,OAAOC,KAAKsL,GAAYrL,SAAQ,SAAUuW,GACxC/a,EAAQ2P,gBAAgBoL,YAa9BC,SAAU,CAAC,kBCjFE,SAASC,GAAiBhC,GACvC,OAAOA,EAAU5W,MAAM,KAAK,GCFf,SAAS+N,GAAsBpQ,GAC5C,IAAImQ,EAAOnQ,EAAQoQ,wBACnB,MAAO,CACL8K,MAAO/K,EAAK+K,MACZC,OAAQhL,EAAKgL,OACb9K,IAAKF,EAAKE,IACVuI,MAAOzI,EAAKyI,MACZD,OAAQxI,EAAKwI,OACbpI,KAAMJ,EAAKI,KACX6K,EAAGjL,EAAKI,KACR8K,EAAGlL,EAAKE,KCPG,SAASiL,GAActb,GACpC,IAAIub,EAAanL,GAAsBpQ,GAGnCkb,EAAQlb,EAAQwb,YAChBL,EAASnb,EAAQsG,aAUrB,OARI1E,KAAK+R,IAAI4H,EAAWL,MAAQA,IAAU,IACxCA,EAAQK,EAAWL,OAGjBtZ,KAAK+R,IAAI4H,EAAWJ,OAASA,IAAW,IAC1CA,EAASI,EAAWJ,QAGf,CACLC,EAAGpb,EAAQ2Q,WACX0K,EAAGrb,EAAQ0Q,UACXwK,MAAOA,EACPC,OAAQA,GCrBG,SAASvV,GAASgR,EAAQhW,GACvC,IAAI6a,EAAW7a,EAAMqF,aAAerF,EAAMqF,cAE1C,GAAI2Q,EAAOhR,SAAShF,GAClB,OAAO,EAEJ,GAAI6a,GAAY7B,GAAa6B,GAAW,CACzC,IAAIja,EAAOZ,EAEX,EAAG,CACD,GAAIY,GAAQoV,EAAO8E,WAAWla,GAC5B,OAAO,EAITA,EAAOA,EAAKR,YAAcQ,EAAKma,WACxBna,GAIb,OAAO,ECpBM,SAASqB,GAAiB7C,GACvC,OAAOsZ,GAAUtZ,GAAS6C,iBAAiB7C,GCD9B,SAAS4b,GAAe5b,GACrC,MAAO,CAAC,QAAS,KAAM,MAAM0U,QAAQ0E,GAAYpZ,KAAa,ECDjD,SAAS6b,GAAmB7b,GAEzC,QAASqD,GAAUrD,GAAWA,EAAQwZ,cACtCxZ,EAAQC,WAAa2C,OAAO3C,UAAUC,gBCDzB,SAAS4b,GAAc9b,GACpC,MAA6B,SAAzBoZ,GAAYpZ,GACPA,EAMPA,EAAQ+b,cACR/b,EAAQgB,aACR4Y,GAAa5Z,GAAWA,EAAQ2b,KAAO,OAEvCE,GAAmB7b,GCRvB,SAASgc,GAAoBhc,GAC3B,OAAK0Z,GAAc1Z,IACoB,UAAvC6C,GAAiB7C,GAASyQ,SAInBzQ,EAAQic,aAHN,KAwCI,SAASC,GAAgBlc,GAItC,IAHA,IAAI4C,EAAS0W,GAAUtZ,GACnBic,EAAeD,GAAoBhc,GAEhCic,GAAgBL,GAAeK,IAA6D,WAA5CpZ,GAAiBoZ,GAAcxL,UACpFwL,EAAeD,GAAoBC,GAGrC,OAAIA,IAA+C,SAA9B7C,GAAY6C,IAA0D,SAA9B7C,GAAY6C,IAAwE,WAA5CpZ,GAAiBoZ,GAAcxL,UAC3H7N,EAGFqZ,GA5CT,SAA4Bjc,GAC1B,IAAImc,GAAsE,IAA1D7J,UAAU8J,UAAUrX,cAAc2P,QAAQ,WAG1D,IAFuD,IAA5CpC,UAAU8J,UAAU1H,QAAQ,YAE3BgF,GAAc1Z,IAII,UAFX6C,GAAiB7C,GAEnByQ,SACb,OAAO,KAMX,IAFA,IAAI4L,EAAcP,GAAc9b,GAEzB0Z,GAAc2C,IAAgB,CAAC,OAAQ,QAAQ3H,QAAQ0E,GAAYiD,IAAgB,GAAG,CAC3F,IAAIC,EAAMzZ,GAAiBwZ,GAI3B,GAAsB,SAAlBC,EAAIC,WAA4C,SAApBD,EAAIE,aAA0C,UAAhBF,EAAIG,UAAiF,IAA1D,CAAC,YAAa,eAAe/H,QAAQ4H,EAAII,aAAsBP,GAAgC,WAAnBG,EAAII,YAA2BP,GAAaG,EAAI3b,QAAyB,SAAf2b,EAAI3b,OACjO,OAAO0b,EAEPA,EAAcA,EAAYrb,WAI9B,OAAO,KAiBgB2b,CAAmB3c,IAAY4C,EC9DzC,SAASga,GAAyB3D,GAC/C,MAAO,CAAC,MAAO,UAAUvE,QAAQuE,IAAc,EAAI,IAAM,ICDpD,IAAI4D,GAAMjb,KAAKib,IACXC,GAAMlb,KAAKkb,IACXC,GAAQnb,KAAKmb,MCDT,SAASC,GAAOF,EAAKnY,EAAOkY,GACzC,OAAOI,GAAQH,EAAKI,GAAQvY,EAAOkY,ICDtB,SAASM,GAAmBC,GACzC,OAAO9Y,OAAO8V,OAAO,GCDd,CACL/J,IAAK,EACLuI,MAAO,EACPD,OAAQ,EACRpI,KAAM,GDHuC6M,GEFlC,SAASC,GAAgB1Y,EAAOJ,GAC7C,OAAOA,EAAKwU,QAAO,SAAUuE,EAASzV,GAEpC,OADAyV,EAAQzV,GAAOlD,EACR2Y,IACN,ICwFL,IAAAC,GAAe,CACbvW,KAAM,QACN8S,SAAS,EACTC,MAAO,OACP5S,GA9EF,SAAe6S,GACb,IAAIwD,EAEAvD,EAAQD,EAAKC,MACbjT,EAAOgT,EAAKhT,KACZyT,EAAUT,EAAKS,QACfgD,EAAexD,EAAMC,SAASU,MAC9B8C,EAAgBzD,EAAM0D,cAAcD,cACpCE,EAAgB3C,GAAiBhB,EAAMhB,WACvC4E,EAAOjB,GAAyBgB,GAEhC/T,EADa,CAAC0G,GAAMqI,IAAOlE,QAAQkJ,IAAkB,EAClC,SAAW,QAElC,GAAKH,GAAiBC,EAAtB,CAIA,IAAIN,EAxBgB,SAAyBU,EAAS7D,GAItD,OAAOkD,GAAsC,iBAH7CW,EAA6B,mBAAZA,EAAyBA,EAAQxZ,OAAO8V,OAAO,GAAIH,EAAM8D,MAAO,CAC/E9E,UAAWgB,EAAMhB,aACb6E,GACkDA,EAAUT,GAAgBS,EAASjF,KAoBvEmF,CAAgBvD,EAAQqD,QAAS7D,GACjDgE,EAAY3C,GAAcmC,GAC1BS,EAAmB,MAATL,EAAexN,GAAME,GAC/B4N,EAAmB,MAATN,EAAelF,GAASC,GAClCwF,EAAUnE,EAAM8D,MAAMlD,UAAUhR,GAAOoQ,EAAM8D,MAAMlD,UAAUgD,GAAQH,EAAcG,GAAQ5D,EAAM8D,MAAMvD,OAAO3Q,GAC9GwU,EAAYX,EAAcG,GAAQ5D,EAAM8D,MAAMlD,UAAUgD,GACxDS,EAAoBpC,GAAgBuB,GACpCc,EAAaD,EAA6B,MAATT,EAAeS,EAAkBE,cAAgB,EAAIF,EAAkBG,aAAe,EAAI,EAC3HC,EAAoBN,EAAU,EAAIC,EAAY,EAG9CvB,EAAMM,EAAcc,GACpBrB,EAAM0B,EAAaN,EAAUpU,GAAOuT,EAAce,GAClDQ,EAASJ,EAAa,EAAIN,EAAUpU,GAAO,EAAI6U,EAC/CxO,EAAS8M,GAAOF,EAAK6B,EAAQ9B,GAE7B+B,EAAWf,EACf5D,EAAM0D,cAAc3W,KAASwW,EAAwB,IAA0BoB,GAAY1O,EAAQsN,EAAsBqB,aAAe3O,EAASyO,EAAQnB,KA6CzJnD,OA1CF,SAAgBC,GACd,IAAIL,EAAQK,EAAML,MAEd6E,EADUxE,EAAMG,QACWza,QAC3Byd,OAAoC,IAArBqB,EAA8B,sBAAwBA,EAErD,MAAhBrB,IAKwB,iBAAjBA,IACTA,EAAexD,EAAMC,SAASM,OAAO/Z,cAAcgd,MAahD7X,GAASqU,EAAMC,SAASM,OAAQiD,KAQrCxD,EAAMC,SAASU,MAAQ6C,IAUvBzC,SAAU,CAAC,iBACX+D,iBAAkB,CAAC,oBC3FjBC,GAAa,CACf3O,IAAK,OACLuI,MAAO,OACPD,OAAQ,OACRpI,KAAM,QAgBD,SAAS0O,GAAY3E,GAC1B,IAAI4E,EAEA1E,EAASF,EAAME,OACf2E,EAAa7E,EAAM6E,WACnBlG,EAAYqB,EAAMrB,UAClBmG,EAAU9E,EAAM8E,QAChB3O,EAAW6J,EAAM7J,SACjB4O,EAAkB/E,EAAM+E,gBACxBC,EAAWhF,EAAMgF,SACjBC,EAAejF,EAAMiF,aAErBC,GAAyB,IAAjBD,EAvBd,SAA2BvF,GACzB,IAAIoB,EAAIpB,EAAKoB,EACTC,EAAIrB,EAAKqB,EAEToE,EADM7c,OACI8c,kBAAoB,EAClC,MAAO,CACLtE,EAAG2B,GAAMA,GAAM3B,EAAIqE,GAAOA,IAAQ,EAClCpE,EAAG0B,GAAMA,GAAM1B,EAAIoE,GAAOA,IAAQ,GAgBAE,CAAkBP,GAAmC,mBAAjBG,EAA8BA,EAAaH,GAAWA,EAC1HQ,EAAUJ,EAAMpE,EAChBA,OAAgB,IAAZwE,EAAqB,EAAIA,EAC7BC,EAAUL,EAAMnE,EAChBA,OAAgB,IAAZwE,EAAqB,EAAIA,EAE7BC,EAAOV,EAAQtE,eAAe,KAC9BiF,EAAOX,EAAQtE,eAAe,KAC9BkF,EAAQzP,GACR0P,EAAQ5P,GACR6P,EAAMtd,OAEV,GAAI0c,EAAU,CACZ,IAAIrD,EAAeC,GAAgB1B,GAC/B2F,EAAa,eACbC,EAAY,cAEZnE,IAAiB3C,GAAUkB,IAGmB,WAA5C3X,GAFJoZ,EAAeJ,GAAmBrB,IAEC/J,WACjC0P,EAAa,eACbC,EAAY,eAKhBnE,EAAeA,EAEXhD,IAAc5I,KAChB4P,EAAQtH,GAER0C,GAAKY,EAAakE,GAAchB,EAAWhE,OAC3CE,GAAKgE,EAAkB,GAAK,GAG1BpG,IAAc1I,KAChByP,EAAQpH,GAERwC,GAAKa,EAAamE,GAAajB,EAAWjE,MAC1CE,GAAKiE,EAAkB,GAAK,GAIhC,IAKMgB,EALFC,EAAehc,OAAO8V,OAAO,CAC/B3J,SAAUA,GACT6O,GAAYN,IAEf,OAAIK,EAGK/a,OAAO8V,OAAO,GAAIkG,IAAeD,EAAiB,IAAmBJ,GAASF,EAAO,IAAM,GAAIM,EAAeL,GAASF,EAAO,IAAM,GAAIO,EAAe9D,WAAa2D,EAAIR,kBAAoB,GAAK,EAAI,aAAetE,EAAI,OAASC,EAAI,MAAQ,eAAiBD,EAAI,OAASC,EAAI,SAAUgF,IAG3R/b,OAAO8V,OAAO,GAAIkG,IAAepB,EAAkB,IAAoBe,GAASF,EAAO1E,EAAI,KAAO,GAAI6D,EAAgBc,GAASF,EAAO1E,EAAI,KAAO,GAAI8D,EAAgB3C,UAAY,GAAI2C,IAsD9L,IAAAqB,GAAe,CACbvZ,KAAM,gBACN8S,SAAS,EACTC,MAAO,cACP5S,GAvDF,SAAuBqZ,GACrB,IAAIvG,EAAQuG,EAAMvG,MACdQ,EAAU+F,EAAM/F,QAChBgG,EAAwBhG,EAAQ4E,gBAChCA,OAA4C,IAA1BoB,GAA0CA,EAC5DC,EAAoBjG,EAAQ6E,SAC5BA,OAAiC,IAAtBoB,GAAsCA,EACjDC,EAAwBlG,EAAQ8E,aAChCA,OAAyC,IAA1BoB,GAA0CA,EAYzDL,EAAe,CACjBrH,UAAWgC,GAAiBhB,EAAMhB,WAClCuB,OAAQP,EAAMC,SAASM,OACvB2E,WAAYlF,EAAM8D,MAAMvD,OACxB6E,gBAAiBA,GAGsB,MAArCpF,EAAM0D,cAAcD,gBACtBzD,EAAME,OAAOK,OAASlW,OAAO8V,OAAO,GAAIH,EAAME,OAAOK,OAAQyE,GAAY3a,OAAO8V,OAAO,GAAIkG,EAAc,CACvGlB,QAASnF,EAAM0D,cAAcD,cAC7BjN,SAAUwJ,EAAMQ,QAAQC,SACxB4E,SAAUA,EACVC,aAAcA,OAIe,MAA7BtF,EAAM0D,cAAc/C,QACtBX,EAAME,OAAOS,MAAQtW,OAAO8V,OAAO,GAAIH,EAAME,OAAOS,MAAOqE,GAAY3a,OAAO8V,OAAO,GAAIkG,EAAc,CACrGlB,QAASnF,EAAM0D,cAAc/C,MAC7BnK,SAAU,WACV6O,UAAU,EACVC,aAAcA,OAIlBtF,EAAMpK,WAAW2K,OAASlW,OAAO8V,OAAO,GAAIH,EAAMpK,WAAW2K,OAAQ,CACnEoG,wBAAyB3G,EAAMhB,aAUjCpK,KAAM,ICvJJgS,GAAU,CACZA,SAAS,GAsCXC,GAAe,CACb9Z,KAAM,iBACN8S,SAAS,EACTC,MAAO,QACP5S,GAAI,aACJkT,OAxCF,SAAgBL,GACd,IAAIC,EAAQD,EAAKC,MACbnS,EAAWkS,EAAKlS,SAChB2S,EAAUT,EAAKS,QACfsG,EAAkBtG,EAAQuG,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAkBxG,EAAQyG,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7Cre,EAAS0W,GAAUW,EAAMC,SAASM,QAClC2G,EAAgB,GAAGhhB,OAAO8Z,EAAMkH,cAActG,UAAWZ,EAAMkH,cAAc3G,QAYjF,OAVIwG,GACFG,EAAc3c,SAAQ,SAAU4c,GAC9BA,EAAatd,iBAAiB,SAAUgE,EAASuZ,OAAQR,OAIzDK,GACFte,EAAOkB,iBAAiB,SAAUgE,EAASuZ,OAAQR,IAG9C,WACDG,GACFG,EAAc3c,SAAQ,SAAU4c,GAC9BA,EAAapd,oBAAoB,SAAU8D,EAASuZ,OAAQR,OAI5DK,GACFte,EAAOoB,oBAAoB,SAAU8D,EAASuZ,OAAQR,MAY1DhS,KAAM,IC/CJyS,GAAO,CACT/Q,KAAM,QACNqI,MAAO,OACPD,OAAQ,MACRtI,IAAK,UAEQ,SAASkR,GAAqBtI,GAC3C,OAAOA,EAAUnO,QAAQ,0BAA0B,SAAU0W,GAC3D,OAAOF,GAAKE,MCRhB,IAAIF,GAAO,CACTvN,MAAO,MACPK,IAAK,SAEQ,SAASqN,GAA8BxI,GACpD,OAAOA,EAAUnO,QAAQ,cAAc,SAAU0W,GAC/C,OAAOF,GAAKE,MCLD,SAASE,GAAgBnI,GACtC,IAAI2G,EAAM5G,GAAUC,GAGpB,MAAO,CACL/I,WAHe0P,EAAIyB,YAInBrR,UAHc4P,EAAI0B,aCDP,SAASC,GAAoB7hB,GAQ1C,OAAOoQ,GAAsByL,GAAmB7b,IAAUuQ,KAAOmR,GAAgB1hB,GAASwQ,WCV7E,SAASsR,GAAe9hB,GAErC,IAAI+hB,EAAoBlf,GAAiB7C,GACrCgiB,EAAWD,EAAkBC,SAC7BC,EAAYF,EAAkBE,UAC9BC,EAAYH,EAAkBG,UAElC,MAAO,6BAA6Bjd,KAAK+c,EAAWE,EAAYD,GCGnD,SAASE,GAAkBniB,EAASoiB,GACjD,IAAIC,OAES,IAATD,IACFA,EAAO,IAGT,IAAIhB,ECdS,SAASkB,EAAgB/I,GACtC,MAAI,CAAC,OAAQ,OAAQ,aAAa7E,QAAQ0E,GAAYG,KAAU,EAEvDA,EAAKC,cAAc/S,KAGxBiT,GAAcH,IAASuI,GAAevI,GACjCA,EAGF+I,EAAgBxG,GAAcvC,IDIlB+I,CAAgBtiB,GAC/BuiB,EAASnB,KAAqE,OAAlDiB,EAAwBriB,EAAQwZ,oBAAyB,EAAS6I,EAAsB5b,MACpHyZ,EAAM5G,GAAU8H,GAChBpW,EAASuX,EAAS,CAACrC,GAAK/f,OAAO+f,EAAIsC,gBAAkB,GAAIV,GAAeV,GAAgBA,EAAe,IAAMA,EAC7GqB,EAAcL,EAAKjiB,OAAO6K,GAC9B,OAAOuX,EAASE,EAChBA,EAAYtiB,OAAOgiB,GAAkBrG,GAAc9Q,KExBtC,SAAS0X,GAAiBvS,GACvC,OAAO7L,OAAO8V,OAAO,GAAIjK,EAAM,CAC7BI,KAAMJ,EAAKiL,EACX/K,IAAKF,EAAKkL,EACVzC,MAAOzI,EAAKiL,EAAIjL,EAAK+K,MACrBvC,OAAQxI,EAAKkL,EAAIlL,EAAKgL,SCuB1B,SAASwH,GAA2B3iB,EAAS4iB,GAC3C,M/BpBoB,a+BoBbA,EAA8BF,GC1BxB,SAAyB1iB,GACtC,IAAIkgB,EAAM5G,GAAUtZ,GAChB6iB,EAAOhH,GAAmB7b,GAC1BwiB,EAAiBtC,EAAIsC,eACrBtH,EAAQ2H,EAAKpE,YACbtD,EAAS0H,EAAKrE,aACdpD,EAAI,EACJC,EAAI,EAuBR,OAjBImH,IACFtH,EAAQsH,EAAetH,MACvBC,EAASqH,EAAerH,OASnB,iCAAiClW,KAAKqN,UAAU8J,aACnDhB,EAAIoH,EAAe7R,WACnB0K,EAAImH,EAAe9R,YAIhB,CACLwK,MAAOA,EACPC,OAAQA,EACRC,EAAGA,EAAIyG,GAAoB7hB,GAC3Bqb,EAAGA,GDRiDyH,CAAgB9iB,IAAY0Z,GAAckJ,GAdlG,SAAoC5iB,GAClC,IAAImQ,EAAOC,GAAsBpQ,GASjC,OARAmQ,EAAKE,IAAMF,EAAKE,IAAMrQ,EAAQ+iB,UAC9B5S,EAAKI,KAAOJ,EAAKI,KAAOvQ,EAAQgjB,WAChC7S,EAAKwI,OAASxI,EAAKE,IAAMrQ,EAAQwe,aACjCrO,EAAKyI,MAAQzI,EAAKI,KAAOvQ,EAAQye,YACjCtO,EAAK+K,MAAQlb,EAAQye,YACrBtO,EAAKgL,OAASnb,EAAQwe,aACtBrO,EAAKiL,EAAIjL,EAAKI,KACdJ,EAAKkL,EAAIlL,EAAKE,IACPF,EAI2G8S,CAA2BL,GAAkBF,GEtBlJ,SAAyB1iB,GACtC,IAAIqiB,EAEAQ,EAAOhH,GAAmB7b,GAC1BkjB,EAAYxB,GAAgB1hB,GAC5ByG,EAA0D,OAAlD4b,EAAwBriB,EAAQwZ,oBAAyB,EAAS6I,EAAsB5b,KAChGyU,EAAQ2B,GAAIgG,EAAKM,YAAaN,EAAKpE,YAAahY,EAAOA,EAAK0c,YAAc,EAAG1c,EAAOA,EAAKgY,YAAc,GACvGtD,EAAS0B,GAAIgG,EAAKO,aAAcP,EAAKrE,aAAc/X,EAAOA,EAAK2c,aAAe,EAAG3c,EAAOA,EAAK+X,aAAe,GAC5GpD,GAAK8H,EAAU1S,WAAaqR,GAAoB7hB,GAChDqb,GAAK6H,EAAU5S,UAMnB,MAJiD,QAA7CzN,GAAiB4D,GAAQoc,GAAMjP,YACjCwH,GAAKyB,GAAIgG,EAAKpE,YAAahY,EAAOA,EAAKgY,YAAc,GAAKvD,GAGrD,CACLA,MAAOA,EACPC,OAAQA,EACRC,EAAGA,EACHC,EAAGA,GFG2KgI,CAAgBxH,GAAmB7b,KG7BtM,SAASsjB,GAAarK,GACnC,OAAOA,EAAU5W,MAAM,KAAK,GCGf,SAASkhB,GAAevJ,GACrC,IAOIoF,EAPAvE,EAAYb,EAAKa,UACjB7a,EAAUga,EAAKha,QACfiZ,EAAYe,EAAKf,UACjB2E,EAAgB3E,EAAYgC,GAAiBhC,GAAa,KAC1DuK,EAAYvK,EAAYqK,GAAarK,GAAa,KAClDwK,EAAU5I,EAAUO,EAAIP,EAAUK,MAAQ,EAAIlb,EAAQkb,MAAQ,EAC9DwI,EAAU7I,EAAUQ,EAAIR,EAAUM,OAAS,EAAInb,EAAQmb,OAAS,EAGpE,OAAQyC,GACN,KAAKvN,GACH+O,EAAU,CACRhE,EAAGqI,EACHpI,EAAGR,EAAUQ,EAAIrb,EAAQmb,QAE3B,MAEF,KAAKxC,GACHyG,EAAU,CACRhE,EAAGqI,EACHpI,EAAGR,EAAUQ,EAAIR,EAAUM,QAE7B,MAEF,KAAKvC,GACHwG,EAAU,CACRhE,EAAGP,EAAUO,EAAIP,EAAUK,MAC3BG,EAAGqI,GAEL,MAEF,KAAKnT,GACH6O,EAAU,CACRhE,EAAGP,EAAUO,EAAIpb,EAAQkb,MACzBG,EAAGqI,GAEL,MAEF,QACEtE,EAAU,CACRhE,EAAGP,EAAUO,EACbC,EAAGR,EAAUQ,GAInB,IAAIsI,EAAW/F,EAAgBhB,GAAyBgB,GAAiB,KAEzE,GAAgB,MAAZ+F,EAAkB,CACpB,IAAI9Z,EAAmB,MAAb8Z,EAAmB,SAAW,QAExC,OAAQH,GACN,InClDa,QmCmDXpE,EAAQuE,GAAYvE,EAAQuE,IAAa9I,EAAUhR,GAAO,EAAI7J,EAAQ6J,GAAO,GAC7E,MAEF,InCrDW,MmCsDTuV,EAAQuE,GAAYvE,EAAQuE,IAAa9I,EAAUhR,GAAO,EAAI7J,EAAQ6J,GAAO,IAOnF,OAAOuV,EC1DM,SAASwE,GAAe3J,EAAOQ,QAC5B,IAAZA,IACFA,EAAU,IAGZ,IAAIoJ,EAAWpJ,EACXqJ,EAAqBD,EAAS5K,UAC9BA,OAAmC,IAAvB6K,EAAgC7J,EAAMhB,UAAY6K,EAC9DC,EAAoBF,EAASG,SAC7BA,OAAiC,IAAtBD,EpCXY,kBoCWqCA,EAC5DE,EAAwBJ,EAASK,aACjCA,OAAyC,IAA1BD,EpCZC,WoCY6CA,EAC7DE,EAAwBN,EAASO,eACjCA,OAA2C,IAA1BD,EpCbH,SoCa+CA,EAC7DE,EAAuBR,EAASS,YAChCA,OAAuC,IAAzBD,GAA0CA,EACxDE,EAAmBV,EAAS/F,QAC5BA,OAA+B,IAArByG,EAA8B,EAAIA,EAC5CnH,EAAgBD,GAAsC,iBAAZW,EAAuBA,EAAUT,GAAgBS,EAASjF,KACpG2L,EpCnBc,WoCmBDJ,EpClBI,YADH,SoCoBdK,EAAmBxK,EAAMC,SAASW,UAClCsE,EAAalF,EAAM8D,MAAMvD,OACzBxa,EAAUia,EAAMC,SAASoK,EAAcE,EAAaJ,GACpDM,ELmBS,SAAyB1kB,EAASgkB,EAAUE,GACzD,IAAIS,EAAmC,oBAAbX,EAlB5B,SAA4BhkB,GAC1B,IAAI4kB,EAAkBzC,GAAkBrG,GAAc9b,IAElD6kB,EADoB,CAAC,WAAY,SAASnQ,QAAQ7R,GAAiB7C,GAASyQ,WAAa,GACnDiJ,GAAc1Z,GAAWkc,GAAgBlc,GAAWA,EAE9F,OAAKqD,GAAUwhB,GAKRD,EAAgBjkB,QAAO,SAAUiiB,GACtC,OAAOvf,GAAUuf,IAAmBhd,GAASgd,EAAgBiC,IAAmD,SAAhCzL,GAAYwJ,MALrF,GAYkDkC,CAAmB9kB,GAAW,GAAGG,OAAO6jB,GAC/FY,EAAkB,GAAGzkB,OAAOwkB,EAAqB,CAACT,IAClDa,EAAsBH,EAAgB,GACtCI,EAAeJ,EAAgB7L,QAAO,SAAUkM,EAASrC,GAC3D,IAAIzS,EAAOwS,GAA2B3iB,EAAS4iB,GAK/C,OAJAqC,EAAQ5U,IAAMwM,GAAI1M,EAAKE,IAAK4U,EAAQ5U,KACpC4U,EAAQrM,MAAQkE,GAAI3M,EAAKyI,MAAOqM,EAAQrM,OACxCqM,EAAQtM,OAASmE,GAAI3M,EAAKwI,OAAQsM,EAAQtM,QAC1CsM,EAAQ1U,KAAOsM,GAAI1M,EAAKI,KAAM0U,EAAQ1U,MAC/B0U,IACNtC,GAA2B3iB,EAAS+kB,IAKvC,OAJAC,EAAa9J,MAAQ8J,EAAapM,MAAQoM,EAAazU,KACvDyU,EAAa7J,OAAS6J,EAAarM,OAASqM,EAAa3U,IACzD2U,EAAa5J,EAAI4J,EAAazU,KAC9ByU,EAAa3J,EAAI2J,EAAa3U,IACvB2U,EKnCkBE,CAAgB7hB,GAAUrD,GAAWA,EAAUA,EAAQmlB,gBAAkBtJ,GAAmB5B,EAAMC,SAASM,QAASwJ,EAAUE,GACnJkB,EAAsBhV,GAAsBqU,GAC5C/G,EAAgB6F,GAAe,CACjC1I,UAAWuK,EACXplB,QAASmf,EACTzE,SAAU,WACVzB,UAAWA,IAEToM,EAAmB3C,GAAiBpe,OAAO8V,OAAO,GAAI+E,EAAYzB,IAClE4H,EpChCc,WoCgCMlB,EAA4BiB,EAAmBD,EAGnEG,EAAkB,CACpBlV,IAAKqU,EAAmBrU,IAAMiV,EAAkBjV,IAAM+M,EAAc/M,IACpEsI,OAAQ2M,EAAkB3M,OAAS+L,EAAmB/L,OAASyE,EAAczE,OAC7EpI,KAAMmU,EAAmBnU,KAAO+U,EAAkB/U,KAAO6M,EAAc7M,KACvEqI,MAAO0M,EAAkB1M,MAAQ8L,EAAmB9L,MAAQwE,EAAcxE,OAExE4M,EAAavL,EAAM0D,cAAczN,OAErC,GpC3CkB,WoC2CdkU,GAA6BoB,EAAY,CAC3C,IAAItV,EAASsV,EAAWvM,GACxB3U,OAAOC,KAAKghB,GAAiB/gB,SAAQ,SAAUqD,GAC7C,IAAI4d,EAAW,CAAC7M,GAAOD,IAAQjE,QAAQ7M,IAAQ,EAAI,GAAK,EACpDgW,EAAO,CAACxN,GAAKsI,IAAQjE,QAAQ7M,IAAQ,EAAI,IAAM,IACnD0d,EAAgB1d,IAAQqI,EAAO2N,GAAQ4H,KAI3C,OAAOF,EC1DM,SAASG,GAAqBzL,EAAOQ,QAClC,IAAZA,IACFA,EAAU,IAGZ,IAAIoJ,EAAWpJ,EACXxB,EAAY4K,EAAS5K,UACrB+K,EAAWH,EAASG,SACpBE,EAAeL,EAASK,aACxBpG,EAAU+F,EAAS/F,QACnB6H,EAAiB9B,EAAS8B,eAC1BC,EAAwB/B,EAASgC,sBACjCA,OAAkD,IAA1BD,EAAmCE,GAAgBF,EAC3EpC,EAAYF,GAAarK,GACzBC,EAAasK,EAAYmC,EAAiB7M,GAAsBA,GAAoBnY,QAAO,SAAUsY,GACvG,OAAOqK,GAAarK,KAAeuK,KAChC3K,GACDkN,EAAoB7M,EAAWvY,QAAO,SAAUsY,GAClD,OAAO4M,EAAsBnR,QAAQuE,IAAc,KAGpB,IAA7B8M,EAAkBtiB,SACpBsiB,EAAoB7M,GAQtB,IAAI8M,EAAYD,EAAkBhN,QAAO,SAAUC,EAAKC,GAOtD,OANAD,EAAIC,GAAa2K,GAAe3J,EAAO,CACrChB,UAAWA,EACX+K,SAAUA,EACVE,aAAcA,EACdpG,QAASA,IACR7C,GAAiBhC,IACbD,IACN,IACH,OAAO1U,OAAOC,KAAKyhB,GAAWC,MAAK,SAAUC,EAAGC,GAC9C,OAAOH,EAAUE,GAAKF,EAAUG,MC6FpC,IAAAC,GAAe,CACbpf,KAAM,OACN8S,SAAS,EACTC,MAAO,OACP5S,GA5HF,SAAc6S,GACZ,IAAIC,EAAQD,EAAKC,MACbQ,EAAUT,EAAKS,QACfzT,EAAOgT,EAAKhT,KAEhB,IAAIiT,EAAM0D,cAAc3W,GAAMqf,MAA9B,CAoCA,IAhCA,IAAIC,EAAoB7L,EAAQkJ,SAC5B4C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmB/L,EAAQgM,QAC3BC,OAAoC,IAArBF,GAAqCA,EACpDG,EAA8BlM,EAAQmM,mBACtC9I,EAAUrD,EAAQqD,QAClBkG,EAAWvJ,EAAQuJ,SACnBE,EAAezJ,EAAQyJ,aACvBI,EAAc7J,EAAQ6J,YACtBuC,EAAwBpM,EAAQkL,eAChCA,OAA2C,IAA1BkB,GAA0CA,EAC3DhB,EAAwBpL,EAAQoL,sBAChCiB,EAAqB7M,EAAMQ,QAAQxB,UACnC2E,EAAgB3C,GAAiB6L,GAEjCF,EAAqBD,IADH/I,IAAkBkJ,GACqCnB,EAjC/E,SAAuC1M,GACrC,GtCLgB,SsCKZgC,GAAiBhC,GACnB,MAAO,GAGT,IAAI8N,EAAoBxF,GAAqBtI,GAC7C,MAAO,CAACwI,GAA8BxI,GAAY8N,EAAmBtF,GAA8BsF,IA2BwCC,CAA8BF,GAA3E,CAACvF,GAAqBuF,KAChH5N,EAAa,CAAC4N,GAAoB3mB,OAAOymB,GAAoB7N,QAAO,SAAUC,EAAKC,GACrF,OAAOD,EAAI7Y,OtCvCG,SsCuCI8a,GAAiBhC,GAAsByM,GAAqBzL,EAAO,CACnFhB,UAAWA,EACX+K,SAAUA,EACVE,aAAcA,EACdpG,QAASA,EACT6H,eAAgBA,EAChBE,sBAAuBA,IACpB5M,KACJ,IACCgO,EAAgBhN,EAAM8D,MAAMlD,UAC5BsE,EAAalF,EAAM8D,MAAMvD,OACzB0M,EAAY,IAAIxf,IAChByf,GAAqB,EACrBC,EAAwBlO,EAAW,GAE9BtP,EAAI,EAAGA,EAAIsP,EAAWzV,OAAQmG,IAAK,CAC1C,IAAIqP,EAAYC,EAAWtP,GAEvByd,EAAiBpM,GAAiBhC,GAElCqO,EtCzDW,UsCyDQhE,GAAarK,GAChCsO,EAAa,CAAClX,GAAKsI,IAAQjE,QAAQ2S,IAAmB,EACtDxd,EAAM0d,EAAa,QAAU,SAC7BvF,EAAW4B,GAAe3J,EAAO,CACnChB,UAAWA,EACX+K,SAAUA,EACVE,aAAcA,EACdI,YAAaA,EACbxG,QAASA,IAEP0J,EAAoBD,EAAaD,EAAmB1O,GAAQrI,GAAO+W,EAAmB3O,GAAStI,GAE/F4W,EAAcpd,GAAOsV,EAAWtV,KAClC2d,EAAoBjG,GAAqBiG,IAG3C,IAAIC,EAAmBlG,GAAqBiG,GACxCE,EAAS,GAUb,GARInB,GACFmB,EAAOtmB,KAAK4gB,EAASqF,IAAmB,GAGtCX,GACFgB,EAAOtmB,KAAK4gB,EAASwF,IAAsB,EAAGxF,EAASyF,IAAqB,GAG1EC,EAAOC,OAAM,SAAUC,GACzB,OAAOA,KACL,CACFR,EAAwBnO,EACxBkO,GAAqB,EACrB,MAGFD,EAAUtf,IAAIqR,EAAWyO,GAG3B,GAAIP,EAqBF,IAnBA,IAEIU,EAAQ,SAAeC,GACzB,IAAIC,EAAmB7O,EAAWpZ,MAAK,SAAUmZ,GAC/C,IAAIyO,EAASR,EAAUjf,IAAIgR,GAE3B,GAAIyO,EACF,OAAOA,EAAOxb,MAAM,EAAG4b,GAAIH,OAAM,SAAUC,GACzC,OAAOA,QAKb,GAAIG,EAEF,OADAX,EAAwBW,EACjB,SAIFD,EAnBYnC,EAAiB,EAAI,EAmBZmC,EAAK,GAGpB,UAFFD,EAAMC,GADmBA,KAOpC7N,EAAMhB,YAAcmO,IACtBnN,EAAM0D,cAAc3W,GAAMqf,OAAQ,EAClCpM,EAAMhB,UAAYmO,EAClBnN,EAAM+N,OAAQ,KAUhBjJ,iBAAkB,CAAC,UACnBlQ,KAAM,CACJwX,OAAO,IC7IX,SAAS4B,GAAejG,EAAU7R,EAAM+X,GAQtC,YAPyB,IAArBA,IACFA,EAAmB,CACjB9M,EAAG,EACHC,EAAG,IAIA,CACLhL,IAAK2R,EAAS3R,IAAMF,EAAKgL,OAAS+M,EAAiB7M,EACnDzC,MAAOoJ,EAASpJ,MAAQzI,EAAK+K,MAAQgN,EAAiB9M,EACtDzC,OAAQqJ,EAASrJ,OAASxI,EAAKgL,OAAS+M,EAAiB7M,EACzD9K,KAAMyR,EAASzR,KAAOJ,EAAK+K,MAAQgN,EAAiB9M,GAIxD,SAAS+M,GAAsBnG,GAC7B,MAAO,CAAC3R,GAAKuI,GAAOD,GAAQpI,IAAM6X,MAAK,SAAUC,GAC/C,OAAOrG,EAASqG,IAAS,KAiC7B,IAAAC,GAAe,CACbthB,KAAM,OACN8S,SAAS,EACTC,MAAO,OACPgF,iBAAkB,CAAC,mBACnB5X,GAlCF,SAAc6S,GACZ,IAAIC,EAAQD,EAAKC,MACbjT,EAAOgT,EAAKhT,KACZigB,EAAgBhN,EAAM8D,MAAMlD,UAC5BsE,EAAalF,EAAM8D,MAAMvD,OACzB0N,EAAmBjO,EAAM0D,cAAc4K,gBACvCC,EAAoB5E,GAAe3J,EAAO,CAC5CmK,eAAgB,cAEdqE,EAAoB7E,GAAe3J,EAAO,CAC5CqK,aAAa,IAEXoE,EAA2BT,GAAeO,EAAmBvB,GAC7D0B,EAAsBV,GAAeQ,EAAmBtJ,EAAY+I,GACpEU,EAAoBT,GAAsBO,GAC1CG,EAAmBV,GAAsBQ,GAC7C1O,EAAM0D,cAAc3W,GAAQ,CAC1B0hB,yBAA0BA,EAC1BC,oBAAqBA,EACrBC,kBAAmBA,EACnBC,iBAAkBA,GAEpB5O,EAAMpK,WAAW2K,OAASlW,OAAO8V,OAAO,GAAIH,EAAMpK,WAAW2K,OAAQ,CACnEsO,+BAAgCF,EAChCG,sBAAuBF,MCH3BG,GAAe,CACbhiB,KAAM,SACN8S,SAAS,EACTC,MAAO,OACPiB,SAAU,CAAC,iBACX7T,GA5BF,SAAgBmT,GACd,IAAIL,EAAQK,EAAML,MACdQ,EAAUH,EAAMG,QAChBzT,EAAOsT,EAAMtT,KACbiiB,EAAkBxO,EAAQvK,OAC1BA,OAA6B,IAApB+Y,EAA6B,CAAC,EAAG,GAAKA,EAC/Cpa,EAAOqK,GAAWH,QAAO,SAAUC,EAAKC,GAE1C,OADAD,EAAIC,GA5BD,SAAiCA,EAAW8E,EAAO7N,GACxD,IAAI0N,EAAgB3C,GAAiBhC,GACjCiQ,EAAiB,CAAC3Y,GAAMF,IAAKqE,QAAQkJ,IAAkB,GAAK,EAAI,EAEhE5D,EAAyB,mBAAX9J,EAAwBA,EAAO5L,OAAO8V,OAAO,GAAI2D,EAAO,CACxE9E,UAAWA,KACP/I,EACFiZ,EAAWnP,EAAK,GAChBoP,EAAWpP,EAAK,GAIpB,OAFAmP,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EACtB,CAAC3Y,GAAMqI,IAAOlE,QAAQkJ,IAAkB,EAAI,CACjDxC,EAAGgO,EACH/N,EAAG8N,GACD,CACF/N,EAAG+N,EACH9N,EAAG+N,GAWcC,CAAwBpQ,EAAWgB,EAAM8D,MAAO7N,GAC1D8I,IACN,IACCsQ,EAAwBza,EAAKoL,EAAMhB,WACnCmC,EAAIkO,EAAsBlO,EAC1BC,EAAIiO,EAAsBjO,EAEW,MAArCpB,EAAM0D,cAAcD,gBACtBzD,EAAM0D,cAAcD,cAActC,GAAKA,EACvCnB,EAAM0D,cAAcD,cAAcrC,GAAKA,GAGzCpB,EAAM0D,cAAc3W,GAAQ6H,ICxB9B0a,GAAe,CACbviB,KAAM,gBACN8S,SAAS,EACTC,MAAO,OACP5S,GApBF,SAAuB6S,GACrB,IAAIC,EAAQD,EAAKC,MACbjT,EAAOgT,EAAKhT,KAKhBiT,EAAM0D,cAAc3W,GAAQuc,GAAe,CACzC1I,UAAWZ,EAAM8D,MAAMlD,UACvB7a,QAASia,EAAM8D,MAAMvD,OACrBE,SAAU,WACVzB,UAAWgB,EAAMhB,aAUnBpK,KAAM,IC6FR2a,GAAe,CACbxiB,KAAM,kBACN8S,SAAS,EACTC,MAAO,OACP5S,GA5GF,SAAyB6S,GACvB,IAAIC,EAAQD,EAAKC,MACbQ,EAAUT,EAAKS,QACfzT,EAAOgT,EAAKhT,KACZsf,EAAoB7L,EAAQkJ,SAC5B4C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmB/L,EAAQgM,QAC3BC,OAAoC,IAArBF,GAAsCA,EACrDxC,EAAWvJ,EAAQuJ,SACnBE,EAAezJ,EAAQyJ,aACvBI,EAAc7J,EAAQ6J,YACtBxG,EAAUrD,EAAQqD,QAClB2L,EAAkBhP,EAAQiP,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAwBlP,EAAQmP,aAChCA,OAAyC,IAA1BD,EAAmC,EAAIA,EACtD3H,EAAW4B,GAAe3J,EAAO,CACnC+J,SAAUA,EACVE,aAAcA,EACdpG,QAASA,EACTwG,YAAaA,IAEX1G,EAAgB3C,GAAiBhB,EAAMhB,WACvCuK,EAAYF,GAAarJ,EAAMhB,WAC/B4Q,GAAmBrG,EACnBG,EAAW/G,GAAyBgB,GACpC6I,ECrCY,MDqCS9C,ECrCH,IAAM,IDsCxBjG,EAAgBzD,EAAM0D,cAAcD,cACpCuJ,EAAgBhN,EAAM8D,MAAMlD,UAC5BsE,EAAalF,EAAM8D,MAAMvD,OACzBsP,EAA4C,mBAAjBF,EAA8BA,EAAatlB,OAAO8V,OAAO,GAAIH,EAAM8D,MAAO,CACvG9E,UAAWgB,EAAMhB,aACb2Q,EACF/a,EAAO,CACTuM,EAAG,EACHC,EAAG,GAGL,GAAKqC,EAAL,CAIA,GAAI6I,GAAiBG,EAAc,CACjC,IAAIqD,EAAwB,MAAbpG,EAAmBtT,GAAME,GACpCyZ,EAAuB,MAAbrG,EAAmBhL,GAASC,GACtC/O,EAAmB,MAAb8Z,EAAmB,SAAW,QACpCzT,EAASwN,EAAciG,GACvB7G,EAAMY,EAAciG,GAAY3B,EAAS+H,GACzClN,EAAMa,EAAciG,GAAY3B,EAASgI,GACzCC,EAAWP,GAAUvK,EAAWtV,GAAO,EAAI,EAC3CqgB,E1CxDW,U0CwDF1G,EAAsByD,EAAcpd,GAAOsV,EAAWtV,GAC/DsgB,E1CzDW,U0CyDF3G,GAAuBrE,EAAWtV,IAAQod,EAAcpd,GAGjE4T,EAAexD,EAAMC,SAASU,MAC9BqD,EAAYyL,GAAUjM,EAAenC,GAAcmC,GAAgB,CACrEvC,MAAO,EACPC,OAAQ,GAENiP,EAAqBnQ,EAAM0D,cAAc,oBAAsB1D,EAAM0D,cAAc,oBAAoBG,QxBtEtG,CACLzN,IAAK,EACLuI,MAAO,EACPD,OAAQ,EACRpI,KAAM,GwBmEF8Z,EAAkBD,EAAmBL,GACrCO,EAAkBF,EAAmBJ,GAMrCO,EAAWvN,GAAO,EAAGiK,EAAcpd,GAAMoU,EAAUpU,IACnD2gB,EAAYX,EAAkB5C,EAAcpd,GAAO,EAAIogB,EAAWM,EAAWF,EAAkBP,EAAoBI,EAASK,EAAWF,EAAkBP,EACzJW,EAAYZ,GAAmB5C,EAAcpd,GAAO,EAAIogB,EAAWM,EAAWD,EAAkBR,EAAoBK,EAASI,EAAWD,EAAkBR,EAC1JxL,EAAoBrE,EAAMC,SAASU,OAASsB,GAAgBjC,EAAMC,SAASU,OAC3E8P,EAAepM,EAAiC,MAAbqF,EAAmBrF,EAAkByE,WAAa,EAAIzE,EAAkB0E,YAAc,EAAI,EAC7H2H,EAAsB1Q,EAAM0D,cAAczN,OAAS+J,EAAM0D,cAAczN,OAAO+J,EAAMhB,WAAW0K,GAAY,EAC3GiH,EAAYlN,EAAciG,GAAY6G,EAAYG,EAAsBD,EACxEG,EAAYnN,EAAciG,GAAY8G,EAAYE,EAEtD,GAAIpE,EAAe,CACjB,IAAIuE,EAAkB9N,GAAO0M,EAASxM,GAAQJ,EAAK8N,GAAa9N,EAAK5M,EAAQwZ,EAASzM,GAAQJ,EAAKgO,GAAahO,GAChHa,EAAciG,GAAYmH,EAC1Bjc,EAAK8U,GAAYmH,EAAkB5a,EAGrC,GAAIwW,EAAc,CAChB,IAAIqE,EAAyB,MAAbpH,EAAmBtT,GAAME,GAErCya,EAAwB,MAAbrH,EAAmBhL,GAASC,GAEvCqS,EAAUvN,EAAc+I,GAExByE,EAAOD,EAAUjJ,EAAS+I,GAE1BI,GAAOF,EAAUjJ,EAASgJ,GAE1BI,GAAmBpO,GAAO0M,EAASxM,GAAQgO,EAAMN,GAAaM,EAAMD,EAASvB,EAASzM,GAAQkO,GAAMN,GAAaM,IAErHzN,EAAc+I,GAAW2E,GACzBvc,EAAK4X,GAAW2E,GAAmBH,GAIvChR,EAAM0D,cAAc3W,GAAQ6H,IAS5BkQ,iBAAkB,CAAC,WEhHN,SAASsM,GAAiBC,EAAyBrP,EAAcsP,QAC9D,IAAZA,IACFA,GAAU,GAGZ,ICVoChS,ECJOvZ,EFcvCE,EAAkB2b,GAAmBI,GACrC9L,EAAOC,GAAsBkb,GAC7BE,EAA0B9R,GAAcuC,GACxC+E,EAAS,CACXxQ,WAAY,EACZF,UAAW,GAET8O,EAAU,CACZhE,EAAG,EACHC,EAAG,GAkBL,OAfImQ,IAA4BA,IAA4BD,MACxB,SAA9BnS,GAAY6C,IAChB6F,GAAe5hB,MACb8gB,GCzBgCzH,EDyBT0C,KCxBd3C,GAAUC,IAAUG,GAAcH,GCJxC,CACL/I,YAFyCxQ,EDQbuZ,GCNR/I,WACpBF,UAAWtQ,EAAQsQ,WDGZoR,GAAgBnI,ID0BnBG,GAAcuC,KAChBmD,EAAUhP,GAAsB6L,IACxBb,GAAKa,EAAa+G,WAC1B5D,EAAQ/D,GAAKY,EAAa8G,WACjB7iB,IACTkf,EAAQhE,EAAIyG,GAAoB3hB,KAI7B,CACLkb,EAAGjL,EAAKI,KAAOyQ,EAAOxQ,WAAa4O,EAAQhE,EAC3CC,EAAGlL,EAAKE,IAAM2Q,EAAO1Q,UAAY8O,EAAQ/D,EACzCH,MAAO/K,EAAK+K,MACZC,OAAQhL,EAAKgL,QG7BjB,IAAIsQ,GAAkB,CACpBxS,UAAW,SACXyS,UAAW,GACXhR,SAAU,YAGZ,SAASiR,KACP,IAAK,IAAIC,EAAOC,UAAUpoB,OAAQ4I,EAAO,IAAIhE,MAAMujB,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/Ezf,EAAKyf,GAAQD,UAAUC,GAGzB,OAAQzf,EAAK+b,MAAK,SAAUpoB,GAC1B,QAASA,GAAoD,mBAAlCA,EAAQoQ,0BAIhC,SAAS2b,GAAgBC,QACL,IAArBA,IACFA,EAAmB,IAGrB,IAAIC,EAAoBD,EACpBE,EAAwBD,EAAkBE,iBAC1CA,OAA6C,IAA1BD,EAAmC,GAAKA,EAC3DE,EAAyBH,EAAkBI,eAC3CA,OAA4C,IAA3BD,EAAoCX,GAAkBW,EAC3E,OAAO,SAAsBvR,EAAWL,EAAQC,QAC9B,IAAZA,IACFA,EAAU4R,GAGZ,IC/C6BllB,EAC3BmlB,ED8CErS,EAAQ,CACVhB,UAAW,SACXsT,iBAAkB,GAClB9R,QAASnW,OAAO8V,OAAO,GAAIqR,GAAiBY,GAC5C1O,cAAe,GACfzD,SAAU,CACRW,UAAWA,EACXL,OAAQA,GAEV3K,WAAY,GACZsK,OAAQ,IAENqS,EAAmB,GACnBC,GAAc,EACd3kB,EAAW,CACbmS,MAAOA,EACPyS,WAAY,SAAoBjS,GAC9BkS,IACA1S,EAAMQ,QAAUnW,OAAO8V,OAAO,GAAIiS,EAAgBpS,EAAMQ,QAASA,GACjER,EAAMkH,cAAgB,CACpBtG,UAAWxX,GAAUwX,GAAasH,GAAkBtH,GAAaA,EAAUsK,eAAiBhD,GAAkBtH,EAAUsK,gBAAkB,GAC1I3K,OAAQ2H,GAAkB3H,IAI5B,IExE4BkR,EAC9BkB,EFuEML,EGtCG,SAAwBb,GAErC,IAAIa,EAlCN,SAAeb,GACb,IAAImB,EAAM,IAAInlB,IACVolB,EAAU,IAAI3jB,IACd4jB,EAAS,GA0Bb,OAzBArB,EAAUlnB,SAAQ,SAAUwoB,GAC1BH,EAAIjlB,IAAIolB,EAAShmB,KAAMgmB,MAkBzBtB,EAAUlnB,SAAQ,SAAUwoB,GACrBF,EAAQ/kB,IAAIilB,EAAShmB,OAhB5B,SAASif,EAAK+G,GACZF,EAAQtY,IAAIwY,EAAShmB,MACN,GAAG7G,OAAO6sB,EAAShS,UAAY,GAAIgS,EAASjO,kBAAoB,IACtEva,SAAQ,SAAUyoB,GACzB,IAAKH,EAAQ/kB,IAAIklB,GAAM,CACrB,IAAIC,EAAcL,EAAI5kB,IAAIglB,GAEtBC,GACFjH,EAAKiH,OAIXH,EAAO3rB,KAAK4rB,GAMV/G,CAAK+G,MAGFD,EAKgBvZ,CAAMkY,GAE7B,OAAOvS,GAAeJ,QAAO,SAAUC,EAAKe,GAC1C,OAAOf,EAAI7Y,OAAOosB,EAAiB5rB,QAAO,SAAUqsB,GAClD,OAAOA,EAASjT,QAAUA,QAE3B,IH8B0BoT,EExEKzB,EFwEsB,GAAGvrB,OAAOgsB,EAAkBlS,EAAMQ,QAAQiR,WEvE9FkB,EAASlB,EAAU3S,QAAO,SAAU6T,EAAQQ,GAC9C,IAAIC,EAAWT,EAAOQ,EAAQpmB,MAK9B,OAJA4lB,EAAOQ,EAAQpmB,MAAQqmB,EAAW/oB,OAAO8V,OAAO,GAAIiT,EAAUD,EAAS,CACrE3S,QAASnW,OAAO8V,OAAO,GAAIiT,EAAS5S,QAAS2S,EAAQ3S,SACrD5L,KAAMvK,OAAO8V,OAAO,GAAIiT,EAASxe,KAAMue,EAAQve,QAC5Cue,EACER,IACN,IAEItoB,OAAOC,KAAKqoB,GAAQC,KAAI,SAAUhlB,GACvC,OAAO+kB,EAAO/kB,QFsGV,OAvCAoS,EAAMsS,iBAAmBA,EAAiB5rB,QAAO,SAAU2sB,GACzD,OAAOA,EAAExT,WAqJbG,EAAMsS,iBAAiB/nB,SAAQ,SAAUgb,GACvC,IAAIxY,EAAOwY,EAAMxY,KACbumB,EAAgB/N,EAAM/E,QACtBA,OAA4B,IAAlB8S,EAA2B,GAAKA,EAC1ClT,EAASmF,EAAMnF,OAEnB,GAAsB,mBAAXA,EAAuB,CAChC,IAAImT,EAAYnT,EAAO,CACrBJ,MAAOA,EACPjT,KAAMA,EACNc,SAAUA,EACV2S,QAASA,IAKX+R,EAAiBprB,KAAKosB,GAFT,kBA7HR1lB,EAASuZ,UAOlBoM,YAAa,WACX,IAAIhB,EAAJ,CAIA,IAAIiB,EAAkBzT,EAAMC,SACxBW,EAAY6S,EAAgB7S,UAC5BL,EAASkT,EAAgBlT,OAG7B,GAAKmR,GAAiB9Q,EAAWL,GAAjC,CASAP,EAAM8D,MAAQ,CACZlD,UAAWwQ,GAAiBxQ,EAAWqB,GAAgB1B,GAAoC,UAA3BP,EAAMQ,QAAQC,UAC9EF,OAAQc,GAAcd,IAOxBP,EAAM+N,OAAQ,EACd/N,EAAMhB,UAAYgB,EAAMQ,QAAQxB,UAKhCgB,EAAMsS,iBAAiB/nB,SAAQ,SAAUwoB,GACvC,OAAO/S,EAAM0D,cAAcqP,EAAShmB,MAAQ1C,OAAO8V,OAAO,GAAI4S,EAASne,SAIzE,IAAK,IAAIwE,EAAQ,EAAGA,EAAQ4G,EAAMsS,iBAAiB9oB,OAAQ4P,IAUzD,IAAoB,IAAhB4G,EAAM+N,MAAV,CAMA,IAAI2F,EAAwB1T,EAAMsS,iBAAiBlZ,GAC/ClM,EAAKwmB,EAAsBxmB,GAC3BymB,EAAyBD,EAAsBlT,QAC/CoJ,OAAsC,IAA3B+J,EAAoC,GAAKA,EACpD5mB,EAAO2mB,EAAsB3mB,KAEf,mBAAPG,IACT8S,EAAQ9S,EAAG,CACT8S,MAAOA,EACPQ,QAASoJ,EACT7c,KAAMA,EACNc,SAAUA,KACNmS,QAjBNA,EAAM+N,OAAQ,EACd3U,GAAS,KAsBfgO,QCjM2Bla,EDiMV,WACf,OAAO,IAAI0mB,SAAQ,SAAUC,GAC3BhmB,EAAS2lB,cACTK,EAAQ7T,OClMT,WAUL,OATKqS,IACHA,EAAU,IAAIuB,SAAQ,SAAUC,GAC9BD,QAAQC,UAAUC,MAAK,WACrBzB,OAAU0B,EACVF,EAAQ3mB,YAKPmlB,ID2LL2B,QAAS,WACPtB,IACAF,GAAc,IAIlB,IAAKd,GAAiB9Q,EAAWL,GAK/B,OAAO1S,EAmCT,SAAS6kB,IACPH,EAAiBhoB,SAAQ,SAAU2C,GACjC,OAAOA,OAETqlB,EAAmB,GAGrB,OAvCA1kB,EAAS4kB,WAAWjS,GAASsT,MAAK,SAAU9T,IACrCwS,GAAehS,EAAQyT,eAC1BzT,EAAQyT,cAAcjU,MAqCnBnS,GAGJ,IAAIqmB,GAA4BpC,KIzPnCoC,GAA4BpC,GAAgB,CAC9CI,iBAFqB,CAACrL,GAAgBpD,GAAe0Q,GAAeC,MCMlEF,GAA4BpC,GAAgB,CAC9CI,iBAFqB,CAACrL,GAAgBpD,GAAe0Q,GAAeC,GAAane,GAAQoe,GAAM/F,GAAiB3N,GAAOnD,2KpDNvG,+BAEC,YACF,sBACY,2BACP,kBACF,mBACG,4DAQC,kBACN,iBACK,uBAEC,kBACN,iBACK,wBAEE,oBACN,mBACK,0JqDGxB,MAYM8W,GAAiB,IAAIvpB,OAAQ,4BAsB7BwpB,GAAgB9nB,IAAU,UAAY,YACtC+nB,GAAmB/nB,IAAU,YAAc,UAC3CgoB,GAAmBhoB,IAAU,aAAe,eAC5CioB,GAAsBjoB,IAAU,eAAiB,aACjDkoB,GAAkBloB,IAAU,aAAe,cAC3CmoB,GAAiBnoB,IAAU,cAAgB,aAE3CkK,GAAU,CACdV,OAAQ,CAAC,EAAG,GACZ8T,SAAU,kBACVnJ,UAAW,SACXrV,QAAS,UACTspB,aAAc,KACdC,WAAW,GAGP5d,GAAc,CAClBjB,OAAQ,0BACR8T,SAAU,mBACVnJ,UAAW,0BACXrV,QAAS,SACTspB,aAAc,yBACdC,UAAW,oBASb,MAAMC,WAAiB3hB,EACrBC,YAAYtN,EAASoE,GACnBqN,MAAMzR,GAEN2K,KAAKskB,QAAU,KACftkB,KAAKuH,QAAUvH,KAAKwH,WAAW/N,GAC/BuG,KAAKukB,MAAQvkB,KAAKwkB,kBAClBxkB,KAAKykB,UAAYzkB,KAAK0kB,gBAEtB1kB,KAAK+H,qBAKW9B,qBAChB,OAAOA,GAGaO,yBACpB,OAAOA,GAGMlK,kBACb,MAxFS,WA6FXgI,SACMvJ,EAAWiF,KAAK4C,YAIH5C,KAAK4C,SAAS5H,UAAUC,SA3ErB,QA8ElB+E,KAAK8M,OAIP9M,KAAK+M,QAGPA,OACE,GAAIhS,EAAWiF,KAAK4C,WAAa5C,KAAKukB,MAAMvpB,UAAUC,SAtFlC,QAuFlB,OAGF,MAAMgR,EAASoY,GAASM,qBAAqB3kB,KAAK4C,UAC5C9C,EAAgB,CACpBA,cAAeE,KAAK4C,UAKtB,IAFkBtC,EAAamB,QAAQzB,KAAK4C,SAtG5B,mBAsGkD9C,GAEpDiC,iBAAd,CAKA,GAAI/B,KAAKykB,UACP5f,EAAYC,iBAAiB9E,KAAKukB,MAAO,SAAU,YAC9C,CACL,QAAsB,IAAXK,GACT,MAAM,IAAIrqB,UAAU,gEAGtB,IAAIuf,EAAmB9Z,KAAK4C,SAEG,WAA3B5C,KAAKuH,QAAQ2I,UACf4J,EAAmB7N,EACVvT,EAAUsH,KAAKuH,QAAQ2I,WAChC4J,EAAmBjhB,EAAWmH,KAAKuH,QAAQ2I,WACA,iBAA3BlQ,KAAKuH,QAAQ2I,YAC7B4J,EAAmB9Z,KAAKuH,QAAQ2I,WAGlC,MAAMiU,EAAenkB,KAAK6kB,mBACpBC,EAAkBX,EAAapD,UAAU5rB,KAAKktB,GAA8B,gBAAlBA,EAAShmB,OAA+C,IAArBgmB,EAASlT,SAE5GnP,KAAKskB,QAAUM,GAAoB9K,EAAkB9Z,KAAKukB,MAAOJ,GAE7DW,GACFjgB,EAAYC,iBAAiB9E,KAAKukB,MAAO,SAAU,UAQnD,iBAAkBjvB,SAASC,kBAC5B0W,EAAOnI,QA9Hc,gBA+HtB,GAAGtO,UAAUF,SAASwG,KAAK/F,UACxB8D,QAAQ0S,GAAQjM,EAAaQ,GAAGyL,EAAM,YAAa9Q,IAGxDuE,KAAK4C,SAASmiB,QACd/kB,KAAK4C,SAAS2B,aAAa,iBAAiB,GAE5CvE,KAAKukB,MAAMvpB,UAAUsJ,OA9ID,QA+IpBtE,KAAK4C,SAAS5H,UAAUsJ,OA/IJ,QAgJpBhE,EAAamB,QAAQzB,KAAK4C,SAtJT,oBAsJgC9C,IAGnDgN,OACE,GAAI/R,EAAWiF,KAAK4C,YAAc5C,KAAKukB,MAAMvpB,UAAUC,SApJnC,QAqJlB,OAGF,MAAM6E,EAAgB,CACpBA,cAAeE,KAAK4C,UAGtB5C,KAAKglB,cAAcllB,GAGrBgD,UACM9C,KAAKskB,SACPtkB,KAAKskB,QAAQhB,UAGfxc,MAAMhE,UAGR4T,SACE1W,KAAKykB,UAAYzkB,KAAK0kB,gBAClB1kB,KAAKskB,SACPtkB,KAAKskB,QAAQ5N,SAMjB3O,qBACEzH,EAAaQ,GAAGd,KAAK4C,SAtLJ,oBAsL2BzD,IAC1CA,EAAMsD,iBACNzC,KAAKsE,WAIT0gB,cAAcllB,GACMQ,EAAamB,QAAQzB,KAAK4C,SAjM5B,mBAiMkD9C,GACpDiC,mBAMV,iBAAkBzM,SAASC,iBAC7B,GAAGC,UAAUF,SAASwG,KAAK/F,UACxB8D,QAAQ0S,GAAQjM,EAAaC,IAAIgM,EAAM,YAAa9Q,IAGrDuE,KAAKskB,SACPtkB,KAAKskB,QAAQhB,UAGftjB,KAAKukB,MAAMvpB,UAAU4C,OAxMD,QAyMpBoC,KAAK4C,SAAS5H,UAAU4C,OAzMJ,QA0MpBoC,KAAK4C,SAAS2B,aAAa,gBAAiB,SAC5CM,EAAYE,oBAAoB/E,KAAKukB,MAAO,UAC5CjkB,EAAamB,QAAQzB,KAAK4C,SApNR,qBAoNgC9C,IAGpD0H,WAAW/N,GAST,GARAA,EAAS,IACJuG,KAAK2C,YAAYsD,WACjBpB,EAAYI,kBAAkBjF,KAAK4C,aACnCnJ,GAGLF,EA7OS,WA6OaE,EAAQuG,KAAK2C,YAAY6D,aAEf,iBAArB/M,EAAOyW,YAA2BxX,EAAUe,EAAOyW,YACV,mBAA3CzW,EAAOyW,UAAUzK,sBAGxB,MAAM,IAAIlL,UAnPH,WAmPqBC,cAAP,kGAGvB,OAAOf,EAGT+qB,kBACE,OAAOtvB,EAAe2B,KAAKmJ,KAAK4C,SA5Nd,kBA4NuC,GAG3DqiB,gBACE,MAAMC,EAAiBllB,KAAK4C,SAASvM,WAErC,GAAI6uB,EAAelqB,UAAUC,SAvON,WAwOrB,OAAOgpB,GAGT,GAAIiB,EAAelqB,UAAUC,SA1OJ,aA2OvB,OAAOipB,GAIT,MAAMiB,EAAkF,QAA1EjtB,iBAAiB8H,KAAKukB,OAAOa,iBAAiB,iBAAiBztB,OAE7E,OAAIutB,EAAelqB,UAAUC,SAnPP,UAoPbkqB,EAAQrB,GAAmBD,GAG7BsB,EAAQnB,GAAsBD,GAGvCW,gBACE,OAA0D,OAAnD1kB,KAAK4C,SAASkB,QAAS,WAGhCuhB,aACE,MAAM9f,OAAEA,GAAWvF,KAAKuH,QAExB,MAAsB,iBAAXhC,EACFA,EAAO7N,MAAM,KAAKwqB,IAAIzd,GAAOrM,OAAOyS,SAASpG,EAAK,KAGrC,mBAAXc,EACF+f,GAAc/f,EAAO+f,EAAYtlB,KAAK4C,UAGxC2C,EAGTsf,mBACE,MAAMU,EAAwB,CAC5BjX,UAAWtO,KAAKilB,gBAChBlE,UAAW,CAAC,CACV1kB,KAAM,kBACNyT,QAAS,CACPuJ,SAAUrZ,KAAKuH,QAAQ8R,WAG3B,CACEhd,KAAM,SACNyT,QAAS,CACPvK,OAAQvF,KAAKqlB,iBAanB,MAP6B,WAAzBrlB,KAAKuH,QAAQ1M,UACf0qB,EAAsBxE,UAAY,CAAC,CACjC1kB,KAAM,cACN8S,SAAS,KAIN,IACFoW,KACsC,mBAA9BvlB,KAAKuH,QAAQ4c,aAA8BnkB,KAAKuH,QAAQ4c,aAAaoB,GAAyBvlB,KAAKuH,QAAQ4c,cAI1HqB,gBAAgBrmB,GACd,MAAMsmB,EAAQvwB,EAAeC,KApSF,8DAoS+B6K,KAAKukB,OAAOvuB,OAAOyE,GAE7E,IAAKgrB,EAAM3sB,OACT,OAGF,IAAI4P,EAAQ+c,EAAM1b,QAAQ5K,EAAMkB,QAlUf,YAqUblB,EAAMjC,KAAwBwL,EAAQ,GACxCA,IArUiB,cAyUfvJ,EAAMjC,KAA0BwL,EAAQ+c,EAAM3sB,OAAS,GACzD4P,IAIFA,GAAmB,IAAXA,EAAe,EAAIA,EAE3B+c,EAAM/c,GAAOqc,QAKS3hB,yBAAC/N,EAASoE,GAChC,IAAIyK,EAAOlH,EAAKM,IAAIjI,EA9VP,eAqWb,GAJK6O,IACHA,EAAO,IAAImgB,GAAShvB,EAHY,iBAAXoE,EAAsBA,EAAS,OAMhC,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjByK,EAAKzK,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CyK,EAAKzK,MAIa2J,uBAAC3J,GACrB,OAAOuG,KAAKiE,MAAK,WACfogB,GAASqB,kBAAkB1lB,KAAMvG,MAIpB2J,kBAACjE,GAChB,GAAIA,IA5WmB,IA4WTA,EAAMyF,QAAiD,UAAfzF,EAAMqB,MA/WhD,QA+WoErB,EAAMjC,KACpF,OAGF,MAAMyoB,EAAUzwB,EAAeC,KA7VN,+BA+VzB,IAAK,IAAI8J,EAAI,EAAGC,EAAMymB,EAAQ7sB,OAAQmG,EAAIC,EAAKD,IAAK,CAClD,MAAM2mB,EAAU5oB,EAAKM,IAAIqoB,EAAQ1mB,GA5XtB,eA6XX,IAAK2mB,IAAyC,IAA9BA,EAAQre,QAAQ6c,UAC9B,SAGF,IAAKwB,EAAQhjB,SAAS5H,UAAUC,SA3Wd,QA4WhB,SAGF,MAAM6E,EAAgB,CACpBA,cAAe8lB,EAAQhjB,UAGzB,GAAIzD,EAAO,CACT,MAAM0mB,EAAe1mB,EAAM0mB,eACrBC,EAAeD,EAAaruB,SAASouB,EAAQrB,OACnD,GACEsB,EAAaruB,SAASouB,EAAQhjB,WACC,WAA9BgjB,EAAQre,QAAQ6c,YAA2B0B,GACb,YAA9BF,EAAQre,QAAQ6c,WAA2B0B,EAE5C,SAIF,GAAIF,EAAQrB,MAAMtpB,SAASkE,EAAMkB,UAA4B,UAAflB,EAAMqB,MA/Y5C,QA+YgErB,EAAMjC,KAAoB,qCAAqC5C,KAAK6E,EAAMkB,OAAOyJ,UACvJ,SAGiB,UAAf3K,EAAMqB,OACRV,EAAcimB,WAAa5mB,GAI/BymB,EAAQZ,cAAcllB,IAICsD,4BAAC/N,GAC1B,OAAOwC,EAAuBxC,IAAYA,EAAQgB,WAGxB+M,6BAACjE,GAQ3B,GAAI,kBAAkB7E,KAAK6E,EAAMkB,OAAOyJ,SAza1B,UA0aZ3K,EAAMjC,KA3aO,WA2aeiC,EAAMjC,MAvajB,cAwafiC,EAAMjC,KAzaO,YAyamBiC,EAAMjC,KACtCiC,EAAMkB,OAAOyD,QApZC,oBAqZf8f,GAAetpB,KAAK6E,EAAMjC,KAC3B,OAGF,MAAM8oB,EAAWhmB,KAAKhF,UAAUC,SAhaZ,QAkapB,IAAK+qB,GApbU,WAobE7mB,EAAMjC,IACrB,OAMF,GAHAiC,EAAMsD,iBACNtD,EAAM8mB,kBAEFlrB,EAAWiF,MACb,OAGF,MAAMkmB,EAAkB,IAAMlmB,KAAK9J,QAvaV,+BAua0C8J,KAAO9K,EAAewB,KAAKsJ,KAvarE,+BAuaiG,GAE1H,GAjce,WAicXb,EAAMjC,IAGR,OAFAgpB,IAAkBnB,aAClBV,GAAS8B,aAINH,GApcY,YAocC7mB,EAAMjC,KAncL,cAmc6BiC,EAAMjC,IAKjD8oB,GA3cS,UA2cG7mB,EAAMjC,IAKvBmnB,GAAS+B,YAAYF,KAAmBV,gBAAgBrmB,GAJtDklB,GAAS8B,aALTD,IAAkBG,SAmBxB/lB,EAAaQ,GAAGxL,SA5cgB,+BASH,8BAmc2C+uB,GAASiC,uBACjFhmB,EAAaQ,GAAGxL,SA7cgB,+BAUV,iBAmc2C+uB,GAASiC,uBAC1EhmB,EAAaQ,GAAGxL,SA/cc,6BA+ckB+uB,GAAS8B,YACzD7lB,EAAaQ,GAAGxL,SA9cc,6BA8ckB+uB,GAAS8B,YACzD7lB,EAAaQ,GAAGxL,SAjdc,6BAUD,+BAucyC,SAAU6J,GAC9EA,EAAMsD,iBACN4hB,GAASqB,kBAAkB1lB,SAU7B/D,EAAmBooB,ICtgBnB,MAGMkC,GAAW,KAEf,MAAMC,EAAgBlxB,SAASC,gBAAgBue,YAC/C,OAAO7c,KAAK+R,IAAI/Q,OAAOwuB,WAAaD,IAGhC1Z,GAAO,CAACyD,EAAQgW,QACpBG,KAEAC,GAAsB,OAAQ,eAAgBC,GAAmBA,EAAkBrW,GAEnFoW,GAd6B,oDAciB,eAAgBC,GAAmBA,EAAkBrW,GACnGoW,GAd8B,cAciB,cAAeC,GAAmBA,EAAkBrW,IAG/FmW,GAAmB,KACvB,MAAMG,EAAcvxB,SAASwG,KAAKpB,MAAM2c,SACpCwP,GACFhiB,EAAYC,iBAAiBxP,SAASwG,KAAM,WAAY+qB,GAG1DvxB,SAASwG,KAAKpB,MAAM2c,SAAW,UAG3BsP,GAAwB,CAACvxB,EAAU0xB,EAAW3qB,KAClD,MAAM4qB,EAAiBR,KACvBrxB,EAAeC,KAAKC,GACjByE,QAAQxE,IACP,GAAIA,IAAYC,SAASwG,MAAQ7D,OAAOwuB,WAAapxB,EAAQye,YAAciT,EACzE,OAGF,MAAMF,EAAcxxB,EAAQqF,MAAMosB,GAC5BF,EAAkB3uB,OAAOC,iBAAiB7C,GAASyxB,GACzDjiB,EAAYC,iBAAiBzP,EAASyxB,EAAWD,GACjDxxB,EAAQqF,MAAMosB,GAAgB3qB,EAAS/D,OAAOC,WAAWuuB,IAA7B,QAI5BvJ,GAAQ,KACZ2J,GAAwB,OAAQ,YAChCA,GAAwB,OAAQ,gBAChCA,GA7C6B,oDA6CmB,gBAChDA,GA7C8B,cA6CmB,gBAG7CA,GAA0B,CAAC5xB,EAAU0xB,KACzC5xB,EAAeC,KAAKC,GAAUyE,QAAQxE,IACpC,MAAM2E,EAAQ6K,EAAYS,iBAAiBjQ,EAASyxB,QAC/B,IAAV9sB,EACT3E,EAAQqF,MAAMusB,eAAeH,IAE7BjiB,EAAYE,oBAAoB1P,EAASyxB,GACzCzxB,EAAQqF,MAAMosB,GAAa9sB,MCxD3BiM,GAAU,CACdxL,WAAW,EACX0I,YAAY,EACZM,YAAanO,SAASwG,KACtBorB,cAAe,MAGX1gB,GAAc,CAClB/L,UAAW,UACX0I,WAAY,UACZM,YAAa,UACbyjB,cAAe,mBASjB,MAAMC,GACJxkB,YAAYlJ,GACVuG,KAAKuH,QAAUvH,KAAKwH,WAAW/N,GAC/BuG,KAAKonB,aAAc,EACnBpnB,KAAK4C,SAAW,KAGlBmK,KAAK5Q,GACE6D,KAAKuH,QAAQ9M,WAKlBuF,KAAKqnB,UAEDrnB,KAAKuH,QAAQpE,YACfzH,EAAOsE,KAAKsnB,eAGdtnB,KAAKsnB,cAActsB,UAAU6O,IAvBT,QAyBpB7J,KAAKunB,kBAAkB,KACrB1qB,EAAQV,MAbRU,EAAQV,GAiBZ2Q,KAAK3Q,GACE6D,KAAKuH,QAAQ9M,WAKlBuF,KAAKsnB,cAActsB,UAAU4C,OApCT,QAsCpBoC,KAAKunB,kBAAkB,KACrBvnB,KAAK8C,UACLjG,EAAQV,MARRU,EAAQV,GAcZmrB,cACE,IAAKtnB,KAAK4C,SAAU,CAClB,MAAM4kB,EAAWlyB,SAASmyB,cAAc,OACxCD,EAASE,UAnDa,iBAoDlB1nB,KAAKuH,QAAQpE,YACfqkB,EAASxsB,UAAU6O,IApDH,QAuDlB7J,KAAK4C,SAAW4kB,EAGlB,OAAOxnB,KAAK4C,SAGd4E,WAAW/N,GAQT,OAPAA,EAAS,IACJwM,MACmB,iBAAXxM,EAAsBA,EAAS,KAGrCgK,YAAchK,EAAOgK,aAAenO,SAASwG,KACpDvC,EAtES,WAsEaE,EAAQ+M,IACvB/M,EAGT4tB,UACMrnB,KAAKonB,cAITpnB,KAAKuH,QAAQ9D,YAAYkkB,YAAY3nB,KAAKsnB,eAE1ChnB,EAAaQ,GAAGd,KAAKsnB,cA5EA,wBA4EgC,KACnDzqB,EAAQmD,KAAKuH,QAAQ2f,iBAGvBlnB,KAAKonB,aAAc,GAGrBtkB,UACO9C,KAAKonB,cAIV9mB,EAAaC,IAAIP,KAAK4C,SAxFD,yBA0FrB5C,KAAKsnB,cAAcjxB,WAAW2N,YAAYhE,KAAK4C,UAC/C5C,KAAKonB,aAAc,GAGrBG,kBAAkBprB,GAChB,IAAK6D,KAAKuH,QAAQpE,WAEhB,YADAtG,EAAQV,GAIV,MAAMyrB,EAA6B9vB,EAAiCkI,KAAKsnB,eACzEhnB,EAAaS,IAAIf,KAAKsnB,cAAe,gBAAiB,IAAMzqB,EAAQV,IACpEpD,EAAqBiH,KAAKsnB,cAAeM,ICpG7C,MAMM3hB,GAAU,CACduhB,UAAU,EACVrhB,UAAU,EACV4e,OAAO,GAGHve,GAAc,CAClBghB,SAAU,mBACVrhB,SAAU,UACV4e,MAAO,WAgCT,MAAM8C,WAAcnlB,EAClBC,YAAYtN,EAASoE,GACnBqN,MAAMzR,GAEN2K,KAAKuH,QAAUvH,KAAKwH,WAAW/N,GAC/BuG,KAAK8nB,QAAU5yB,EAAeW,QAhBV,gBAgBmCmK,KAAK4C,UAC5D5C,KAAK+nB,UAAY/nB,KAAKgoB,sBACtBhoB,KAAKioB,UAAW,EAChBjoB,KAAKkoB,sBAAuB,EAC5BloB,KAAKmM,kBAAmB,EAKRlG,qBAChB,OAAOA,GAGM3J,kBACb,MAlES,QAuEXgI,OAAOxE,GACL,OAAOE,KAAKioB,SAAWjoB,KAAK8M,OAAS9M,KAAK+M,KAAKjN,GAGjDiN,KAAKjN,GACH,GAAIE,KAAKioB,UAAYjoB,KAAKmM,iBACxB,OAGEnM,KAAKmoB,gBACPnoB,KAAKmM,kBAAmB,GAG1B,MAAMic,EAAY9nB,EAAamB,QAAQzB,KAAK4C,SA/D5B,gBA+DkD,CAChE9C,cAAAA,IAGEE,KAAKioB,UAAYG,EAAUrmB,mBAI/B/B,KAAKioB,UAAW,EAEhBI,KAEA/yB,SAASwG,KAAKd,UAAU6O,IAjEJ,cAmEpB7J,KAAKsoB,gBAELtoB,KAAKuoB,kBACLvoB,KAAKwoB,kBAELloB,EAAaQ,GAAGd,KAAK4C,SA9EI,yBAcC,4BAgEiDzD,GAASa,KAAK8M,KAAK3N,IAE9FmB,EAAaQ,GAAGd,KAAK8nB,QA7EQ,6BA6E0B,KACrDxnB,EAAaS,IAAIf,KAAK4C,SA/EG,2BA+E8BzD,IACjDA,EAAMkB,SAAWL,KAAK4C,WACxB5C,KAAKkoB,sBAAuB,OAKlCloB,KAAKyoB,cAAc,IAAMzoB,KAAK0oB,aAAa5oB,KAG7CgN,KAAK3N,GAKH,GAJIA,GACFA,EAAMsD,kBAGHzC,KAAKioB,UAAYjoB,KAAKmM,iBACzB,OAKF,GAFkB7L,EAAamB,QAAQzB,KAAK4C,SA3G5B,iBA6GFb,iBACZ,OAGF/B,KAAKioB,UAAW,EAChB,MAAM9kB,EAAanD,KAAKmoB,cAEpBhlB,IACFnD,KAAKmM,kBAAmB,GAG1BnM,KAAKuoB,kBACLvoB,KAAKwoB,kBAELloB,EAAaC,IAAIjL,SAtHE,oBAwHnB0K,KAAK4C,SAAS5H,UAAU4C,OA9GJ,QAgHpB0C,EAAaC,IAAIP,KAAK4C,SAxHG,0BAyHzBtC,EAAaC,IAAIP,KAAK8nB,QAtHO,8BAwH7B9nB,KAAKkD,eAAe,IAAMlD,KAAK2oB,aAAc3oB,KAAK4C,SAAUO,GAG9DL,UACE,CAAC7K,OAAQ+H,KAAK8nB,SACXjuB,QAAQ+uB,GAAetoB,EAAaC,IAAIqoB,EAvJ5B,cAyJf5oB,KAAK+nB,UAAUjlB,UACfgE,MAAMhE,UAONxC,EAAaC,IAAIjL,SA5IE,oBA+IrBuzB,eACE7oB,KAAKsoB,gBAKPN,sBACE,OAAO,IAAIb,GAAS,CAClB1sB,UAAWoG,QAAQb,KAAKuH,QAAQigB,UAChCrkB,WAAYnD,KAAKmoB,gBAIrB3gB,WAAW/N,GAOT,OANAA,EAAS,IACJwM,MACApB,EAAYI,kBAAkBjF,KAAK4C,aACnCnJ,GAELF,EAzLS,QAyLaE,EAAQ+M,IACvB/M,EAGTivB,aAAa5oB,GACX,MAAMqD,EAAanD,KAAKmoB,cAClBW,EAAY5zB,EAAeW,QA1JT,cA0JsCmK,KAAK8nB,SAE9D9nB,KAAK4C,SAASvM,YAAc2J,KAAK4C,SAASvM,WAAWC,WAAaC,KAAKC,cAE1ElB,SAASwG,KAAK6rB,YAAY3nB,KAAK4C,UAGjC5C,KAAK4C,SAASlI,MAAMG,QAAU,QAC9BmF,KAAK4C,SAASoC,gBAAgB,eAC9BhF,KAAK4C,SAAS2B,aAAa,cAAc,GACzCvE,KAAK4C,SAAS2B,aAAa,OAAQ,UACnCvE,KAAK4C,SAAS+C,UAAY,EAEtBmjB,IACFA,EAAUnjB,UAAY,GAGpBxC,GACFzH,EAAOsE,KAAK4C,UAGd5C,KAAK4C,SAAS5H,UAAU6O,IAnLJ,QAqLhB7J,KAAKuH,QAAQwd,OACf/kB,KAAK+oB,gBAcP/oB,KAAKkD,eAXsB,KACrBlD,KAAKuH,QAAQwd,OACf/kB,KAAK4C,SAASmiB,QAGhB/kB,KAAKmM,kBAAmB,EACxB7L,EAAamB,QAAQzB,KAAK4C,SA1MX,iBA0MkC,CAC/C9C,cAAAA,KAIoCE,KAAK8nB,QAAS3kB,GAGxD4lB,gBACEzoB,EAAaC,IAAIjL,SAlNE,oBAmNnBgL,EAAaQ,GAAGxL,SAnNG,mBAmNsB6J,IACnC7J,WAAa6J,EAAMkB,QACnBL,KAAK4C,WAAazD,EAAMkB,QACvBL,KAAK4C,SAAS3H,SAASkE,EAAMkB,SAChCL,KAAK4C,SAASmiB,UAKpBwD,kBACMvoB,KAAKioB,SACP3nB,EAAaQ,GAAGd,KAAK4C,SA3NI,2BA2N6BzD,IAChDa,KAAKuH,QAAQpB,UAlPN,WAkPkBhH,EAAMjC,KACjCiC,EAAMsD,iBACNzC,KAAK8M,QACK9M,KAAKuH,QAAQpB,UArPd,WAqP0BhH,EAAMjC,KACzC8C,KAAKgpB,+BAIT1oB,EAAaC,IAAIP,KAAK4C,SApOG,4BAwO7B4lB,kBACMxoB,KAAKioB,SACP3nB,EAAaQ,GAAG7I,OA5OA,kBA4OsB,IAAM+H,KAAKsoB,iBAEjDhoB,EAAaC,IAAItI,OA9OD,mBAkPpB0wB,aACE3oB,KAAK4C,SAASlI,MAAMG,QAAU,OAC9BmF,KAAK4C,SAAS2B,aAAa,eAAe,GAC1CvE,KAAK4C,SAASoC,gBAAgB,cAC9BhF,KAAK4C,SAASoC,gBAAgB,QAC9BhF,KAAKmM,kBAAmB,EACxBnM,KAAK+nB,UAAUjb,KAAK,KAClBxX,SAASwG,KAAKd,UAAU4C,OAlPN,cAmPlBoC,KAAKipB,oBACLC,KACA5oB,EAAamB,QAAQzB,KAAK4C,SAhQV,qBAoQpB6lB,cAActsB,GACZmE,EAAaQ,GAAGd,KAAK4C,SAhQI,yBAgQ2BzD,IAC9Ca,KAAKkoB,qBACPloB,KAAKkoB,sBAAuB,EAI1B/oB,EAAMkB,SAAWlB,EAAMgqB,iBAIG,IAA1BnpB,KAAKuH,QAAQigB,SACfxnB,KAAK8M,OAC8B,WAA1B9M,KAAKuH,QAAQigB,UACtBxnB,KAAKgpB,gCAIThpB,KAAK+nB,UAAUhb,KAAK5Q,GAGtBgsB,cACE,OAAOnoB,KAAK4C,SAAS5H,UAAUC,SA9QX,QAiRtB+tB,6BAEE,GADkB1oB,EAAamB,QAAQzB,KAAK4C,SA/RlB,0BAgSZb,iBACZ,OAGF,MAAMqnB,EAAqBppB,KAAK4C,SAAS6V,aAAenjB,SAASC,gBAAgBse,aAE5EuV,IACHppB,KAAK4C,SAASlI,MAAM6c,UAAY,UAGlCvX,KAAK4C,SAAS5H,UAAU6O,IA3RF,gBA4RtB,MAAMwf,EAA0BvxB,EAAiCkI,KAAK8nB,SACtExnB,EAAaC,IAAIP,KAAK4C,SAAU,iBAChCtC,EAAaS,IAAIf,KAAK4C,SAAU,gBAAiB,KAC/C5C,KAAK4C,SAAS5H,UAAU4C,OA/RJ,gBAgSfwrB,IACH9oB,EAAaS,IAAIf,KAAK4C,SAAU,gBAAiB,KAC/C5C,KAAK4C,SAASlI,MAAM6c,UAAY,KAElCxe,EAAqBiH,KAAK4C,SAAUymB,MAGxCtwB,EAAqBiH,KAAK4C,SAAUymB,GACpCrpB,KAAK4C,SAASmiB,QAOhBuD,gBACE,MAAMc,EAAqBppB,KAAK4C,SAAS6V,aAAenjB,SAASC,gBAAgBse,aAC3EkT,EAAiBuC,KACjBC,EAAoBxC,EAAiB,IAErCwC,GAAqBH,IAAuBrtB,KAAawtB,IAAsBH,GAAsBrtB,OACzGiE,KAAK4C,SAASlI,MAAM8uB,YAAiBzC,EAAF,OAGhCwC,IAAsBH,IAAuBrtB,MAAcwtB,GAAqBH,GAAsBrtB,OACzGiE,KAAK4C,SAASlI,MAAM+uB,aAAkB1C,EAAF,MAIxCkC,oBACEjpB,KAAK4C,SAASlI,MAAM8uB,YAAc,GAClCxpB,KAAK4C,SAASlI,MAAM+uB,aAAe,GAKfrmB,uBAAC3J,EAAQqG,GAC7B,OAAOE,KAAKiE,MAAK,WACf,MAAMC,EAAO2jB,GAAMzB,YAAYpmB,OAAS,IAAI6nB,GAAM7nB,KAAwB,iBAAXvG,EAAsBA,EAAS,IAE9F,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjByK,EAAKzK,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CyK,EAAKzK,GAAQqG,QAWnBQ,EAAaQ,GAAGxL,SAhWc,0BASD,4BAuVyC,SAAU6J,GAC9E,MAAMkB,EAASxI,EAAuBmI,MAElC,CAAC,IAAK,QAAQxI,SAASwI,KAAK8J,UAC9B3K,EAAMsD,iBAGRnC,EAAaS,IAAIV,EA/WC,gBA+WmB+nB,IAC/BA,EAAUrmB,kBAKdzB,EAAaS,IAAIV,EAtXC,kBAsXqB,KACjC5F,EAAUuF,OACZA,KAAK+kB,aAKE8C,GAAMzB,YAAY/lB,IAAW,IAAIwnB,GAAMxnB,IAE/CiE,OAAOtE,SAUd/D,EAAmB4rB,IC/ZnB,MAOM5hB,GAAU,CACduhB,UAAU,EACVrhB,UAAU,EACVkQ,QAAQ,GAGJ7P,GAAc,CAClBghB,SAAU,UACVrhB,SAAU,UACVkQ,OAAQ,WAwBV,MAAMqT,WAAkBhnB,EACtBC,YAAYtN,EAASoE,GACnBqN,MAAMzR,GAEN2K,KAAKuH,QAAUvH,KAAKwH,WAAW/N,GAC/BuG,KAAKioB,UAAW,EAChBjoB,KAAK+nB,UAAY/nB,KAAKgoB,sBACtBhoB,KAAK+H,qBAKQzL,kBACb,MArDS,YAwDO2J,qBAChB,OAAOA,GAKT3B,OAAOxE,GACL,OAAOE,KAAKioB,SAAWjoB,KAAK8M,OAAS9M,KAAK+M,KAAKjN,GAGjDiN,KAAKjN,GACCE,KAAKioB,UAIS3nB,EAAamB,QAAQzB,KAAK4C,SAjD5B,oBAiDkD,CAAE9C,cAAAA,IAEtDiC,mBAId/B,KAAKioB,UAAW,EAChBjoB,KAAK4C,SAASlI,MAAMI,WAAa,UAEjCkF,KAAK+nB,UAAUhb,OAEV/M,KAAKuH,QAAQ8O,SAChBgS,KACAroB,KAAK2pB,uBAAuB3pB,KAAK4C,WAGnC5C,KAAK4C,SAASoC,gBAAgB,eAC9BhF,KAAK4C,SAAS2B,aAAa,cAAc,GACzCvE,KAAK4C,SAAS2B,aAAa,OAAQ,UACnCvE,KAAK4C,SAAS5H,UAAU6O,IAvEJ,QA6EpB7J,KAAKkD,eAJoB,KACvB5C,EAAamB,QAAQzB,KAAK4C,SAtEX,qBAsEkC,CAAE9C,cAAAA,KAGfE,KAAK4C,UAAU,IAGvDkK,OACO9M,KAAKioB,WAIQ3nB,EAAamB,QAAQzB,KAAK4C,SAhF5B,qBAkFFb,mBAIdzB,EAAaC,IAAIjL,SApFE,wBAqFnB0K,KAAK4C,SAASgnB,OACd5pB,KAAKioB,UAAW,EAChBjoB,KAAK4C,SAAS5H,UAAU4C,OA9FJ,QA+FpBoC,KAAK+nB,UAAUjb,OAef9M,KAAKkD,eAboB,KACvBlD,KAAK4C,SAAS2B,aAAa,eAAe,GAC1CvE,KAAK4C,SAASoC,gBAAgB,cAC9BhF,KAAK4C,SAASoC,gBAAgB,QAC9BhF,KAAK4C,SAASlI,MAAMI,WAAa,SAE5BkF,KAAKuH,QAAQ8O,QAChB6S,KAGF5oB,EAAamB,QAAQzB,KAAK4C,SArGV,wBAwGoB5C,KAAK4C,UAAU,KAGvDE,UACE9C,KAAK+nB,UAAUjlB,UACfgE,MAAMhE,UACNxC,EAAaC,IAAIjL,SA7GE,wBAkHrBkS,WAAW/N,GAOT,OANAA,EAAS,IACJwM,MACApB,EAAYI,kBAAkBjF,KAAK4C,aAChB,iBAAXnJ,EAAsBA,EAAS,IAE5CF,EAlJS,YAkJaE,EAAQ+M,IACvB/M,EAGTuuB,sBACE,OAAO,IAAIb,GAAS,CAClB1sB,UAAWuF,KAAKuH,QAAQigB,SACxBrkB,YAAY,EACZM,YAAazD,KAAK4C,SAASvM,WAC3B6wB,cAAe,IAAMlnB,KAAK8M,SAI9B6c,uBAAuBt0B,GACrBiL,EAAaC,IAAIjL,SAtIE,wBAuInBgL,EAAaQ,GAAGxL,SAvIG,uBAuIsB6J,IACnC7J,WAAa6J,EAAMkB,QACrBhL,IAAY8J,EAAMkB,QACjBhL,EAAQ4F,SAASkE,EAAMkB,SACxBhL,EAAQ0vB,UAGZ1vB,EAAQ0vB,QAGVhd,qBACEzH,EAAaQ,GAAGd,KAAK4C,SAhJI,6BAGC,gCA6IiD,IAAM5C,KAAK8M,QAEtFxM,EAAaQ,GAAGd,KAAK4C,SAjJM,+BAiJ2BzD,IAChDa,KAAKuH,QAAQpB,UA1KJ,WA0KgBhH,EAAMjC,KACjC8C,KAAK8M,SAOW1J,uBAAC3J,GACrB,OAAOuG,KAAKiE,MAAK,WACf,MAAMC,EAAOlH,EAAKM,IAAI0C,KAxLX,iBAwL8B,IAAI0pB,GAAU1pB,KAAwB,iBAAXvG,EAAsBA,EAAS,IAEnG,GAAsB,iBAAXA,EAAX,CAIA,QAAqB4pB,IAAjBnf,EAAKzK,IAAyBA,EAAOhC,WAAW,MAAmB,gBAAXgC,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CyK,EAAKzK,GAAQuG,WAWnBM,EAAaQ,GAAGxL,SAnLc,8BAKD,gCA8KyC,SAAU6J,GAC9E,MAAMkB,EAASxI,EAAuBmI,MAMtC,GAJI,CAAC,IAAK,QAAQxI,SAASwI,KAAK8J,UAC9B3K,EAAMsD,iBAGJ1H,EAAWiF,MACb,OAGFM,EAAaS,IAAIV,EAhMG,sBAgMmB,KAEjC5F,EAAUuF,OACZA,KAAK+kB,UAKT,MAAM8E,EAAe30B,EAAeW,QA7MhB,mBA8MhBg0B,GAAgBA,IAAiBxpB,GACnCqpB,GAAUtD,YAAYyD,GAAc/c,QAGzB9P,EAAKM,IAAI+C,EArOP,iBAqO4B,IAAIqpB,GAAUrpB,IAEpDiE,OAAOtE,SAGdM,EAAaQ,GAAG7I,OAvOa,6BAuOgB,KAC3C/C,EAAeC,KAxNK,mBAwNe0E,QAAQiwB,IAAO9sB,EAAKM,IAAIwsB,EA3O5C,iBA2O6D,IAAIJ,GAAUI,IAAK/c,UASjG9Q,EAAmBytB,IC1QnB,MAAMK,GAAW,IAAIvrB,IAAI,CACvB,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUIwrB,GAAmB,6DAOnBC,GAAmB,qIAEnBC,GAAmB,CAACC,EAAMC,KAC9B,MAAMC,EAAWF,EAAKzb,SAAStU,cAE/B,GAAIgwB,EAAqB5yB,SAAS6yB,GAChC,OAAIN,GAAS3sB,IAAIitB,IACRxpB,QAAQmpB,GAAiB1vB,KAAK6vB,EAAKG,YAAcL,GAAiB3vB,KAAK6vB,EAAKG,YAMvF,MAAMC,EAASH,EAAqBp0B,OAAOw0B,GAAaA,aAAqBnwB,QAG7E,IAAK,IAAI4E,EAAI,EAAGC,EAAMqrB,EAAOzxB,OAAQmG,EAAIC,EAAKD,IAC5C,GAAIsrB,EAAOtrB,GAAG3E,KAAK+vB,GACjB,OAAO,EAIX,OAAO,GAqCF,SAASI,GAAaC,EAAYC,EAAWC,GAClD,IAAKF,EAAW5xB,OACd,OAAO4xB,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAGpB,MACMG,GADY,IAAI5yB,OAAO6yB,WACKC,gBAAgBL,EAAY,aACxDM,EAAgBrxB,OAAOC,KAAK+wB,GAC5Bpb,EAAW,GAAG/Z,UAAUq1B,EAAgB/uB,KAAKnG,iBAAiB,MAEpE,IAAK,IAAIsJ,EAAI,EAAGC,EAAMqQ,EAASzW,OAAQmG,EAAIC,EAAKD,IAAK,CACnD,MAAM6qB,EAAKva,EAAStQ,GACdgsB,EAASnB,EAAGpb,SAAStU,cAE3B,IAAK4wB,EAAcxzB,SAASyzB,GAAS,CACnCnB,EAAGzzB,WAAW2N,YAAY8lB,GAE1B,SAGF,MAAMoB,EAAgB,GAAG11B,UAAUs0B,EAAG5kB,YAChCimB,EAAoB,GAAG31B,OAAOm1B,EAAU,MAAQ,GAAIA,EAAUM,IAAW,IAE/EC,EAAcrxB,QAAQswB,IACfD,GAAiBC,EAAMgB,IAC1BrB,EAAG9kB,gBAAgBmlB,EAAKzb,YAK9B,OAAOmc,EAAgB/uB,KAAKsvB,UC1F9B,MAIMC,GAAqB,IAAIhxB,OAAQ,wBAA6B,KAC9DixB,GAAwB,IAAI9sB,IAAI,CAAC,WAAY,YAAa,eAE1DgI,GAAc,CAClB+kB,UAAW,UACXC,SAAU,SACVC,MAAO,4BACPhqB,QAAS,SACTiqB,MAAO,kBACPxT,KAAM,UACN9iB,SAAU,mBACVkZ,UAAW,oBACX/I,OAAQ,0BACR2H,UAAW,2BACX+O,mBAAoB,QACpB5C,SAAU,mBACVsS,YAAa,oBACbC,SAAU,UACVhB,WAAY,kBACZD,UAAW,SACXxG,aAAc,0BAGV0H,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOjwB,IAAU,OAAS,QAC1BkwB,OAAQ,SACRC,KAAMnwB,IAAU,QAAU,QAGtBkK,GAAU,CACdslB,WAAW,EACXC,SAAU,+GAIV/pB,QAAS,cACTgqB,MAAO,GACPC,MAAO,EACPxT,MAAM,EACN9iB,UAAU,EACVkZ,UAAW,MACX/I,OAAQ,CAAC,EAAG,GACZ2H,WAAW,EACX+O,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C5C,SAAU,kBACVsS,YAAa,GACbC,UAAU,EACVhB,WAAY,KACZD,UDhC8B,CAE9BwB,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAzCP,kBA0C7B5Q,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/B6Q,KAAM,GACN5Q,EAAG,GACH6Q,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJ/tB,EAAG,GACHguB,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,ICEJ1J,aAAc,MAGV1rB,GAAQ,CACZq1B,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAuBf,MAAMC,WAAgB9rB,EACpBC,YAAYtN,EAASoE,GACnB,QAAsB,IAAXmrB,GACT,MAAM,IAAIrqB,UAAU,+DAGtBuM,MAAMzR,GAGN2K,KAAKyuB,YAAa,EAClBzuB,KAAK0uB,SAAW,EAChB1uB,KAAK2uB,YAAc,GACnB3uB,KAAK4uB,eAAiB,GACtB5uB,KAAKskB,QAAU,KAGftkB,KAAKuH,QAAUvH,KAAKwH,WAAW/N,GAC/BuG,KAAK6uB,IAAM,KAEX7uB,KAAK8uB,gBAKW7oB,qBAChB,OAAOA,GAGM3J,kBACb,MAxHS,UA2HK7D,mBACd,OAAOA,GAGa+N,yBACpB,OAAOA,GAKTuoB,SACE/uB,KAAKyuB,YAAa,EAGpBO,UACEhvB,KAAKyuB,YAAa,EAGpBQ,gBACEjvB,KAAKyuB,YAAczuB,KAAKyuB,WAG1BnqB,OAAOnF,GACL,GAAKa,KAAKyuB,WAIV,GAAItvB,EAAO,CACT,MAAMymB,EAAU5lB,KAAKkvB,6BAA6B/vB,GAElDymB,EAAQgJ,eAAevI,OAAST,EAAQgJ,eAAevI,MAEnDT,EAAQuJ,uBACVvJ,EAAQwJ,OAAO,KAAMxJ,GAErBA,EAAQyJ,OAAO,KAAMzJ,OAElB,CACL,GAAI5lB,KAAKsvB,gBAAgBt0B,UAAUC,SAxFjB,QA0FhB,YADA+E,KAAKqvB,OAAO,KAAMrvB,MAIpBA,KAAKovB,OAAO,KAAMpvB,OAItB8C,UACE4G,aAAa1J,KAAK0uB,UAElBpuB,EAAaC,IAAIP,KAAK4C,SAASkB,QAAS,UAAwB,gBAAiB9D,KAAKuvB,mBAElFvvB,KAAK6uB,KAAO7uB,KAAK6uB,IAAIx4B,YACvB2J,KAAK6uB,IAAIx4B,WAAW2N,YAAYhE,KAAK6uB,KAGnC7uB,KAAKskB,SACPtkB,KAAKskB,QAAQhB,UAGfxc,MAAMhE,UAGRiK,OACE,GAAoC,SAAhC/M,KAAK4C,SAASlI,MAAMG,QACtB,MAAM,IAAIyI,MAAM,uCAGlB,IAAMtD,KAAKwvB,kBAAmBxvB,KAAKyuB,WACjC,OAGF,MAAMrG,EAAY9nB,EAAamB,QAAQzB,KAAK4C,SAAU5C,KAAK2C,YAAYlK,MAAMu1B,MACvEyB,EAAar0B,EAAe4E,KAAK4C,UACjC8sB,EAA4B,OAAfD,EACjBzvB,KAAK4C,SAASiM,cAActZ,gBAAgB0F,SAAS+E,KAAK4C,UAC1D6sB,EAAWx0B,SAAS+E,KAAK4C,UAE3B,GAAIwlB,EAAUrmB,mBAAqB2tB,EACjC,OAGF,MAAMb,EAAM7uB,KAAKsvB,gBACXK,EAAQ54B,EAAOiJ,KAAK2C,YAAYrG,MAEtCuyB,EAAItqB,aAAa,KAAMorB,GACvB3vB,KAAK4C,SAAS2B,aAAa,mBAAoBorB,GAE/C3vB,KAAK4vB,aAED5vB,KAAKuH,QAAQgkB,WACfsD,EAAI7zB,UAAU6O,IA/II,QAkJpB,MAAMyE,EAA8C,mBAA3BtO,KAAKuH,QAAQ+G,UACpCtO,KAAKuH,QAAQ+G,UAAU1Y,KAAKoK,KAAM6uB,EAAK7uB,KAAK4C,UAC5C5C,KAAKuH,QAAQ+G,UAETuhB,EAAa7vB,KAAK8vB,eAAexhB,GACvCtO,KAAK+vB,oBAAoBF,GAEzB,MAAM3iB,UAAEA,GAAclN,KAAKuH,QAC3BvK,EAAKC,IAAI4xB,EAAK7uB,KAAK2C,YAAYE,SAAU7C,MAEpCA,KAAK4C,SAASiM,cAActZ,gBAAgB0F,SAAS+E,KAAK6uB,OAC7D3hB,EAAUya,YAAYkH,GACtBvuB,EAAamB,QAAQzB,KAAK4C,SAAU5C,KAAK2C,YAAYlK,MAAMy1B,WAGzDluB,KAAKskB,QACPtkB,KAAKskB,QAAQ5N,SAEb1W,KAAKskB,QAAUM,GAAoB5kB,KAAK4C,SAAUisB,EAAK7uB,KAAK6kB,iBAAiBgL,IAG/EhB,EAAI7zB,UAAU6O,IArKM,QAuKpB,MAAM8hB,EAAkD,mBAA7B3rB,KAAKuH,QAAQokB,YAA6B3rB,KAAKuH,QAAQokB,cAAgB3rB,KAAKuH,QAAQokB,YAC3GA,GACFkD,EAAI7zB,UAAU6O,OAAO8hB,EAAYj0B,MAAM,MAOrC,iBAAkBpC,SAASC,iBAC7B,GAAGC,UAAUF,SAASwG,KAAK/F,UAAU8D,QAAQxE,IAC3CiL,EAAaQ,GAAGzL,EAAS,YAAaoG,KAI1C,MAWM0H,EAAanD,KAAK6uB,IAAI7zB,UAAUC,SAnMlB,QAoMpB+E,KAAKkD,eAZY,KACf,MAAM8sB,EAAiBhwB,KAAK2uB,YAE5B3uB,KAAK2uB,YAAc,KACnBruB,EAAamB,QAAQzB,KAAK4C,SAAU5C,KAAK2C,YAAYlK,MAAMw1B,OAvLzC,QAyLd+B,GACFhwB,KAAKqvB,OAAO,KAAMrvB,OAKQA,KAAK6uB,IAAK1rB,GAG1C2J,OACE,IAAK9M,KAAKskB,QACR,OAGF,MAAMuK,EAAM7uB,KAAKsvB,gBAqBjB,GADkBhvB,EAAamB,QAAQzB,KAAK4C,SAAU5C,KAAK2C,YAAYlK,MAAMq1B,MAC/D/rB,iBACZ,OAGF8sB,EAAI7zB,UAAU4C,OAnOM,QAuOhB,iBAAkBtI,SAASC,iBAC7B,GAAGC,UAAUF,SAASwG,KAAK/F,UACxB8D,QAAQxE,GAAWiL,EAAaC,IAAIlL,EAAS,YAAaoG,IAG/DuE,KAAK4uB,eAAL,OAAqC,EACrC5uB,KAAK4uB,eAAL,OAAqC,EACrC5uB,KAAK4uB,eAAL,OAAqC,EAErC,MAAMzrB,EAAanD,KAAK6uB,IAAI7zB,UAAUC,SAlPlB,QAmPpB+E,KAAKkD,eAtCY,KACXlD,KAAKmvB,yBA1MU,SA8MfnvB,KAAK2uB,aAAoCE,EAAIx4B,YAC/Cw4B,EAAIx4B,WAAW2N,YAAY6qB,GAG7B7uB,KAAKiwB,iBACLjwB,KAAK4C,SAASoC,gBAAgB,oBAC9B1E,EAAamB,QAAQzB,KAAK4C,SAAU5C,KAAK2C,YAAYlK,MAAMs1B,QAEvD/tB,KAAKskB,UACPtkB,KAAKskB,QAAQhB,UACbtjB,KAAKskB,QAAU,QAuBWtkB,KAAK6uB,IAAK1rB,GACxCnD,KAAK2uB,YAAc,GAGrBjY,SACuB,OAAjB1W,KAAKskB,SACPtkB,KAAKskB,QAAQ5N,SAMjB8Y,gBACE,OAAO3uB,QAAQb,KAAKkwB,YAGtBZ,gBACE,GAAItvB,KAAK6uB,IACP,OAAO7uB,KAAK6uB,IAGd,MAAMx5B,EAAUC,SAASmyB,cAAc,OAIvC,OAHApyB,EAAQ+1B,UAAYprB,KAAKuH,QAAQikB,SAEjCxrB,KAAK6uB,IAAMx5B,EAAQU,SAAS,GACrBiK,KAAK6uB,IAGde,aACE,MAAMf,EAAM7uB,KAAKsvB,gBACjBtvB,KAAKmwB,kBAAkBj7B,EAAeW,QA1QX,iBA0Q2Cg5B,GAAM7uB,KAAKkwB,YACjFrB,EAAI7zB,UAAU4C,OAlRM,OAEA,QAmRtBuyB,kBAAkB96B,EAAS+6B,GACzB,GAAgB,OAAZ/6B,EAIJ,OAAIqD,EAAU03B,IACZA,EAAUv3B,EAAWu3B,QAGjBpwB,KAAKuH,QAAQ2Q,KACXkY,EAAQ/5B,aAAehB,IACzBA,EAAQ+1B,UAAY,GACpB/1B,EAAQsyB,YAAYyI,IAGtB/6B,EAAQg7B,YAAcD,EAAQC,mBAM9BrwB,KAAKuH,QAAQ2Q,MACXlY,KAAKuH,QAAQqkB,WACfwE,EAAU3F,GAAa2F,EAASpwB,KAAKuH,QAAQojB,UAAW3qB,KAAKuH,QAAQqjB,aAGvEv1B,EAAQ+1B,UAAYgF,GAEpB/6B,EAAQg7B,YAAcD,GAI1BF,WACE,IAAIzE,EAAQzrB,KAAK4C,SAAStL,aAAa,0BAQvC,OANKm0B,IACHA,EAAsC,mBAAvBzrB,KAAKuH,QAAQkkB,MAC1BzrB,KAAKuH,QAAQkkB,MAAM71B,KAAKoK,KAAK4C,UAC7B5C,KAAKuH,QAAQkkB,OAGVA,EAGT6E,iBAAiBT,GACf,MAAmB,UAAfA,EACK,MAGU,SAAfA,EACK,QAGFA,EAKTX,6BAA6B/vB,EAAOymB,GAClC,MAAM2K,EAAUvwB,KAAK2C,YAAYE,SAQjC,OAPA+iB,EAAUA,GAAW5oB,EAAKM,IAAI6B,EAAMY,eAAgBwwB,MAGlD3K,EAAU,IAAI5lB,KAAK2C,YAAYxD,EAAMY,eAAgBC,KAAKwwB,sBAC1DxzB,EAAKC,IAAIkC,EAAMY,eAAgBwwB,EAAS3K,IAGnCA,EAGTP,aACE,MAAM9f,OAAEA,GAAWvF,KAAKuH,QAExB,MAAsB,iBAAXhC,EACFA,EAAO7N,MAAM,KAAKwqB,IAAIzd,GAAOrM,OAAOyS,SAASpG,EAAK,KAGrC,mBAAXc,EACF+f,GAAc/f,EAAO+f,EAAYtlB,KAAK4C,UAGxC2C,EAGTsf,iBAAiBgL,GACf,MAAMtK,EAAwB,CAC5BjX,UAAWuhB,EACX9O,UAAW,CACT,CACE1kB,KAAM,OACNyT,QAAS,CACPmM,mBAAoBjc,KAAKuH,QAAQ0U,qBAGrC,CACE5f,KAAM,SACNyT,QAAS,CACPvK,OAAQvF,KAAKqlB,eAGjB,CACEhpB,KAAM,kBACNyT,QAAS,CACPuJ,SAAUrZ,KAAKuH,QAAQ8R,WAG3B,CACEhd,KAAM,QACNyT,QAAS,CACPza,QAAU,IAAG2K,KAAK2C,YAAYrG,eAGlC,CACED,KAAM,WACN8S,SAAS,EACTC,MAAO,aACP5S,GAAI0H,GAAQlE,KAAKywB,6BAA6BvsB,KAGlDqf,cAAerf,IACTA,EAAK4L,QAAQxB,YAAcpK,EAAKoK,WAClCtO,KAAKywB,6BAA6BvsB,KAKxC,MAAO,IACFqhB,KACsC,mBAA9BvlB,KAAKuH,QAAQ4c,aAA8BnkB,KAAKuH,QAAQ4c,aAAaoB,GAAyBvlB,KAAKuH,QAAQ4c,cAI1H4L,oBAAoBF,GAClB7vB,KAAKsvB,gBAAgBt0B,UAAU6O,IAAK,cAAkB7J,KAAKswB,iBAAiBT,IAG9EC,eAAexhB,GACb,OAAOud,GAAcvd,EAAU9T,eAGjCs0B,gBACmB9uB,KAAKuH,QAAQ9F,QAAQ/J,MAAM,KAEnCmC,QAAQ4H,IACf,GAAgB,UAAZA,EACFnB,EAAaQ,GAAGd,KAAK4C,SAAU5C,KAAK2C,YAAYlK,MAAM01B,MAAOnuB,KAAKuH,QAAQnS,SAAU+J,GAASa,KAAKsE,OAAOnF,SACpG,GA3ZU,WA2ZNsC,EAA4B,CACrC,MAAMivB,EA/ZQ,UA+ZEjvB,EACdzB,KAAK2C,YAAYlK,MAAM61B,WACvBtuB,KAAK2C,YAAYlK,MAAM21B,QACnBuC,EAlaQ,UAkaGlvB,EACfzB,KAAK2C,YAAYlK,MAAM81B,WACvBvuB,KAAK2C,YAAYlK,MAAM41B,SAEzB/tB,EAAaQ,GAAGd,KAAK4C,SAAU8tB,EAAS1wB,KAAKuH,QAAQnS,SAAU+J,GAASa,KAAKovB,OAAOjwB,IACpFmB,EAAaQ,GAAGd,KAAK4C,SAAU+tB,EAAU3wB,KAAKuH,QAAQnS,SAAU+J,GAASa,KAAKqvB,OAAOlwB,OAIzFa,KAAKuvB,kBAAoB,KACnBvvB,KAAK4C,UACP5C,KAAK8M,QAITxM,EAAaQ,GAAGd,KAAK4C,SAASkB,QAAS,UAAwB,gBAAiB9D,KAAKuvB,mBAEjFvvB,KAAKuH,QAAQnS,SACf4K,KAAKuH,QAAU,IACVvH,KAAKuH,QACR9F,QAAS,SACTrM,SAAU,IAGZ4K,KAAK4wB,YAITA,YACE,MAAMnF,EAAQzrB,KAAK4C,SAAStL,aAAa,SACnCu5B,SAA2B7wB,KAAK4C,SAAStL,aAAa,2BAExDm0B,GAA+B,WAAtBoF,KACX7wB,KAAK4C,SAAS2B,aAAa,yBAA0BknB,GAAS,KAC1DA,GAAUzrB,KAAK4C,SAAStL,aAAa,eAAkB0I,KAAK4C,SAASytB,aACvErwB,KAAK4C,SAAS2B,aAAa,aAAcknB,GAG3CzrB,KAAK4C,SAAS2B,aAAa,QAAS,KAIxC6qB,OAAOjwB,EAAOymB,GACZA,EAAU5lB,KAAKkvB,6BAA6B/vB,EAAOymB,GAE/CzmB,IACFymB,EAAQgJ,eACS,YAAfzvB,EAAMqB,KAhdQ,QADA,UAkdZ,GAGFolB,EAAQ0J,gBAAgBt0B,UAAUC,SA5dlB,SAEC,SA0d8C2qB,EAAQ+I,YACzE/I,EAAQ+I,YA3dW,QA+drBjlB,aAAakc,EAAQ8I,UAErB9I,EAAQ+I,YAjea,OAmehB/I,EAAQre,QAAQmkB,OAAU9F,EAAQre,QAAQmkB,MAAM3e,KAKrD6Y,EAAQ8I,SAAWp1B,WAAW,KAxeT,SAyefssB,EAAQ+I,aACV/I,EAAQ7Y,QAET6Y,EAAQre,QAAQmkB,MAAM3e,MARvB6Y,EAAQ7Y,QAWZsiB,OAAOlwB,EAAOymB,GACZA,EAAU5lB,KAAKkvB,6BAA6B/vB,EAAOymB,GAE/CzmB,IACFymB,EAAQgJ,eACS,aAAfzvB,EAAMqB,KA9eQ,QADA,SAgfZolB,EAAQhjB,SAAS3H,SAASkE,EAAMW,gBAGlC8lB,EAAQuJ,yBAIZzlB,aAAakc,EAAQ8I,UAErB9I,EAAQ+I,YA7fY,MA+ff/I,EAAQre,QAAQmkB,OAAU9F,EAAQre,QAAQmkB,MAAM5e,KAKrD8Y,EAAQ8I,SAAWp1B,WAAW,KApgBV,QAqgBdssB,EAAQ+I,aACV/I,EAAQ9Y,QAET8Y,EAAQre,QAAQmkB,MAAM5e,MARvB8Y,EAAQ9Y,QAWZqiB,uBACE,IAAK,MAAM1tB,KAAWzB,KAAK4uB,eACzB,GAAI5uB,KAAK4uB,eAAentB,GACtB,OAAO,EAIX,OAAO,EAGT+F,WAAW/N,GACT,MAAMq3B,EAAiBjsB,EAAYI,kBAAkBjF,KAAK4C,UAqC1D,OAnCAjJ,OAAOC,KAAKk3B,GAAgBj3B,QAAQk3B,IAC9BzF,GAAsBluB,IAAI2zB,WACrBD,EAAeC,MAI1Bt3B,EAAS,IACJuG,KAAK2C,YAAYsD,WACjB6qB,KACmB,iBAAXr3B,GAAuBA,EAASA,EAAS,KAG/CyT,WAAiC,IAArBzT,EAAOyT,UAAsB5X,SAASwG,KAAOjD,EAAWY,EAAOyT,WAEtD,iBAAjBzT,EAAOiyB,QAChBjyB,EAAOiyB,MAAQ,CACb3e,KAAMtT,EAAOiyB,MACb5e,KAAMrT,EAAOiyB,QAIW,iBAAjBjyB,EAAOgyB,QAChBhyB,EAAOgyB,MAAQhyB,EAAOgyB,MAAMvxB,YAGA,iBAAnBT,EAAO22B,UAChB32B,EAAO22B,QAAU32B,EAAO22B,QAAQl2B,YAGlCX,EAjoBS,UAioBaE,EAAQuG,KAAK2C,YAAY6D,aAE3C/M,EAAOmyB,WACTnyB,EAAO+xB,SAAWf,GAAahxB,EAAO+xB,SAAU/xB,EAAOkxB,UAAWlxB,EAAOmxB,aAGpEnxB,EAGT+2B,qBACE,MAAM/2B,EAAS,GAEf,GAAIuG,KAAKuH,QACP,IAAK,MAAMrK,KAAO8C,KAAKuH,QACjBvH,KAAK2C,YAAYsD,QAAQ/I,KAAS8C,KAAKuH,QAAQrK,KACjDzD,EAAOyD,GAAO8C,KAAKuH,QAAQrK,IAKjC,OAAOzD,EAGTw2B,iBACE,MAAMpB,EAAM7uB,KAAKsvB,gBACX0B,EAAWnC,EAAIv3B,aAAa,SAAS6C,MAAMkxB,IAChC,OAAb2F,GAAqBA,EAASl4B,OAAS,GACzCk4B,EAAS9O,IAAI+O,GAASA,EAAMt5B,QACzBkC,QAAQq3B,GAAUrC,EAAI7zB,UAAU4C,OAAOszB,IAI9CT,6BAA6BnL,GAC3B,MAAMhW,MAAEA,GAAUgW,EAEbhW,IAILtP,KAAK6uB,IAAMvf,EAAMC,SAASM,OAC1B7P,KAAKiwB,iBACLjwB,KAAK+vB,oBAAoB/vB,KAAK8vB,eAAexgB,EAAMhB,aAK/BlL,uBAAC3J,GACrB,OAAOuG,KAAKiE,MAAK,WACf,IAAIC,EAAOlH,EAAKM,IAAI0C,KAhrBT,cAirBX,MAAMuH,EAA4B,iBAAX9N,GAAuBA,EAE9C,IAAKyK,IAAQ,eAAe5J,KAAKb,MAI5ByK,IACHA,EAAO,IAAIsqB,GAAQxuB,KAAMuH,IAGL,iBAAX9N,GAAqB,CAC9B,QAA4B,IAAjByK,EAAKzK,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CyK,EAAKzK,UAabwC,EAAmBuyB,IC/tBnB,MAIMnD,GAAqB,IAAIhxB,OAAQ,wBAA6B,KAE9D4L,GAAU,IACXuoB,GAAQvoB,QACXqI,UAAW,QACX/I,OAAQ,CAAC,EAAG,GACZ9D,QAAS,QACT2uB,QAAS,GACT5E,SAAU,+IAONhlB,GAAc,IACfgoB,GAAQhoB,YACX4pB,QAAS,6BAGL33B,GAAQ,CACZq1B,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAef,MAAM4C,WAAgB3C,GAGFvoB,qBAChB,OAAOA,GAGM3J,kBACb,MAzDS,UA4DK7D,mBACd,OAAOA,GAGa+N,yBACpB,OAAOA,GAKTgpB,gBACE,OAAOxvB,KAAKkwB,YAAclwB,KAAKoxB,cAGjCxB,aACE,MAAMf,EAAM7uB,KAAKsvB,gBAGjBtvB,KAAKmwB,kBAAkBj7B,EAAeW,QAtCnB,kBAsC2Cg5B,GAAM7uB,KAAKkwB,YACzE,IAAIE,EAAUpwB,KAAKoxB,cACI,mBAAZhB,IACTA,EAAUA,EAAQx6B,KAAKoK,KAAK4C,WAG9B5C,KAAKmwB,kBAAkBj7B,EAAeW,QA3CjB,gBA2C2Cg5B,GAAMuB,GAEtEvB,EAAI7zB,UAAU4C,OAjDM,OACA,QAqDtBmyB,oBAAoBF,GAClB7vB,KAAKsvB,gBAAgBt0B,UAAU6O,IAAK,cAAkB7J,KAAKswB,iBAAiBT,IAG9EuB,cACE,OAAOpxB,KAAK4C,SAAStL,aAAa,oBAAsB0I,KAAKuH,QAAQ6oB,QAGvEH,iBACE,MAAMpB,EAAM7uB,KAAKsvB,gBACX0B,EAAWnC,EAAIv3B,aAAa,SAAS6C,MAAMkxB,IAChC,OAAb2F,GAAqBA,EAASl4B,OAAS,GACzCk4B,EAAS9O,IAAI+O,GAASA,EAAMt5B,QACzBkC,QAAQq3B,GAAUrC,EAAI7zB,UAAU4C,OAAOszB,IAMxB9tB,uBAAC3J,GACrB,OAAOuG,KAAKiE,MAAK,WACf,IAAIC,EAAOlH,EAAKM,IAAI0C,KA/GT,cAgHX,MAAMuH,EAA4B,iBAAX9N,EAAsBA,EAAS,KAEtD,IAAKyK,IAAQ,eAAe5J,KAAKb,MAI5ByK,IACHA,EAAO,IAAIitB,GAAQnxB,KAAMuH,GACzBvK,EAAKC,IAAI+C,KAxHA,aAwHgBkE,IAGL,iBAAXzK,GAAqB,CAC9B,QAA4B,IAAjByK,EAAKzK,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CyK,EAAKzK,UAabwC,EAAmBk1B,ICvInB,MAKMlrB,GAAU,CACdV,OAAQ,GACR8rB,OAAQ,OACRhxB,OAAQ,IAGJmG,GAAc,CAClBjB,OAAQ,SACR8rB,OAAQ,SACRhxB,OAAQ,oBA2BV,MAAMixB,WAAkB5uB,EACtBC,YAAYtN,EAASoE,GACnBqN,MAAMzR,GACN2K,KAAKuxB,eAA2C,SAA1BvxB,KAAK4C,SAASkH,QAAqB7R,OAAS+H,KAAK4C,SACvE5C,KAAKuH,QAAUvH,KAAKwH,WAAW/N,GAC/BuG,KAAK0M,UAAa,GAAE1M,KAAKuH,QAAQlH,qBAAiCL,KAAKuH,QAAQlH,4BAAkCL,KAAKuH,QAAQlH,wBAC9HL,KAAKwxB,SAAW,GAChBxxB,KAAKyxB,SAAW,GAChBzxB,KAAK0xB,cAAgB,KACrB1xB,KAAK2xB,cAAgB,EAErBrxB,EAAaQ,GAAGd,KAAKuxB,eAlCH,sBAkCiC,IAAMvxB,KAAK4xB,YAE9D5xB,KAAK6xB,UACL7xB,KAAK4xB,WAKW3rB,qBAChB,OAAOA,GAGM3J,kBACb,MAjES,YAsEXu1B,UACE,MAAMC,EAAa9xB,KAAKuxB,iBAAmBvxB,KAAKuxB,eAAet5B,OAvC7C,SACE,WA0Cd85B,EAAuC,SAAxB/xB,KAAKuH,QAAQ8pB,OAChCS,EACA9xB,KAAKuH,QAAQ8pB,OAETW,EA9Cc,aA8CDD,EACjB/xB,KAAKiyB,gBACL,EAEFjyB,KAAKwxB,SAAW,GAChBxxB,KAAKyxB,SAAW,GAChBzxB,KAAK2xB,cAAgB3xB,KAAKkyB,mBAEVh9B,EAAeC,KAAK6K,KAAK0M,WAEjCwV,IAAI7sB,IACV,MAAM88B,EAAiBv6B,EAAuBvC,GACxCgL,EAAS8xB,EAAiBj9B,EAAeW,QAAQs8B,GAAkB,KAEzE,GAAI9xB,EAAQ,CACV,MAAM+xB,EAAY/xB,EAAOoF,wBACzB,GAAI2sB,EAAU7hB,OAAS6hB,EAAU5hB,OAC/B,MAAO,CACL3L,EAAYktB,GAAc1xB,GAAQqF,IAAMssB,EACxCG,GAKN,OAAO,OAENn8B,OAAOq8B,GAAQA,GACf/W,KAAK,CAACC,EAAGC,IAAMD,EAAE,GAAKC,EAAE,IACxB3hB,QAAQw4B,IACPryB,KAAKwxB,SAAS/6B,KAAK47B,EAAK,IACxBryB,KAAKyxB,SAASh7B,KAAK47B,EAAK,MAI9BvvB,UACExC,EAAaC,IAAIP,KAAKuxB,eAhHP,iBAiHfzqB,MAAMhE,UAKR0E,WAAW/N,GAOT,GAA6B,iBAN7BA,EAAS,IACJwM,MACApB,EAAYI,kBAAkBjF,KAAK4C,aAChB,iBAAXnJ,GAAuBA,EAASA,EAAS,KAGpC4G,QAAuB3H,EAAUe,EAAO4G,QAAS,CACjE,IAAIgM,GAAEA,GAAO5S,EAAO4G,OACfgM,IACHA,EAAKtV,EAlIA,aAmIL0C,EAAO4G,OAAOgM,GAAKA,GAGrB5S,EAAO4G,OAAU,IAAGgM,EAKtB,OAFA9S,EAzIS,YAyIaE,EAAQ+M,IAEvB/M,EAGTw4B,gBACE,OAAOjyB,KAAKuxB,iBAAmBt5B,OAC7B+H,KAAKuxB,eAAeta,YACpBjX,KAAKuxB,eAAe5rB,UAGxBusB,mBACE,OAAOlyB,KAAKuxB,eAAe9Y,cAAgBxhB,KAAKib,IAC9C5c,SAASwG,KAAK2c,aACdnjB,SAASC,gBAAgBkjB,cAI7B6Z,mBACE,OAAOtyB,KAAKuxB,iBAAmBt5B,OAC7BA,OAAOs6B,YACPvyB,KAAKuxB,eAAe9rB,wBAAwB+K,OAGhDohB,WACE,MAAMjsB,EAAY3F,KAAKiyB,gBAAkBjyB,KAAKuH,QAAQhC,OAChDkT,EAAezY,KAAKkyB,mBACpBM,EAAYxyB,KAAKuH,QAAQhC,OAASkT,EAAezY,KAAKsyB,mBAM5D,GAJItyB,KAAK2xB,gBAAkBlZ,GACzBzY,KAAK6xB,UAGHlsB,GAAa6sB,EAAjB,CACE,MAAMnyB,EAASL,KAAKyxB,SAASzxB,KAAKyxB,SAAS34B,OAAS,GAEhDkH,KAAK0xB,gBAAkBrxB,GACzBL,KAAKyyB,UAAUpyB,OAJnB,CAUA,GAAIL,KAAK0xB,eAAiB/rB,EAAY3F,KAAKwxB,SAAS,IAAMxxB,KAAKwxB,SAAS,GAAK,EAG3E,OAFAxxB,KAAK0xB,cAAgB,UACrB1xB,KAAK0yB,SAIP,IAAK,IAAIzzB,EAAIe,KAAKwxB,SAAS14B,OAAQmG,KACVe,KAAK0xB,gBAAkB1xB,KAAKyxB,SAASxyB,IACxD0G,GAAa3F,KAAKwxB,SAASvyB,UACM,IAAzBe,KAAKwxB,SAASvyB,EAAI,IAAsB0G,EAAY3F,KAAKwxB,SAASvyB,EAAI,KAGhFe,KAAKyyB,UAAUzyB,KAAKyxB,SAASxyB,KAKnCwzB,UAAUpyB,GACRL,KAAK0xB,cAAgBrxB,EAErBL,KAAK0yB,SAEL,MAAMC,EAAU3yB,KAAK0M,UAAUhV,MAAM,KAClCwqB,IAAI9sB,GAAa,GAAEA,qBAA4BiL,OAAYjL,WAAkBiL,OAE1EuyB,EAAO19B,EAAeW,QAAQ88B,EAAQE,KAAK,MAE7CD,EAAK53B,UAAUC,SA1LU,kBA2L3B/F,EAAeW,QAlLY,mBAkLsB+8B,EAAK9uB,QAnLlC,cAoLjB9I,UAAU6O,IA3LO,UA6LpB+oB,EAAK53B,UAAU6O,IA7LK,YAgMpB+oB,EAAK53B,UAAU6O,IAhMK,UAkMpB3U,EAAeiB,QAAQy8B,EA/LG,qBAgMvB/4B,QAAQi5B,IAGP59B,EAAewB,KAAKo8B,EAAY,+BAC7Bj5B,QAAQw4B,GAAQA,EAAKr3B,UAAU6O,IAvMlB,WA0MhB3U,EAAewB,KAAKo8B,EArMH,aAsMdj5B,QAAQk5B,IACP79B,EAAea,SAASg9B,EAxMX,aAyMVl5B,QAAQw4B,GAAQA,EAAKr3B,UAAU6O,IA7MtB,gBAkNtBvJ,EAAamB,QAAQzB,KAAKuxB,eAvNN,wBAuNsC,CACxDzxB,cAAeO,IAInBqyB,SACEx9B,EAAeC,KAAK6K,KAAK0M,WACtB1W,OAAO4Y,GAAQA,EAAK5T,UAAUC,SAzNX,WA0NnBpB,QAAQ+U,GAAQA,EAAK5T,UAAU4C,OA1NZ,WA+NFwF,uBAAC3J,GACrB,OAAOuG,KAAKiE,MAAK,WACf,MAAMC,EAAOotB,GAAUlL,YAAYpmB,OAAS,IAAIsxB,GAAUtxB,KAAwB,iBAAXvG,EAAsBA,EAAS,IAEtG,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjByK,EAAKzK,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CyK,EAAKzK,UAWX6G,EAAaQ,GAAG7I,OAzPa,6BAyPgB,KAC3C/C,EAAeC,KArPS,0BAsPrB0E,QAAQm5B,GAAO,IAAI1B,GAAU0B,MAUlC/2B,EAAmBq1B,IC3PnB,MAAM2B,WAAYvwB,EAGDpG,kBACb,MAlCS,MAuCXyQ,OACE,GAAK/M,KAAK4C,SAASvM,YACjB2J,KAAK4C,SAASvM,WAAWC,WAAaC,KAAKC,cAC3CwJ,KAAK4C,SAAS5H,UAAUC,SA9BJ,UA+BpB,OAGF,IAAItE,EACJ,MAAM0J,EAASxI,EAAuBmI,KAAK4C,UACrCswB,EAAclzB,KAAK4C,SAASkB,QA/BN,qBAiC5B,GAAIovB,EAAa,CACf,MAAMC,EAAwC,OAAzBD,EAAYxkB,UAA8C,OAAzBwkB,EAAYxkB,SAhC7C,wBADH,UAkClB/X,EAAWzB,EAAeC,KAAKg+B,EAAcD,GAC7Cv8B,EAAWA,EAASA,EAASmC,OAAS,GAGxC,MAAMs6B,EAAYz8B,EAChB2J,EAAamB,QAAQ9K,EApDP,cAoD6B,CACzCmJ,cAAeE,KAAK4C,WAEtB,KAMF,GAJkBtC,EAAamB,QAAQzB,KAAK4C,SAvD5B,cAuDkD,CAChE9C,cAAenJ,IAGHoL,kBAAmC,OAAdqxB,GAAsBA,EAAUrxB,iBACjE,OAGF/B,KAAKyyB,UAAUzyB,KAAK4C,SAAUswB,GAE9B,MAAMG,EAAW,KACf/yB,EAAamB,QAAQ9K,EAnEL,gBAmE6B,CAC3CmJ,cAAeE,KAAK4C,WAEtBtC,EAAamB,QAAQzB,KAAK4C,SApEX,eAoEkC,CAC/C9C,cAAenJ,KAIf0J,EACFL,KAAKyyB,UAAUpyB,EAAQA,EAAOhK,WAAYg9B,GAE1CA,IAMJZ,UAAUp9B,EAAS6X,EAAW/Q,GAC5B,MAIMm3B,IAJiBpmB,GAAqC,OAAvBA,EAAUwB,UAA4C,OAAvBxB,EAAUwB,SAE5ExZ,EAAea,SAASmX,EA3EN,WA0ElBhY,EAAeC,KAzEM,wBAyEmB+X,IAGZ,GACxBS,EAAkBxR,GAAam3B,GAAUA,EAAOt4B,UAAUC,SAnF5C,QAqFdo4B,EAAW,IAAMrzB,KAAKuzB,oBAAoBl+B,EAASi+B,EAAQn3B,GAE7Dm3B,GAAU3lB,GACZ2lB,EAAOt4B,UAAU4C,OAvFC,QAwFlBoC,KAAKkD,eAAemwB,EAAUh+B,GAAS,IAEvCg+B,IAIJE,oBAAoBl+B,EAASi+B,EAAQn3B,GACnC,GAAIm3B,EAAQ,CACVA,EAAOt4B,UAAU4C,OAlGG,UAoGpB,MAAM41B,EAAgBt+B,EAAeW,QA1FJ,kCA0F4Cy9B,EAAOj9B,YAEhFm9B,GACFA,EAAcx4B,UAAU4C,OAvGN,UA0GgB,QAAhC01B,EAAOh8B,aAAa,SACtBg8B,EAAO/uB,aAAa,iBAAiB,GAIzClP,EAAQ2F,UAAU6O,IA/GI,UAgHe,QAAjCxU,EAAQiC,aAAa,SACvBjC,EAAQkP,aAAa,iBAAiB,GAGxC7I,EAAOrG,GAEHA,EAAQ2F,UAAUC,SArHF,SAsHlB5F,EAAQ2F,UAAU6O,IArHA,QAwHpB,IAAIoC,EAAS5W,EAAQgB,WAKrB,GAJI4V,GAA8B,OAApBA,EAAOyC,WACnBzC,EAASA,EAAO5V,YAGd4V,GAAUA,EAAOjR,UAAUC,SAhIF,iBAgIsC,CACjE,MAAMw4B,EAAkBp+B,EAAQyO,QA5HZ,aA8HhB2vB,GACFv+B,EAAeC,KA1HU,mBA0HqBs+B,GAC3C55B,QAAQ65B,GAAYA,EAAS14B,UAAU6O,IApIxB,WAuIpBxU,EAAQkP,aAAa,iBAAiB,GAGpCpI,GACFA,IAMkBiH,uBAAC3J,GACrB,OAAOuG,KAAKiE,MAAK,WACf,MAAMC,EAAOlH,EAAKM,IAAI0C,KA9JX,WA8J8B,IAAIizB,GAAIjzB,MAEjD,GAAsB,iBAAXvG,EAAqB,CAC9B,QAA4B,IAAjByK,EAAKzK,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CyK,EAAKzK,UAYb6G,EAAaQ,GAAGxL,SAzKc,wBAWD,4EA8JyC,SAAU6J,GAC1E,CAAC,IAAK,QAAQ3H,SAASwI,KAAK8J,UAC9B3K,EAAMsD,iBAGJ1H,EAAWiF,QAIFhD,EAAKM,IAAI0C,KA1LP,WA0L0B,IAAIizB,GAAIjzB,OAC5C+M,UAUP9Q,EAAmBg3B,ICvMnB,MAmBMzsB,GAAc,CAClB+kB,UAAW,UACXoI,SAAU,UACVjI,MAAO,UAGHzlB,GAAU,CACdslB,WAAW,EACXoI,UAAU,EACVjI,MAAO,KAWT,MAAMkI,WAAclxB,EAClBC,YAAYtN,EAASoE,GACnBqN,MAAMzR,GAEN2K,KAAKuH,QAAUvH,KAAKwH,WAAW/N,GAC/BuG,KAAK0uB,SAAW,KAChB1uB,KAAK6zB,sBAAuB,EAC5B7zB,KAAK8zB,yBAA0B,EAC/B9zB,KAAK8uB,gBAKetoB,yBACpB,OAAOA,GAGSP,qBAChB,OAAOA,GAGM3J,kBACb,MA7DS,QAkEXyQ,OACoBzM,EAAamB,QAAQzB,KAAK4C,SAxD5B,iBA0DFb,mBAId/B,KAAK+zB,gBAED/zB,KAAKuH,QAAQgkB,WACfvrB,KAAK4C,SAAS5H,UAAU6O,IA9DN,QA0EpB7J,KAAK4C,SAAS5H,UAAU4C,OAzEJ,QA0EpBlC,EAAOsE,KAAK4C,UACZ5C,KAAK4C,SAAS5H,UAAU6O,IAzED,WA2EvB7J,KAAKkD,eAbY,KACflD,KAAK4C,SAAS5H,UAAU4C,OA/DH,WAgErBoC,KAAK4C,SAAS5H,UAAU6O,IAjEN,QAmElBvJ,EAAamB,QAAQzB,KAAK4C,SAvEX,kBAyEf5C,KAAKg0B,sBAOuBh0B,KAAK4C,SAAU5C,KAAKuH,QAAQgkB,YAG5Dze,OACO9M,KAAK4C,SAAS5H,UAAUC,SAhFT,UAoFFqF,EAAamB,QAAQzB,KAAK4C,SA3F5B,iBA6FFb,mBASd/B,KAAK4C,SAAS5H,UAAU4C,OA/FJ,QAgGpBoC,KAAKkD,eANY,KACflD,KAAK4C,SAAS5H,UAAU6O,IA5FN,QA6FlBvJ,EAAamB,QAAQzB,KAAK4C,SAlGV,oBAsGY5C,KAAK4C,SAAU5C,KAAKuH,QAAQgkB,aAG5DzoB,UACE9C,KAAK+zB,gBAED/zB,KAAK4C,SAAS5H,UAAUC,SAtGR,SAuGlB+E,KAAK4C,SAAS5H,UAAU4C,OAvGN,QA0GpBkJ,MAAMhE,UAKR0E,WAAW/N,GAST,OARAA,EAAS,IACJwM,MACApB,EAAYI,kBAAkBjF,KAAK4C,aAChB,iBAAXnJ,GAAuBA,EAASA,EAAS,IAGtDF,EAtIS,QAsIaE,EAAQuG,KAAK2C,YAAY6D,aAExC/M,EAGTu6B,qBACOh0B,KAAKuH,QAAQosB,WAId3zB,KAAK6zB,sBAAwB7zB,KAAK8zB,0BAItC9zB,KAAK0uB,SAAWp1B,WAAW,KACzB0G,KAAK8M,QACJ9M,KAAKuH,QAAQmkB,SAGlBuI,eAAe90B,EAAO+0B,GACpB,OAAQ/0B,EAAMqB,MACZ,IAAK,YACL,IAAK,WACHR,KAAK6zB,qBAAuBK,EAC5B,MACF,IAAK,UACL,IAAK,WACHl0B,KAAK8zB,wBAA0BI,EAMnC,GAAIA,EAEF,YADAl0B,KAAK+zB,gBAIP,MAAM5oB,EAAchM,EAAMW,cACtBE,KAAK4C,WAAauI,GAAenL,KAAK4C,SAAS3H,SAASkQ,IAI5DnL,KAAKg0B,qBAGPlF,gBACExuB,EAAaQ,GAAGd,KAAK4C,SAjLI,yBA2BC,4BAsJiD,IAAM5C,KAAK8M,QACtFxM,EAAaQ,GAAGd,KAAK4C,SAjLA,qBAiL2BzD,GAASa,KAAKi0B,eAAe90B,GAAO,IACpFmB,EAAaQ,GAAGd,KAAK4C,SAjLD,oBAiL2BzD,GAASa,KAAKi0B,eAAe90B,GAAO,IACnFmB,EAAaQ,GAAGd,KAAK4C,SAjLF,mBAiL2BzD,GAASa,KAAKi0B,eAAe90B,GAAO,IAClFmB,EAAaQ,GAAGd,KAAK4C,SAjLD,oBAiL2BzD,GAASa,KAAKi0B,eAAe90B,GAAO,IAGrF40B,gBACErqB,aAAa1J,KAAK0uB,UAClB1uB,KAAK0uB,SAAW,KAKItrB,uBAAC3J,GACrB,OAAOuG,KAAKiE,MAAK,WACf,IAAIC,EAAOlH,EAAKM,IAAI0C,KApMT,YA2MX,GAJKkE,IACHA,EAAO,IAAI0vB,GAAM5zB,KAHe,iBAAXvG,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjByK,EAAKzK,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CyK,EAAKzK,GAAQuG,kBAarB/D,EAAmB23B,ICjOJ,CACbrwB,MAAAA,EACAc,OAAAA,EACAwC,SAAAA,EACAqF,SAAAA,EACAmY,SAAAA,GACAwD,MAAAA,GACA6B,UAAAA,GACAyH,QAAAA,GACAG,UAAAA,GACA2B,IAAAA,GACAW,MAAAA,GACApF,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst customEventsRegex = /^(mouseenter|mouseleave)/i\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            // eslint-disable-next-line unicorn/consistent-destructuring\n            EventHandler.off(element, event.type, selector, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  let typeEvent = getTypeEvent(originalTypeEvent)\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (customEventsRegex.test(originalTypeEvent)) {\n    const wrapFn = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    if (delegationFn) {\n      delegationFn = wrapFn(delegationFn)\n    } else {\n      handler = wrapFn(handler)\n    }\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport {\n  emulateTransitionEnd,\n  execute,\n  getElement,\n  getTransitionDurationFromElement\n} from './util/index'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.0.1'\n\nclass BaseComponent {\n  constructor(element) {\n    element = getElement(element)\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    Object.getOwnPropertyNames(this).forEach(propertyName => {\n      this[propertyName] = null\n    })\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    if (!isAnimated) {\n      execute(callback)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n    EventHandler.one(element, 'transitionend', () => execute(callback))\n\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(element, this.DATA_KEY)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-bs-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_ALERT = 'alert'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASS_NAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(element), element, isAnimated)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.get(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toL<PERSON>er<PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_INDICATOR = '[data-bs-target]'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(ORDER_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(ORDER_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const order = index > activeIndex ?\n      ORDER_NEXT :\n      ORDER_PREV\n\n    this._slide(order, this._items[index])\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    this._slide(direction > 0 ? DIRECTION_RIGHT : DIRECTION_LEFT)\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.touches && event.touches.length > 1 ?\n        0 :\n        event.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    if (event.key === ARROW_LEFT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_RIGHT)\n    } else if (event.key === ARROW_RIGHT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_LEFT)\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByOrder(order, activeElement) {\n    const isNext = order === ORDER_NEXT\n    const isPrev = order === ORDER_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrev && activeIndex === 0) || (isNext && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = isPrev ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n      activeIndicator.removeAttribute('aria-current')\n\n      const indicators = SelectorEngine.find(SELECTOR_INDICATOR, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        if (Number.parseInt(indicators[i].getAttribute('data-bs-slide-to'), 10) === this._getItemIndex(element)) {\n          indicators[i].classList.add(CLASS_NAME_ACTIVE)\n          indicators[i].setAttribute('aria-current', 'true')\n          break\n        }\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(directionOrOrder, element) {\n    const order = this._directionToOrder(directionOrOrder)\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || this._getItemByOrder(order, activeElement)\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    const isNext = order === ORDER_NEXT\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n    const eventDirectionName = this._orderToDirection(order)\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    const triggerSlidEvent = () => {\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const completeCallBack = () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(triggerSlidEvent, 0)\n      }\n\n      this._queueCallback(completeCallBack, activeElement, true)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      triggerSlidEvent()\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (![DIRECTION_RIGHT, DIRECTION_LEFT].includes(direction)) {\n      return direction\n    }\n\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (![ORDER_NEXT, ORDER_PREV].includes(order)) {\n      return order\n    }\n\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.get(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.get(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getSelectorFromElement,\n  getElementFromSelector,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${this._element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${this._element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Data.get(tempActiveData, DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.set(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    parent = getElement(parent)\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.get(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export default function getBoundingClientRect(element) {\n  var rect = element.getBoundingClientRect();\n  return {\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    right: rect.right,\n    bottom: rect.bottom,\n    left: rect.left,\n    x: rect.left,\n    y: rect.top\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') !== -1;\n  var isIE = navigator.userAgent.indexOf('Trident') !== -1;\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport default function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport within from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!isHTMLElement(arrowElement)) {\n      console.error(['Popper: \"arrow\" element must be an HTMLElement (not an SVGElement).', 'To use an SVG arrow, wrap it in an HTMLElement that will be used as', 'the arrow.'].join(' '));\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: \"arrow\" modifier\\'s `element` must be a child of the popper', 'element.'].join(' '));\n    }\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "import { top, left, right, bottom } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(round(x * dpr) / dpr) || 0,\n    y: round(round(y * dpr) / dpr) || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets;\n\n  var _ref3 = roundOffsets === true ? roundOffsetsByDPR(offsets) : typeof roundOffsets === 'function' ? roundOffsets(offsets) : offsets,\n      _ref3$x = _ref3.x,\n      x = _ref3$x === void 0 ? 0 : _ref3$x,\n      _ref3$y = _ref3.y,\n      y = _ref3$y === void 0 ? 0 : _ref3$y;\n\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top) {\n      sideY = bottom; // $FlowFixMe[prop-missing]\n\n      y -= offsetParent[heightProp] - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left) {\n      sideX = right; // $FlowFixMe[prop-missing]\n\n      x -= offsetParent[widthProp] - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) < 2 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref4) {\n  var state = _ref4.state,\n      options = _ref4.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element) {\n  var rect = getBoundingClientRect(element);\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element)) : isHTMLElement(clippingParent) ? getInnerBoundingClientRect(clippingParent) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nexport default function getViewportRect(element) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0; // NB: This isn't supported on iOS <= 12. If the keyboard is open, the popper\n  // can be obscured underneath it.\n  // Also, `html.clientHeight` adds the bottom bar height in Safari iOS, even\n  // if it isn't open, so if this isn't available, the popper will be detected\n  // to overflow the bottom of the screen too early.\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height; // Uses Layout Viewport (like Chrome; Safari does not currently)\n    // In Chrome, it returns a value very close to 0 (+/-) but contains rounding\n    // errors due to floating point numbers, so we need to check precision.\n    // Safari returns a number <= 0, usually < -1 when pinch-zoomed\n    // Feature detection fails in mobile emulation mode in Chrome.\n    // Math.abs(win.innerWidth / visualViewport.scale - visualViewport.width) <\n    // 0.001\n    // Fallback here: \"Not Safari\" userAgent\n\n    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var referenceElement = state.elements.reference;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary);\n  var referenceClientRect = getBoundingClientRect(referenceElement);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\";\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport within from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { max as mathMax, min as mathMin } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis || checkAltAxis) {\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = popperOffsets[mainAxis] + overflow[mainSide];\n    var max = popperOffsets[mainAxis] - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - tetherOffsetValue : minLen - arrowLen - arrowPaddingMin - tetherOffsetValue;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + tetherOffsetValue : maxLen + arrowLen + arrowPaddingMax + tetherOffsetValue;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = state.modifiersData.offset ? state.modifiersData.offset[state.placement][mainAxis] : 0;\n    var tetherMin = popperOffsets[mainAxis] + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = popperOffsets[mainAxis] + maxOffset - offsetModifierValue;\n\n    if (checkMainAxis) {\n      var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n      popperOffsets[mainAxis] = preventedOffset;\n      data[mainAxis] = preventedOffset - offset;\n    }\n\n    if (checkAltAxis) {\n      var _mainSide = mainAxis === 'x' ? top : left;\n\n      var _altSide = mainAxis === 'x' ? bottom : right;\n\n      var _offset = popperOffsets[altAxis];\n\n      var _min = _offset + overflow[_mainSide];\n\n      var _max = _offset - overflow[_altSide];\n\n      var _preventedOffset = within(tether ? mathMin(_min, tetherMin) : _min, _offset, tether ? mathMax(_max, tetherMax) : _max);\n\n      popperOffsets[altAxis] = _preventedOffset;\n      data[altAxis] = _preventedOffset - _offset;\n    }\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\"; // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement);\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(options) {\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  isDisabled,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null,\n  autoClose: true\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)',\n  autoClose: '(boolean|string)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (isDisabled(this._element)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    if (isActive) {\n      this.hide()\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = getElement(this._config.reference)\n      } else if (typeof this._config.reference === 'object') {\n        referenceElement = this._config.reference\n      }\n\n      const popperConfig = this._getPopperConfig()\n      const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n      if (isDisplayStatic) {\n        Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n      }\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', noop))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      this.toggle()\n    })\n  }\n\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.off(elem, 'mouseover', noop))\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem(event) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    // Up\n    if (event.key === ARROW_UP_KEY && index > 0) {\n      index--\n    }\n\n    // Down\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) {\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Data.get(toggles[i], DATA_KEY)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      if (!context._element.classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      const relatedTarget = {\n        relatedTarget: context._element\n      }\n\n      if (event) {\n        const composedPath = event.composedPath()\n        const isMenuTarget = composedPath.includes(context._menu)\n        if (\n          composedPath.includes(context._element) ||\n          (context._config.autoClose === 'inside' && !isMenuTarget) ||\n          (context._config.autoClose === 'outside' && isMenuTarget)\n        ) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n        if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n          continue\n        }\n\n        if (event.type === 'click') {\n          relatedTarget.clickEvent = event\n        }\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (!isActive && event.key === ESCAPE_KEY) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const getToggleButton = () => this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n\n    if (event.key === ESCAPE_KEY) {\n      getToggleButton().focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive && (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY)) {\n      getToggleButton().click()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    Dropdown.getInstance(getToggleButton())._selectMenuItem(event)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.dropdownInterface(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nconst getWidth = () => {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = document.documentElement.clientWidth\n  return Math.abs(window.innerWidth - documentWidth)\n}\n\nconst hide = (width = getWidth()) => {\n  _disableOverFlow()\n  // give padding to element to balances the hidden scrollbar width\n  _setElementAttributes('body', 'paddingRight', calculatedValue => calculatedValue + width)\n  // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements, to keep shown fullwidth\n  _setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n  _setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n}\n\nconst _disableOverFlow = () => {\n  const actualValue = document.body.style.overflow\n  if (actualValue) {\n    Manipulator.setDataAttribute(document.body, 'overflow', actualValue)\n  }\n\n  document.body.style.overflow = 'hidden'\n}\n\nconst _setElementAttributes = (selector, styleProp, callback) => {\n  const scrollbarWidth = getWidth()\n  SelectorEngine.find(selector)\n    .forEach(element => {\n      if (element !== document.body && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      const actualValue = element.style[styleProp]\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n      element.style[styleProp] = `${callback(Number.parseFloat(calculatedValue))}px`\n    })\n}\n\nconst reset = () => {\n  _resetElementAttributes('body', 'overflow')\n  _resetElementAttributes('body', 'paddingRight')\n  _resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n  _resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n}\n\nconst _resetElementAttributes = (selector, styleProp) => {\n  SelectorEngine.find(selector).forEach(element => {\n    const value = Manipulator.getDataAttribute(element, styleProp)\n    if (typeof value === 'undefined') {\n      element.style.removeProperty(styleProp)\n    } else {\n      Manipulator.removeDataAttribute(element, styleProp)\n      element.style[styleProp] = value\n    }\n  })\n}\n\nconst isBodyOverflowing = () => {\n  return getWidth() > 0\n}\n\nexport {\n  getWidth,\n  hide,\n  isBodyOverflowing,\n  reset\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { emulateTransitionEnd, execute, getTransitionDurationFromElement, reflow, typeCheckConfig } from './index'\n\nconst Default = {\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: document.body, // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: 'element',\n  clickCallback: '(function|null)'\n}\nconst NAME = 'backdrop'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nclass Backdrop {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    if (this._config.isAnimated) {\n      reflow(this._getElement())\n    }\n\n    this._getElement().classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  // Private\n\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = CLASS_NAME_BACKDROP\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n\n    config.rootElement = config.rootElement || document.body\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    this._config.rootElement.appendChild(this._getElement())\n\n    EventHandler.on(this._getElement(), EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._getElement().parentNode.removeChild(this._element)\n    this._isAppended = false\n  }\n\n  _emulateAnimation(callback) {\n    if (!this._config.isAnimated) {\n      execute(callback)\n      return\n    }\n\n    const backdropTransitionDuration = getTransitionDurationFromElement(this._getElement())\n    EventHandler.one(this._getElement(), 'transitionend', () => execute(callback))\n    emulateTransitionEnd(this._getElement(), backdropTransitionDuration)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isRTL,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport { getWidth as getScroll<PERSON><PERSON><PERSON>idth, hide as scrollBarHide, reset as scrollBarReset } from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._isShown = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    scrollBarHide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    this._queueCallback(() => this._hideModal(), this._element, isAnimated)\n  }\n\n  dispose() {\n    [window, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    this._backdrop.dispose()\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, isAnimated)\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      scrollBarReset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _showBackdrop(callback) {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n      if (this._ignoreBackdropClick) {\n        this._ignoreBackdropClick = false\n        return\n      }\n\n      if (event.target !== event.currentTarget) {\n        return\n      }\n\n      if (this._config.backdrop === true) {\n        this.hide()\n      } else if (this._config.backdrop === 'static') {\n        this._triggerBackdropTransition()\n      }\n    })\n\n    this._backdrop.show(callback)\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n    EventHandler.off(this._element, 'transitionend')\n    EventHandler.one(this._element, 'transitionend', () => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        EventHandler.one(this._element, 'transitionend', () => {\n          this._element.style.overflowY = ''\n        })\n        emulateTransitionEnd(this._element, modalTransitionDuration)\n      }\n    })\n    emulateTransitionEnd(this._element, modalTransitionDuration)\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = getScrollBarWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if ((!isBodyOverflowing && isModalOverflowing && !isRTL()) || (isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${scrollbarWidth}px`\n    }\n\n    if ((isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getInstance(this) || new Modal(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  const data = Modal.getInstance(target) || new Modal(target)\n\n  data.toggle(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport { hide as scrollBarHide, reset as scrollBarReset } from './util/scrollbar'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\nimport Backdrop from './util/backdrop'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_SHOW = 'show'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"offcanvas\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      scrollBarHide()\n      this._enforceFocusOnElement(this._element)\n    }\n\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (!this._config.scroll) {\n        scrollBarReset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    super.dispose()\n    EventHandler.off(document, EVENT_FOCUSIN)\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: this._config.backdrop,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: () => this.hide()\n    })\n  }\n\n  _enforceFocusOnElement(element) {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n        element !== event.target &&\n        !element.contains(event.target)) {\n        element.focus()\n      }\n    })\n    element.focus()\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Offcanvas(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    Offcanvas.getInstance(allReadyOpen).hide()\n  }\n\n  const data = Data.get(target, DATA_KEY) || new Offcanvas(target)\n\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => (Data.get(el, DATA_KEY) || new Offcanvas(el)).show())\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  findShadowRoot,\n  getElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this._config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip && this.tip.parentNode) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    this.setContent()\n\n    if (this._config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const { container } = this._config\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.appendChild(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = typeof this._config.customClass === 'function' ? this._config.customClass() : this._config.customClass\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop)\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this._config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (isElement(content)) {\n      content = getElement(content)\n\n      // content is a DOM node or a jQuery\n      if (this._config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this._config.html) {\n      if (this._config.sanitize) {\n        content = sanitizeHtml(content, this._config.allowList, this._config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this._config.title === 'function' ?\n        this._config.title.call(this._element) :\n        this._config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.get(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n      Data.set(event.delegateTarget, dataKey, context)\n    }\n\n    return context\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this._config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this._config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this._config.selector) {\n      this._config = {\n        ...this._config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context._config.delay || !context._config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context._config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context._config.delay || !context._config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context._config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this._config) {\n      for (const key in this._config) {\n        if (this.constructor.Default[key] !== this._config[key]) {\n          config[key] = this._config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n                '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this._config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.set(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getInstance(this) || new ScrollSpy(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE))) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      active.classList.remove(CLASS_NAME_SHOW)\n      this._queueCallback(complete, element, true)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    let parent = element.parentNode\n    if (parent && parent.nodeName === 'LI') {\n      parent = parent.parentNode\n    }\n\n    if (parent && parent.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE, dropdownElement)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  const data = Data.get(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        this._hasMouseInteraction = isInteracting\n        break\n      case 'focusin':\n      case 'focusout':\n        this._hasKeyboardInteraction = isInteracting\n        break\n      default:\n        break\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.1): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Offcanvas from './src/offcanvas'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}