﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.file;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class espaceforum : System.Web.UI.Page
    {

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();

        ISujetForum objpost = new SujetForumImp();
        SujetForum_Class post = new SujetForum_Class();
        SujetForum_Class pos = new SujetForum_Class();

        IDomainePost objdompost = new DomainePostImp();
        DomainePost_Class dompost = new DomainePost_Class();

        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();

        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();

        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();

        ICommonCode co = new CommonCode();
        //   UrlPartage = $"{Request.Url.GetLeftPart(UriPartial.Authority)}/post/" + ep.name,
        IDomaineInterventionOrganisation objdomorg = new DomaineInterventionOrganisationImp();
        DomaineInterventionOrganisation_Class domorg = new DomaineInterventionOrganisation_Class();

        INotification objnotif = new NotificationImp();
        Notification_Class notif=new Notification_Class();

        static string imge, imge1, pdfe, nameorg;
         long ide; static long idorg;
        static int rolid; int info;
        long index;

        protected void Page_Load(object sender, EventArgs e)
        {
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin
                long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte

            }
            if (!IsPostBack)
            {
              
                affich();

            }
        }

        void EnvoieQuestion()
        {
            try
            {
                if (ide > 0)
                {
                    if (txttitre.Value == "" || txtquestion.Value == "")
                    {
                        Response.Write("<script LANGUAGE=JavaScript>alert('Veuillez renseigner le sujet et la question.!!!')</script>");

                    }
                    else
                    {
                        if(objpost.count(1,-1,"", co.GenerateSlug(txttitre.Value)) > 0)
                        {
                            string url = $"{HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority)}/forums.aspx?name=" + co.GenerateSlug(txttitre.Value);

                            string script = $@"
    <script LANGUAGE='JavaScript'>
        alert('Cette question a déjà été posée, vous allez être redirigé vers le post.');
        window.location.href = '{url}';
    </script>";

                            Response.Write(script);

                            return;
                        }
                        pos.Titre = txttitre.Value;
                        pos.Contenu = txtquestion.Value;
                        pos.MembreId = ide;
                        pos.DateCreation = DateTime.Now;
                        pos.NombreVues = 0;
                        pos.Statut = "ouvert";
                        pos.etat = "";
                        pos.name = co.GenerateSlug(txttitre.Value);

                        info = objpost.Ajouter(pos);
                        if (info == 1)
                        {
                            objpost.ChargerListview(listforum, -1, -1, "", "ouvert", "", 0);
                            EnvoieNotification();
                            Response.Write("<script LANGUAGE=JavaScript>alert('Votre question a été bien envoyé.!!!')</script>");

                        }
                        else
                        {
                            Response.Write("<script LANGUAGE=JavaScript>alert('Erreur.!!!')</script>");

                        }
                    }
                }
                else Response.Redirect("~/login.aspx");
            }
            catch (Exception ex)
            {

            }
        }
        void EnvoieNotification()
        {
            // Après avoir sauvé la nouvelle question dans la base
            objmem.AfficherDetails(ide, mem);
            long auteur = ide;
            string titreSujet = txttitre.Value;
            string contenu = txtquestion.Value;
            string name = "Forum: " + mem.Nom + " " + mem.Prenom + " a posé une question";
           
            string statut = "envoyé";
            string liensujet = $"{HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority)}/forums.aspx?name="+co.GenerateSlug(txttitre.Value);

            Task.Run(() => objnotif.EnvoyerNotificationAction(auteur, titreSujet, contenu,liensujet,name, statut));


        }
        protected void btnenreg_ServerClick(object sender, EventArgs e)
        {
            if (ide>0)
            {
                EnvoieQuestion();
              
            }
            else Response.Redirect("~/login.aspx");

        }

        protected void btnrecent_ServerClick(object sender, EventArgs e)
        {
            objpost.ChargerListview(listforum, -1, -1, "", "ouvert","", 0);
        }

        protected void btnnonrepondu_ServerClick(object sender, EventArgs e)
        {
            objpost.ChargerListview(listforum, -1, -1, "", "ouvert", "reponses", 1);
        }

        protected void btnvu_ServerClick(object sender, EventArgs e)
        {
            objpost.ChargerListview(listforum, -1, -1, "", "ouvert", "vues", 1);

        }
        protected string TruncateText(object textObj, int maxLength)
        {
            if (textObj == null) return string.Empty;
            string text = textObj.ToString();
            return text.Length <= maxLength ? text : text.Substring(0, maxLength) + "...";
        }

        void affich()
        {
            objpost.ChargerListview(listforum,-1,-1,"","ouvert","",0);
           // objactdom.ChargerDomaines(listcategorie, "programmementorat");
            //objpost.Chargement_GDV(listprogrammentorat, -1, -1, "programmementorat", "fnuap", 1);

            // objc.Chargement_GDVL(listinst, 2);
        }
       
        public string GetRelativeDate(DateTime date)
        {
            return co.GetRelativeDate(date);
        }


        protected void listforum_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "view")
            {
                objpost.AfficherDetails(-1,-1,index,"ouvert",1, pos);
                post.NombreVues = pos.NombreVues + 1;
                objpost.Modifier(post,pos.SujetForumId,1);

                Response.Redirect("~/forums.aspx?name=" + index);



            }
        }

        protected void listcategorie_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            //long index = Convert.ToInt64(e.CommandArgument);
            //if (e.CommandName == "viewdom")
            //{
            //    objpost.Chargement_GDV(listprogrammentorat, index, -1, "programmementorat", "publié", 2);



            //}
        }

       
    }
}