﻿using LinCom.Class;
using LinCom.file;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class ActiviteProjetImp : IActiviteProjet
    {
        private ActiviteProjet actproj = new ActiviteProjet();
        int msg;

        public void AfficherDetails(long financementId, long idorg,string name, int cd, ActiviteProjet_Class actproj)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var f = con.ActiviteProjets.FirstOrDefault(x => x.ActiviteProjetId == financementId && x.OrganisationId == idorg);
                    if (f != null)
                    {
                        actproj.ActiviteProjetId = f.ActiviteProjetId;
                        actproj.PostId = f.PostId;
                        actproj.Titre = f.Titre;
                        actproj.Description = f.Description;
                        actproj.DateActivite = f.DateActivite;
                        actproj.MembreId = f.MembreId;
                        actproj.OrganisationId = f.OrganisationId;
                        actproj.photo1 = f.photo1;
                        actproj.photo2 = f.photo2;
                        actproj.statut = f.statut;
                        actproj.DateActiviteEnreg = f.DateActiviteEnreg;
                        actproj.name = f.name;
                        actproj.summery = f.summery;
                        actproj.number_of_view = f.number_of_view;
                        actproj.like = f.like;
                        actproj.dislike = f.dislike;
                        actproj.MOIS = f.MOIS;
                        actproj.ANNEE = f.ANNEE;
                        actproj.DateCreation = f.DateCreation;
                        actproj.etat = f.etat;






                    }
                }
              
            }
        }

        public int Ajouter(ActiviteProjet_Class f)
        {
            using (Connection con = new Connection())
            {
                actproj.PostId = f.PostId;
                actproj.Titre = f.Titre;
                actproj.Description = f.Description;
                actproj.DateActivite = f.DateActivite;
                actproj.MembreId = f.MembreId;
                actproj.OrganisationId = f.OrganisationId;
                actproj.photo1 = f.photo1;
                actproj.photo2 = f.photo2;
                actproj.statut = f.statut;
                actproj.DateActiviteEnreg = f.DateActiviteEnreg;
                actproj.name = f.name;
                actproj.summery = f.summery;
                actproj.number_of_view = f.number_of_view;
                actproj.like = f.like;
                actproj.dislike = f.dislike;
                actproj.MOIS = f.MOIS;
                actproj.ANNEE = f.ANNEE;
                actproj.DateCreation = f.DateCreation;
                actproj.etat = f.etat;

                try
                {
                    con.ActiviteProjets.Add(actproj);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }

        public void ChargerActiviteProjet(GridView gdv, long id, long idorg, string name, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var query = from f in con.ActiviteProjets
                                join po in con.Posts on f.PostId equals po.PostId
                                join ca in con.CategoriePosts on po.CategoriePostId equals ca.CategoriePostId
                                join o in con.Organisations on f.OrganisationId equals o.OrganisationId
                                join m in con.Membres on f.MembreId equals m.MembreId
                                where f.OrganisationId == idorg
                                select new
                                {
                                    id = f.ActiviteProjetId,
                                    PostId = f.PostId,
                                    Titre = f.Titre,
                                    Description = f.Description,
                                    DateActivite = f.DateActivite,
                                    MembreId = f.MembreId,
                                    OrganisationId = f.OrganisationId,
                                    photo1 = f.photo1,
                                    photo2 = f.photo2,
                                    statut = f.statut,
                                    DateActiviteEnreg = f.DateActiviteEnreg,
                                    name = f.name,
                                    summery = f.summery,
                                    number_of_view = f.number_of_view,
                                    like = f.like,
                                    dislike = f.dislike,
                                    MOIS = f.MOIS,
                                    ANNEE = f.ANNEE,
                                    DateCreation = f.DateCreation,
                                    etat = f.etat,
                                    Titreprojet = po.Titre,
                                }
                ;

                    gdv.DataSource = query.OrderByDescending(x => x.DateActivite).ToList();
                    gdv.DataBind();
                }


            }
        }

        public int Modifier(ActiviteProjet_Class f, long id, long idorg, string name, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var actproj = con.ActiviteProjets.FirstOrDefault(x => x.ActiviteProjetId == id && x.OrganisationId == idorg);
                    if (actproj != null)
                    {

                        actproj.PostId = f.PostId;
                        actproj.Titre = f.Titre;
                        actproj.Description = f.Description;
                        actproj.DateActivite = f.DateActivite;
                     
                        actproj.photo1 = f.photo1;
                        actproj.photo2 = f.photo2;
                        actproj.statut = f.statut;
                      
                        actproj.name = f.name;
                        actproj.summery = f.summery;
                      
                        actproj.MOIS = f.MOIS;
                        actproj.ANNEE = f.ANNEE;
                        actproj.etat = f.etat;
                        actproj.DateActiviteEnreg = f.DateActiviteEnreg;

                        try
                        {


                            if (con.SaveChanges() == 1)
                            {
                                con.ActiviteProjets.Add(actproj);
                                con.Entry(actproj).State = EntityState.Modified;
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return msg = 0;
                        }
                    }

                }

                return msg;
            }
        }

        public int Supprimer(long id,long idorg)
        {
            using (Connection con = new Connection())
            {
                var f = con.ActiviteProjets.FirstOrDefault(x => x.ActiviteProjetId == id && x.OrganisationId==idorg);
                if (f != null)
                {
                    con.ActiviteProjets.Remove(f);
                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

    }
}