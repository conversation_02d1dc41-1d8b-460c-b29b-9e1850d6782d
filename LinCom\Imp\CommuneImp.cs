﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{

    public class CommuneImp : ICommune
    {
        int msg;
        Commune p = new Commune();
        public int add(CommuneClass add)
        {

            using (Connection con = new Connection())
            {
                p.ProvinceId = add.ProvinceId;
                p.Nom = add.Nom;
                p.name = add.name;



                try
                {
                    con.Communes.Add(p);

                    if (con.SaveChanges() == 1)
                    {
                        con.Communes.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }

        public void afficherDetails(int code, CommuneClass pr)
        {
            using (Connection con = new Connection())
            {
                p = con.Communes.Where(x => x.CommuneId == code).FirstOrDefault();

                if (p != null)
                {
                    pr.CommuneId=p.CommuneId;
                    pr.Nom = p.Nom;
                    pr.ProvinceId = p.ProvinceId;
                    pr.name = p.name;

                }

            }
        }

        public void afficherDetails(string code, CommuneClass pr)
        {
            using (Connection con = new Connection())
            {
                p = con.Communes.Where(x => x.name == code).FirstOrDefault();

                if (p != null)
                {
                    pr.CommuneId = p.CommuneId;
                    pr.Nom = p.Nom;
                    pr.ProvinceId = p.ProvinceId;
                    pr.name = p.name;

                }

            }
        }

        public void Chargement_GDV(GridView GV_apv)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.Communes
                           join pr in con.Provinces on ep.ProvinceId equals pr.ProvinceId
                           select new
                           {


                               id = ep.CommuneId,
                               nom = ep.Nom,
                               provinceId = ep.ProvinceId,
                               name = ep.name,
                               nomp=pr.Nom


                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public void chargerCommune(DropDownList ddw)
        {
            ddw.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = con.Communes.ToList();

                if (obj != null && obj.Count() > 0)
                {
                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner la Commune";
                    ddw.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.CommuneId.ToString();
                        item.Text = data.Nom;
                        ddw.Items.Add(item);
                    }

                }

            }
        }

        public void chargerCommune(DropDownList ddw, int code)
        {
            ddw.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = con.Communes.Where(x => x.ProvinceId == code).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner la Commune";
                    ddw.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.CommuneId.ToString();
                        item.Text = data.Nom;
                        ddw.Items.Add(item);
                    }

                }
                else
                {
                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    ddw.Items.Add(item0);
                }

            }
        }

        public int count()
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                var b = (from l in con.Communes
                         select l).Count();
                n = b;
            }
            return n;
        }

        public int edit(CommuneClass cl, int id)
        {
            using (Connection con = new Connection())
            {
                p = con.Communes.Where(x => x.CommuneId == id).FirstOrDefault();

                try
                {
                    p.Nom = cl.Nom;
                    p.ProvinceId = cl.ProvinceId;
                    p.name = cl.name;


                    if (con.SaveChanges() == 1)
                    {
                        con.Communes.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }
                return msg;
            }
        }
        public int edit(CommuneClass cl, string id)
        {
            using (Connection con = new Connection())
            {
                p = con.Communes.Where(x => x.name == id).FirstOrDefault();

                try
                {
                    p.Nom = cl.Nom;
                    p.ProvinceId = cl.ProvinceId;
                    p.name = cl.name;


                    if (con.SaveChanges() == 1)
                    {
                        con.Communes.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }
                return msg;
            }
        }

        public void search(GridView GV_apv, string code)
        {
            using (Connection con = new Connection())
            {



                var obj = (from ep in con.Communes
                           join pr in con.Provinces on ep.ProvinceId equals pr.ProvinceId
                           where (ep.Nom.ToString().Contains(code) || ep.Nom.StartsWith(code) || pr.Nom.Contains(code)|| pr.Nom.StartsWith(code))
                           select new
                           {
                               idCom = ep.CommuneId,
                               nom = ep.Nom,
                               province = ep.ProvinceId,
                               name = ep.name,
                               nomp=pr.Nom

                           }).ToList();


                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public int supprimer(int id)
        {

            using (Connection con = new Connection())
            {

                p = con.Communes.Where(x => x.CommuneId == id).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.Communes.Attach(p);

                con.Communes.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }
    }
}