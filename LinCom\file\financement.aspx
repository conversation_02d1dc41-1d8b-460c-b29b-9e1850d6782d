﻿<%@ Page Title="Financement des Projets" ValidateRequest="false" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="financement.aspx.cs" Inherits="LinCom.file.financement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">

                                    <div class="breadcomb-ctn">
                                        <h2>Financements</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">

                                    <a data-toggle="tooltip" data-placement="left" href="listfinancement.aspx" title="Clique sur ce button pour visualiser la liste des Financements" class="btn">Liste des Financements</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcomb area End-->
    <!-- Form Element area Start-->
    <div class="form-element-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">

                        <div class="cmp-tb-hd bcs-hd">
                            <p style="color: red">Remplissez les champs obligatoires avec asterix</p>
                        </div>
                        <div class="alert-list">
                            <div class="alert alert-success alert-dismissible" role="alert" runat="server" id="div_msg_succes">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                                <span runat="server" id="msg_succes">Enregistrement réussi</span>
                            </div>
                            <div class="alert alert-danger alert-dismissible" role="alert" runat="server" id="div_msg_error">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                                <span runat="server" id="msg_error">Enregistrement échoué</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int form-elet-mg">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Projet *</label>
                                        <asp:DropDownList class="form-control" ID="drpdprojet" runat="server">

                                            <asp:ListItem Value="-1">Selectionner le projet</asp:ListItem>

                                        </asp:DropDownList>
                                    </div>

                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int form-elet-mg">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Titre du Financement *</label>
                                        <input type="text" runat="server" id="txtIntitule" class="form-control" placeholder="Intitulé du Financement *">
                                    </div>

                                </div>
                            </div>

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int form-elet-mg">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Source du financement (Partenaire) *</label>
                                        <input type="text" runat="server" id="txtsource" class="form-control" placeholder="Source du Financement *">
                                    </div>

                                </div>
                            </div>

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int form-elet-mg">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Montant du Financement *</label>
                                        <input type="number" runat="server" id="txtMontant" class="form-control" placeholder="Montant du Financement *">
                                    </div>

                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int form-elet-mg">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Monnaie du Montant du Financement(ex. BIF, USD, EURO) *</label>
                                        <input type="text" runat="server" id="txtdevise" class="form-control" placeholder="Monnaie du Montant du Financement(ex. BIF, USD, EURO) *">
                                    </div>

                                </div>
                            </div>

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int form-elet-mg">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Date du Financement *</label>
                                        <input type="date" runat="server" id="txtDate" class="form-control" placeholder="Date du Financement *">
                                    </div>

                                </div>
                            </div>

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Statut du Financement *</label>
                                        <asp:DropDownList class="form-control" ID="drpdstatut" runat="server">

                                            <asp:ListItem Value="-1">Selectionner le statut</asp:ListItem>
                                            <asp:ListItem Value="en attente">En attente</asp:ListItem>
                                            <asp:ListItem Value="partie">Une partie</asp:ListItem>
                                            <asp:ListItem Value="totalité">Totalité</asp:ListItem>

                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Etat du Financement *</label>
                                        <asp:DropDownList class="form-control" ID="drpdetat" runat="server">

                                            <asp:ListItem Value="-1">Selectionner l'etat du Projet</asp:ListItem>
                                            <asp:ListItem Value="en cours">En cours</asp:ListItem>
                                            <asp:ListItem Value="a venir">A venir</asp:ListItem>
                                            <asp:ListItem Value="cloturé">Cloturé</asp:ListItem>

                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-form"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Description du Financement </label>
                                        <div>

                                            <textarea class="html-editor" runat="server" id="txtdescription" rows="10"></textarea>

                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">

                        <div class="row">

                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <button type="button" runat="server" id="btnEnregistrer" onserverclick="btnEnregistrer_ServerClick" class="btn btn-success notika-gp-success">Ajouter</button>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Form Element area End-->
</asp:Content>
