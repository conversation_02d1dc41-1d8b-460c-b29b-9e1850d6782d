﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class SujetForumImp : ISujetForum
    {
         SujetForum forum = new SujetForum();
        int msg;

        public void AfficherDetails(long forumId,long idmem,string name,string statut, int cd, SujetForum_Class forumClass)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var f = con.SujetForums.FirstOrDefault(x => x.SujetForumId == forumId && x.Statut==statut);
                    if (f != null)
                    {
                        forumClass.SujetForumId = f.SujetForumId;
                        forumClass.Titre = f.Titre;
                        forumClass.Contenu = f.Contenu;
                        forumClass.MembreId = f.MembreId;
                        forumClass.DateCreation = f.DateCreation;
                        forumClass.NombreVues = f.NombreVues;
                        forumClass.Statut = f.Statut;
                        forumClass.etat = f.etat;
                        forumClass.name = f.name;


                    }
                }
                else if (cd==1)
                {
                    var f = con.SujetForums.FirstOrDefault(x => x.name == name && x.Statut == statut);
                    if (f != null)
                    {
                        forumClass.SujetForumId = f.SujetForumId;
                        forumClass.Titre = f.Titre;
                        forumClass.Contenu = f.Contenu;
                        forumClass.MembreId = f.MembreId;
                        forumClass.DateCreation = f.DateCreation;
                        forumClass.NombreVues = f.NombreVues;
                        forumClass.Statut = f.Statut;
                        forumClass.etat = f.etat;
                        forumClass.name = f.name;


                    }
                }
               
            }
        }

        public int Ajouter(SujetForum_Class f)
        {
            using (Connection con = new Connection())
            {
               
                forum.Titre = f.Titre;
                forum.Contenu = f.Contenu;
                forum.MembreId = f.MembreId;
                forum.DateCreation = f.DateCreation;
                forum.NombreVues = f.NombreVues;
                forum.Statut = f.Statut;
                forum.etat = f.etat;
                forum.name = f.name;

                try
                {
                    con.SujetForums.Add(forum);

                    if (con.SaveChanges() == 1)
                    {
                        con.SujetForums.Add(forum);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }

                return msg;
            }
        }

        public void ChargerGridView(GridView gdv, long id, long idmem,string name,string statut, int cd)
        {
            using (Connection con = new Connection())
            {
                var query = from f in con.SujetForums
                            join m in con.Membres on f.MembreId equals m.MembreId
                            
                            select new
                            {
                                Titre = f.Titre,
                                Contenu = f.Contenu,
                                MembreId = f.MembreId,
                                DateCreation = f.DateCreation,
                                NombreVues = f.NombreVues,
                                Statut = f.Statut,
                                etat = f.etat,
                                name = f.name,
                                Nommembre=m.Nom+" "+m.Prenom,
                                Photomembre=m.PhotoProfil,

            }
            ;

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }
        public void ChargerListview(ListView gdv, long id, long idmem, string name,string statut, string tri /*= "date" "date", "reponses", "vues"*/, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var query = (from f in con.SujetForums
                               join m in con.Membres on f.MembreId equals m.MembreId
                               where f.Statut == statut
                               orderby f.DateCreation descending
                               select new
                               {
                                   id = f.SujetForumId,
                                   Titre = f.Titre,
                                   Contenu = f.Contenu,
                                   MembreId = f.MembreId,
                                   DateCreation = f.DateCreation,
                                   NombreVues = f.NombreVues,
                                   Statut = f.Statut,
                                   etat = f.etat,
                                   name = f.name,
                                   Nommembre = m.Nom + " " + m.Prenom,
                                   Photomembre = m.PhotoProfil,

                                   NombreReponses = con.RepliesForums.Count(r => r.SujetForumId == f.SujetForumId)


                               }).ToList();

                    
                gdv.DataSource = query.ToList();
                    gdv.DataBind();
                } 
                else if (cd==1) 
                {
                    var query = from f in con.SujetForums
                                join m in con.Membres on f.MembreId equals m.MembreId
                                where f.Statut == statut
                                      && !con.RepliesForums.Any(r => r.SujetForumId == f.SujetForumId) // Aucun reply
                                orderby f.DateCreation descending
                                select new
                                {
                                    id = f.SujetForumId,
                                    Titre = f.Titre,
                                    Contenu = f.Contenu,
                                    MembreId = f.MembreId,
                                    DateCreation = f.DateCreation,
                                    NombreVues = f.NombreVues,
                                    Statut = f.Statut,
                                    etat = f.etat,
                                    name = f.name,
                                    Nommembre = m.Nom + " " + m.Prenom,
                                    Photomembre = m.PhotoProfil,
                                    NombreReponses = con.RepliesForums.Count(r => r.SujetForumId == f.SujetForumId)

                                };

                    switch (tri.ToLower())
                    {
                        case "reponses":
                            query = query.OrderByDescending(x => x.NombreReponses);
                            break;
                        case "vues":
                            query = query.OrderByDescending(x => x.NombreVues);
                            break;
                        default:
                            query = query.OrderByDescending(x => x.DateCreation);
                            break;
                    }
                    gdv.DataSource = query.ToList();
                    gdv.DataBind();

                }
                
            }
        }
        public int count(int cd, long idorg, string publie, string code)
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var b = (from p in con.SujetForums

                             select p).Count();
                    n = b;
                }
                else if (cd == 1)
                {
                    var b = (from p in con.SujetForums
                             where p.name==code
                             select p).Count();
                    n = b;
                }

            }
            return n;
        }
        public void ChargerSujetsRecents(Repeater rpt, long forumId)
        {
            using (Connection con = new Connection())
            {
                var sujets = (from f in con.SujetForums
                              join m in con.Membres on f.MembreId equals m.MembreId
                              orderby f.DateCreation descending
                              select new
                              {
                                  id=f.SujetForumId,
                                  Titre = f.Titre,
                                  Contenu = f.Contenu,
                                  MembreId = f.MembreId,
                                  DateCreation = f.DateCreation,
                                  NombreVues = f.NombreVues,
                                  Statut = f.Statut,
                                  etat = f.etat,
                                  name = f.name,
                                  Nommembre = m.Nom + " " + m.Prenom,
                                  Photomembre = m.PhotoProfil,

                                  NombreReponses = con.RepliesForums.Count(r => r.SujetForumId == f.SujetForumId)
                              
                              
                              }).Take(5).ToList();

                rpt.DataSource = sujets;
                rpt.DataBind();
            }
        }

        public int CompterParticipants(long forumId)
        {
            using (Connection con = new Connection())
            {
                // Récupérer les sujets du forum
                var sujets = con.SujetForums.Where(s => s.SujetForumId == forumId).Select(s => s.SujetForumId).ToList();

                if (sujets.Any())
                {
                    // Compter les membres distincts qui ont répondu aux sujets de ce forum
                    return con.RepliesForums
                        .Where(r => sujets.Contains(r.SujetForumId))
                        .Select(r => r.MembreId)
                        .Distinct()
                        .Count();
                }

                return 0;
            }
        }

        public int Modifier(SujetForum_Class f, long id,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    forum = con.SujetForums.FirstOrDefault(x => x.SujetForumId == id);
                    if (f != null)
                    {
                        forum.Titre = f.Titre;
                        forum.Contenu = f.Contenu;
                        forum.MembreId = f.MembreId;
                        forum.DateCreation = f.DateCreation;
                        forum.NombreVues = f.NombreVues;
                        forum.Statut = f.Statut;
                        forum.etat = f.etat;
                        forum.name = f.name;

                        try
                        {
                            if (con.SaveChanges() == 1)
                            {
                                con.SujetForums.Add(forum);
                                con.Entry(forum).State = EntityState.Modified;
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return 0;
                        }
                    }
                }
                else if (cd==1)
                {
                    forum = con.SujetForums.FirstOrDefault(x => x.SujetForumId == id);
                    if (f != null)
                    {
                        forum.NombreVues = f.NombreVues;
                       
                        try
                        {
                            if (con.SaveChanges() == 1)
                            {
                                con.SujetForums.Add(forum);
                                con.Entry(forum).State = EntityState.Modified;
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return msg=0;
                        }
                    }
                }
                else if (cd == 1)
                {
                    forum = con.SujetForums.FirstOrDefault(x => x.SujetForumId == id);
                    if (f != null)
                    {
                        forum.Statut = f.Statut;
                      
                        try
                        {
                            if (con.SaveChanges() == 1)
                            {
                                con.SujetForums.Add(forum);
                                con.Entry(forum).State = EntityState.Modified;
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return msg = 0;
                        }
                    }
                }
                return msg;
            }
        }

        public int supprimer(long id)
        {
            using (Connection con = new Connection())
            {

                forum = con.SujetForums.Where(x => x.SujetForumId == id).First();

                if (con.Entry(forum).State == EntityState.Detached)
                    con.SujetForums.Attach(forum);

                con.SujetForums.Remove(forum);
                con.SaveChanges();

                return msg = 1;
            }
        }


        public int ChangerStatut(long forumId, string nouveauStatut)
        {
            using (Connection con = new Connection())
            {
                var f = con.SujetForums.FirstOrDefault();
                if (f != null)
                {
                    f.name = nouveauStatut;
                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public void ChargerStatistiques(Repeater rpt, long forumId)
        {
            using (Connection con = new Connection())
            {
                var forum = con.SujetForums.FirstOrDefault(f => f.SujetForumId == forumId);
                if (forum != null)
                {
                    // Récupérer les sujets du forum
                    var sujets = con.SujetForums.Where(s => s.SujetForumId == forumId).ToList();
                    var sujetIds = sujets.Select(s => s.SujetForumId).ToList();

                    // Calculer les statistiques
                    var stats = new
                    {
                        id = forum.SujetForumId,
                        Nom = forum.Titre,
                        NombreSujets = sujets.Count,
                        NombreReplies = con.RepliesForums.Count(r => sujetIds.Contains(r.SujetForumId)),
                        NombreParticipants = CompterParticipants(forumId),
                        DernierSujet = sujets.OrderByDescending(s => s.DateCreation).FirstOrDefault()?.Titre ?? "Aucun sujet",
                        DateDernierSujet = sujets.OrderByDescending(s => s.DateCreation).FirstOrDefault()?.DateCreation,
                        SujetLePlusActif = (from s in sujets
                                            let nbReplies = con.RepliesForums.Count(r => r.SujetForumId == s.SujetForumId)
                                            orderby nbReplies descending
                                            select new { s.Titre, NbReplies = nbReplies }).FirstOrDefault()?.Titre ?? "Aucun sujet"
                    };

                    rpt.DataSource = new[] { stats };
                    rpt.DataBind();
                }
            }
        }

        public void ChargerForumsPopulaires(Repeater rpt, int nombreForums = 5)
        {
            using (Connection con = new Connection())
            {
                var forums = from f in con.SujetForums
                             select new
                             {
                                 id=f.SujetForumId,
                                 f.Titre,
                                 NombreSujets = con.SujetForums.Count(),
                                 NombreReplies = (from s in con.SujetForums
                                                  where s.SujetForumId == f.SujetForumId
                                                  join r in con.RepliesForums on s.SujetForumId equals r.SujetForumId
                                                  select r).Count(),
                                 DernierSujet = (from s in con.SujetForums
                                                 where s.SujetForumId == f.SujetForumId
                                                 orderby s.DateCreation descending
                                                 select s.Titre).FirstOrDefault() ?? "Aucun sujet",
                                 DateDernierSujet = (from s in con.SujetForums
                                                     where s.SujetForumId == f.SujetForumId
                                                     orderby s.DateCreation descending
                                                     select s.DateCreation).FirstOrDefault()
                             };

                rpt.DataSource = forums.OrderByDescending(f => f.NombreReplies).Take(nombreForums).ToList();
                rpt.DataBind();
            }
        }


    }
}