﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class financement : System.Web.UI.Page
    {
        private int info;
        int financementId;
        Financement_Class financementClass = new Financement_Class();
        Financement_Class financemen = new Financement_Class();
        IFinancement objFinancement = new FinancementImp();
        ICommonCode co = new CommonCode();
        IPoste objpos=new PosteImp();
        Post_Class pos=new Post_Class();

        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        DomaineRessource_Class actco = new DomaineRessource_Class();
        IDomaineRessource objactco = new DomaineRessourceImp();
        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;
        static string nsco;

        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];
            financementId = Convert.ToInt32(nsco);
            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
                    rolid = Convert.ToInt32(role.Value);//roleconnecte

                }
                else Response.Redirect("~/login.aspx");

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;

                InitialiserMessages();
                objpos.chargerPost(drpdprojet,-1,idorg,"projet","Selectionner le projet",0);
                // Vérifier si un ID est passé en paramètre pour l'édition
                if ( nsco!= null)
                {
                  
                    btnEnregistrer.InnerText = "Modifier";
                    AfficherDetails();
                }
                else
                {
                    btnEnregistrer.InnerText = "Enregistrer";
                }
            }
        }

        private void InitialiserMessages()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
        }


        protected void btn_ajouter_ServerClick(object sender, EventArgs e)
        {
            if (nsco != null)
            {
                ModifierFinancement();
            }
            else
            {
                AjouterFinancement();
            }
        }

        private void AjouterFinancement()
        {
            try
            {
                if (drpdprojet.SelectedValue=="-1" || 
                    string.IsNullOrEmpty(txtIntitule.Value) ||  string.IsNullOrEmpty(txtsource.Value) ||
                    string.IsNullOrEmpty(txtMontant.Value) ||  string.IsNullOrEmpty(txtDate.Value) ||
                    drpdstatut.SelectedValue == "-1" || txtdevise.Value=="" || drpdetat.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }

                financemen.PostId = Convert.ToInt64(drpdprojet.SelectedValue);
                financemen.Montant = Convert.ToDouble(txtMontant.Value);
                financemen.Source = txtsource.Value;
                financemen.Intitulefinancement = txtIntitule.Value;
                financemen.PartenaireId =0;
                financemen.DateFinancement = txtDate.Value;
                financemen.Dateenreg = DateTime.Now;
                financemen.statut = drpdstatut.SelectedValue;
                financemen.MembreId = ide;
                financemen.MOIS = Convert.ToDateTime(txtDate.Value).Month.ToString();
                financemen.ANNEE = Convert.ToDateTime(txtDate.Value).Year.ToString();
                financemen.OrganisationId = idorg;
                financemen.name = co.GenerateSlug(txtIntitule.Value);
                financemen.etat = drpdetat.SelectedValue;
                financemen.Description = txtdescription.Value;
                financemen.Devise = txtdevise.Value;
              
                info = objFinancement.Ajouter(financemen);

                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    msg_succes.InnerText = "Le financement a été enregistré avec succès";
                    ReinitialiserFormulaire();
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de l'enregistrement du financement";
                }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }

        private void ModifierFinancement()
        {
            try
            {
                if (drpdprojet.SelectedValue == "-1" ||
                    string.IsNullOrEmpty(txtIntitule.Value) || string.IsNullOrEmpty(txtsource.Value) ||
                    string.IsNullOrEmpty(txtMontant.Value) || string.IsNullOrEmpty(txtDate.Value) ||
                    drpdstatut.SelectedValue == "-1" || drpdetat.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }

                financemen.PostId = Convert.ToInt64(drpdprojet.SelectedValue);
                financemen.Montant = Convert.ToDouble(txtMontant.Value);
                financemen.Source = txtsource.Value;
                financemen.Intitulefinancement = txtIntitule.Value;
                financemen.PartenaireId = 0;
                financemen.DateFinancement = txtDate.Value;
                financemen.statut = drpdstatut.SelectedValue;
                financemen.MOIS = Convert.ToDateTime(txtDate.Value).Month.ToString();
                financemen.ANNEE = Convert.ToDateTime(txtDate.Value).Year.ToString();
                financemen.name = co.GenerateSlug(txtIntitule.Value);
                financemen.etat = drpdetat.SelectedValue;
                financemen.Description = txtdescription.Value;
                financemen.Devise = txtdevise.Value;


                info = objFinancement.Modifier(financemen,Convert.ToInt64(nsco),idorg,"", 0);

                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    msg_succes.InnerText = "Le financement a été modifié avec succès";
                    ReinitialiserFormulaire();
                }
            else
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de la modification du financement";
            }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }
        protected void btnEnregistrer_ServerClick(object sender, EventArgs e)
        {
            // Vérifier la validation côté serveur
            if (!Page.IsValid)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Veuillez corriger les erreurs avant de continuer.";
                return;
            }

            if (nsco == null)
            {
                AjouterFinancement();
            }
            else
            {
                ModifierFinancement();
            }
        }
        private void AfficherDetails()
        {
            if (financementId > 0)
            {
                objFinancement.AfficherDetails(financementId,idorg,-1,"",0, financemen);

                if (financemen.FinancementId > 0)
                {
                    drpdprojet.SelectedValue = financemen.PostId.ToString();
                    txtMontant.Value = financemen.Montant.ToString();
                    txtsource.Value = financemen.Source ?? "";
                    txtIntitule.Value = financemen.Intitulefinancement ?? "";
                    txtDate.Value = financemen.DateFinancement.ToString();
                    txtdescription.Value = financemen.Description;
                    drpdstatut.SelectedValue = financemen.statut;
                    financemen.Devise = txtdevise.Value;

                    drpdetat.SelectedValue = financemen.etat ?? "";

                }
            }
        }

        private void ReinitialiserFormulaire()
        {
            drpdprojet.SelectedValue = "-1";
            txtMontant.Value = "";
            txtsource.Value = "";
            txtIntitule.Value = "";
            txtDate.Value = "";
            drpdstatut.SelectedValue = "-1";
            drpdetat.SelectedValue = "-1";

        }
    }
}