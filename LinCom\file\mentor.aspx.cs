﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class mentor : System.Web.UI.Page
    {
        Mentor_Class mentorObj = new Mentor_Class();
        IMentor objMentor = new MentorImp();
        CommonCode co = new CommonCode();

        IMembre objMembre = new MembreImp();
        IProgrammeMentorat objProgramme = new ProgrammeMentoratImp();

        Membre_Class membreClass = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        Membre_Class mem = new Membre_Class();
      
        IProvince objProvince = new ProvinceImp();
        ICommune objCommune = new CommuneImp();
     
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        string nsco;
        int mentorId;
        int rolid;
       long ide; static long idorg;
        static string imge, imge1, pdfe, nameorg;
        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];
            if (!string.IsNullOrEmpty(nsco))
            {
                mentorId = Convert.ToInt32(nsco);
            }

            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {
                    ide = Convert.ToInt64(idperso.Value);
                    rolid = Convert.ToInt32(role.Value);
                }
                else
                {
                    Response.Redirect("~/login.aspx");
                }

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;

                InitialiserMessages();
                ChargerDropDownLists();

                // Si modification, charger les données
                if (mentorId > 0)
                {
                    ChargerDonneesMentor();
                }
            }
        }

        private void InitialiserMessages()
        {
            div_msg_success.Visible = false;
            div_msg_error.Visible = false;
        }

        private void ChargerDropDownLists()
        {
            try
            {
                // Charger les membres
                objMembre.chargerMembre(drpdMembre);

                // Charger les programmes de mentorat depuis la table Post
                IOrganisation objOrganisation = new OrganisationImp();
                Organisation_Class org = new Organisation_Class();
                MembresOrganisation_Class memorg = new MembresOrganisation_Class();
                IMembresOrganisation objmemorg = new MembresOrganisationImp();

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                long idorg = Convert.ToInt64(memorg.OrganisationId);

                objProgramme.chargerProgrammeMentoratFromPost(drpdProgramme, idorg);
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors du chargement des données : " + ex.Message;
            }
        }

        private void ChargerDonneesMentor()
        {
            try
            {
                objMentor.AfficherDetails(mentorId, mentorObj);

                drpdMembre.SelectedValue = mentorObj.MembreId.ToString();
                drpdProgramme.SelectedValue = mentorObj.ProgrammeMentoratId.ToString();
                txtDomaineExpertise.Value = mentorObj.DomaineExpertise;
                drpdStatut.SelectedValue = mentorObj.status;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors du chargement des données : " + ex.Message;
            }
        }

        protected void btnEnregistrer_ServerClick(object sender, EventArgs e)
        {
            try
            {
                if (ValiderFormulaire())
                {
                    if (mentorId > 0)
                    {
                        ModifierMentor();
                    }
                    else
                    {
                        AjouterMentor();
                    }
                }
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de l'enregistrement : " + ex.Message;
            }
        }

        private bool ValiderFormulaire()
        {
            if (drpdMembre.SelectedValue == "-1" ||
                drpdProgramme.SelectedValue == "-1" ||
                string.IsNullOrEmpty(txtDomaineExpertise.Value) ||
                drpdStatut.SelectedValue == "-1")
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                return false;
            }
            return true;
        }

        private void AjouterMentor()
        {
            mentorObj.MembreId = Convert.ToInt64(drpdMembre.SelectedValue);
            mentorObj.ProgrammeMentoratId = Convert.ToInt32(drpdProgramme.SelectedValue);
            mentorObj.DomaineExpertise = txtDomaineExpertise.Value;
            mentorObj.status = drpdStatut.SelectedValue;
            mentorObj.name = co.GenerateSlug(txtDomaineExpertise.Value);

            int resultat = objMentor.Ajouter(mentorObj);

            if (resultat == 1)
            {
                div_msg_success.Visible = true;
                msg_success.InnerText = "Mentor ajouté avec succès";
                ViderFormulaire();
            }
            else
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de l'ajout du mentor";
            }
        }

        private void ModifierMentor()
        {
            mentorObj.MembreId = Convert.ToInt64(drpdMembre.SelectedValue);
            mentorObj.ProgrammeMentoratId = Convert.ToInt32(drpdProgramme.SelectedValue);
            mentorObj.DomaineExpertise = txtDomaineExpertise.Value;
            mentorObj.status = drpdStatut.SelectedValue;
            mentorObj.name = co.GenerateSlug(txtDomaineExpertise.Value);

            int resultat = objMentor.Modifier(mentorObj,Convert.ToInt32(nsco),idorg,0);

            if (resultat == 1)
            {
                div_msg_success.Visible = true;
                msg_success.InnerText = "Mentor modifié avec succès";
                Response.Redirect("listmentor.aspx");
            }
            else
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de la modification du mentor";
            }
        }

        private void ViderFormulaire()
        {
            drpdMembre.SelectedValue = "-1";
            drpdProgramme.SelectedValue = "-1";
            txtDomaineExpertise.Value = "";
            drpdStatut.SelectedValue = "-1";
        }
    }
}