﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class politiqueconfidentialite : System.Web.UI.Page
    {

        private int info;
        string nscno;
        PolitiqueConfidentialite_Class condition = new PolitiqueConfidentialite_Class();
        PolitiqueConfidentialite_Class condTemp = new PolitiqueConfidentialite_Class();
        IPolitiqueConfidentialite objCondition = new PolitiqueConfidentialiteImp();
        ICommonCode co = new CommonCode();

        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        static string imge, imge1, pdfe, nameorg;
        long ide; long idorg;
        static int rolid;
        long index;

        protected void Page_Load(object sender, EventArgs e)
        {
            nscno = Request.QueryString["name"];
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin
                long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
                rolid = Convert.ToInt32(role.Value);//roleconnecte

            }
            else Response.Redirect("~/login.aspx");

            objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
            idorg = Convert.ToInt64(memorg.OrganisationId);
            objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
            nameorg = org.Nom;
            if (!IsPostBack)
            {
                initial_msg();
                afficher();

            }
        }

        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
        }

        public void AjouterCondition()
        {
            try
            {
                if (txtContenu.Value == "")
                {
                    div_msg_succes.Visible = false;
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir le contenu des conditions d'utilisation";
                    return;
                }
                else
                {
                    condition.name = ((objCondition.countID()) + 1).ToString();
                    condition.Contenu = txtContenu.Value;
                    condition.DatePublication = DateTime.Now;

                    info = objCondition.Ajouter(condition);

                    if (info == 1)
                    {
                        Response.Redirect("~/file/politiqueconfidentialite.aspx");
                    }
                    else
                    {
                        div_msg_succes.Visible = false;
                        div_msg_error.Visible = true;
                        msg_error.InnerText = "Erreur lors de l'enregistrement des conditions d'utilisation";
                    }
                }


            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false;
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + e.Message;
            }
            catch (Exception ex)
            {
                div_msg_succes.Visible = false;
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }

        public void ModifierCondition()
        {
            try
            {
                if (txtContenu.Value == "")
                {
                    div_msg_succes.Visible = false;
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir le contenu des conditions d'utilisation";
                    return;
                }

                // Récupération des détails de la condition existante
                objCondition.AfficherDetails(objCondition.ID(), condTemp);
                int conditionId = condTemp.PolitiqueId;

                // Préparation des données pour la modification
                condition.Contenu = txtContenu.Value;

                // Utilisation de la nouvelle méthode Modifier de l'interface
                info = objCondition.Modifier(condition, objCondition.ID());

                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    div_msg_error.Visible = false;
                    msg_succes.InnerText = "Les politiques d'utilisation ont été modifiées avec succès";
                    Response.Redirect("~/file/politiqueconfidentialite.aspx");
                }
                else
                {
                    div_msg_succes.Visible = false;
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de la modification des politique de confidentialité";
                }
            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false;
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + e.Message;
            }
            catch (Exception ex)
            {
                div_msg_succes.Visible = false;
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }

        protected void btn_ajouter_ServerClick(object sender, EventArgs e)
        {
            if (objCondition.ID() > 0)
            {
                ModifierCondition();

            }
            else AjouterCondition();



        }

        protected void afficher()
        {
            objCondition.AfficherDetails(objCondition.ID(), condition);
            txtContenu.Value = condition.Contenu;

        }
    }
}