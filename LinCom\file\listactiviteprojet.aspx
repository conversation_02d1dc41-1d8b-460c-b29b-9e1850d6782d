﻿<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="listactiviteprojet.aspx.cs" Inherits="LinCom.file.listactiviteprojet" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
                 <!-- Breadcomb area Start-->
<div class="breadcomb-area">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="breadcomb-list">
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                            <div class="breadcomb-wp">
                                <div class="breadcomb-icon">
                                    <i class="notika-icon notika-form"></i>
                                </div>
                                <div class="breadcomb-ctn">
                                    <h2>Liste des Activités des Projets</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                            <div class="breadcomb-report">

                                <a data-toggle="tooltip" data-placement="left" href="activiteprojet.aspx" title="Clique sur ce button pour creer une nouvelle" class="btn">Nouvelle activité</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Breadcomb area End-->
<!-- Data Table area Start-->
<div class="normal-table-area">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="data-table-list">
                    <asp:Label ID="lblMessage" runat="server" Visible="false" CssClass="alert alert-success"></asp:Label>
                    <div class="table-responsive">
                        <asp:GridView ID="gdv" class="table table-striped" runat="server" CssClass="datatbemp"
                            AutoGenerateColumns="False" EmptyDataText="Aucune Donnée Trouvée pour votre Recherche"
                            GridLines="None" Width="100%" OnRowCommand="gdv_RowCommand">
                            <AlternatingRowStyle BackColor="#DCDCDC" />
                            <Columns>
                                <asp:TemplateField HeaderText="Projet" FooterText="Projet">
                                    <ItemTemplate>
                                        <asp:Label ID="lb_projet" runat="server" Text='<%# Eval("Titreprojet") %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Projet" FooterText="Projet">
                                    <ItemTemplate>
                                        <asp:Label ID="lb_fina" runat="server" Text='<%# Eval("Titre") %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                               
                              
                                <asp:TemplateField HeaderText="Date" FooterText="Date">
                                    <ItemTemplate>
                                        <asp:Label ID="lb_date" runat="server" Text='<%# Eval("DateActivite", "{0:dd/MM/yyyy}") %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Action" FooterText="Action">
                                    <ItemTemplate>
                                        <asp:Button ID="btnEdit" BackColor="Green" class="btn btn-info btn-fix" CommandName="view" CommandArgument='<%# Eval("id") %>' runat="server" Text="Edit" />
                                        <asp:Button class="btn btn-danger notika-btn-danger" BackColor="Red" ID="btn_del" runat="server" Text="Delete" CommandName="delete" CommandArgument='<%# Eval("id") %>' OnClientClick=" return confirm('Voulez-Vous vraiment supprimer??')" />
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <EditRowStyle Font-Bold="True"></EditRowStyle>
                            <EmptyDataRowStyle HorizontalAlign="Center" Font-Names="century" Font-Size="X-Large"></EmptyDataRowStyle>
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Data Table area End-->
</asp:Content>
