﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="LincomDBModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="ActiviteProjet">
          <Key>
            <PropertyRef Name="ActiviteProjetId" />
          </Key>
          <Property Name="ActiviteProjetId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PostId" Type="bigint" />
          <Property Name="Titre" Type="nvarchar" MaxLength="150" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="DateActivite" Type="varchar" MaxLength="100" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="OrganisationId" Type="bigint" />
          <Property Name="photo1" Type="varchar" MaxLength="100" />
          <Property Name="photo2" Type="varchar" MaxLength="100" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
          <Property Name="DateActiviteEnreg" Type="datetime" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="summery" Type="nvarchar(max)" />
          <Property Name="number_of_view" Type="int" />
          <Property Name="like" Type="int" />
          <Property Name="dislike" Type="int" />
          <Property Name="MOIS" Type="varchar" MaxLength="100" />
          <Property Name="ANNEE" Type="varchar" MaxLength="100" />
          <Property Name="DateCreation" Type="datetime" />
          <Property Name="etat" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="AvisRessources">
          <Key>
            <PropertyRef Name="AvisId" />
          </Key>
          <Property Name="AvisId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="RessourceId" Type="bigint" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="Note" Type="int" />
          <Property Name="Commentaire" Type="nvarchar(max)" />
          <Property Name="DateAvis" Type="varchar" MaxLength="100" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="budgets_activite">
          <Key>
            <PropertyRef Name="id_budget" />
          </Key>
          <Property Name="id_budget" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ActiviteProjetId" Type="bigint" />
          <Property Name="montant_prevu" Type="float" />
          <Property Name="montant_realise" Type="float" />
          <Property Name="observation" Type="text" />
        </EntityType>
        <EntityType Name="CategoriePost">
          <Key>
            <PropertyRef Name="CategoriePostId" />
          </Key>
          <Property Name="CategoriePostId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Libelle" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="CommentPost">
          <Key>
            <PropertyRef Name="CommentPostId" />
          </Key>
          <Property Name="CommentPostId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PostId" Type="bigint" Nullable="false" />
          <Property Name="MembreId" Type="bigint" Nullable="false" />
          <Property Name="Contenu" Type="nvarchar(max)" />
          <Property Name="DateCommentaire" Type="datetime" />
          <Property Name="EstVisible" Type="varchar" MaxLength="100" />
          <Property Name="Nbrevue" Type="int" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="Commune">
          <Key>
            <PropertyRef Name="CommuneId" />
          </Key>
          <Property Name="CommuneId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Nom" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="ProvinceId" Type="int" Nullable="false" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="ConditionUtilisation">
          <Key>
            <PropertyRef Name="ConditionId" />
          </Key>
          <Property Name="ConditionId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Contenu" Type="nvarchar(max)" />
          <Property Name="DatePublication" Type="datetime" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="Conversation">
          <Key>
            <PropertyRef Name="ConversationId" />
          </Key>
          <Property Name="ConversationId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Sujet" Type="nvarchar(max)" />
          <Property Name="IsGroup" Type="int" />
          <Property Name="CreatedAt" Type="datetime" />
        </EntityType>
        <EntityType Name="ConversationParticipants">
          <Key>
            <PropertyRef Name="ConversationparticipantsID" />
          </Key>
          <Property Name="ConversationparticipantsID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ConversationId" Type="bigint" Nullable="false" />
          <Property Name="UserId" Type="bigint" Nullable="false" />
          <Property Name="JoinedAt" Type="datetime" />
        </EntityType>
        <EntityType Name="DomaineFormation">
          <Key>
            <PropertyRef Name="DomaineFormationId" />
          </Key>
          <Property Name="DomaineFormationId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="FormationId" Type="bigint" Nullable="false" />
          <Property Name="DomaineInterventionId" Type="int" />
          <Property Name="DateCreation" Type="datetime" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="DomaineIntervention">
          <Key>
            <PropertyRef Name="DomaineInterventionId" />
          </Key>
          <Property Name="DomaineInterventionId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Libelle" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="DomaineInterventionOrganisation">
          <Key>
            <PropertyRef Name="DomaineInterventionOrganisationId" />
          </Key>
          <Property Name="DomaineInterventionOrganisationId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="DomaineInterventionId" Type="int" Nullable="false" />
          <Property Name="OrganisationId" Type="bigint" Nullable="false" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="name" Type="nvarchar(max)" Nullable="false" />
          <Property Name="statut" Type="varchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="DomainePost">
          <Key>
            <PropertyRef Name="DomainePostId" />
          </Key>
          <Property Name="DomainePostId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PostId" Type="bigint" Nullable="false" />
          <Property Name="DomaineInterventionOrganisationId" Type="int" />
          <Property Name="DateCreation" Type="datetime" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="OrganisationId" Type="bigint" />
        </EntityType>
        <EntityType Name="DomaineProjet">
          <Key>
            <PropertyRef Name="DomaineProjetId" />
          </Key>
          <Property Name="DomaineProjetId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ProjetId" Type="bigint" Nullable="false" />
          <Property Name="DomaineInterventionId" Type="int" />
          <Property Name="DateCreation" Type="datetime" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="OrganisationId" Type="bigint" />
        </EntityType>
        <EntityType Name="DomaineRessources">
          <Key>
            <PropertyRef Name="DomaineResourceId" />
          </Key>
          <Property Name="DomaineResourceId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="RessourceId" Type="bigint" Nullable="false" />
          <Property Name="DomaineInterventionId" Type="int" />
          <Property Name="DateCreation" Type="datetime" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="OrganisationId" Type="bigint" />
        </EntityType>
        <EntityType Name="FAQ">
          <Key>
            <PropertyRef Name="FAQId" />
          </Key>
          <Property Name="FAQId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Question" Type="nvarchar" MaxLength="255" />
          <Property Name="Reponse" Type="nvarchar(max)" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
          <Property Name="DatePublication" Type="datetime" />
        </EntityType>
        <EntityType Name="FeedbackMentor">
          <Key>
            <PropertyRef Name="FeedbackId" />
          </Key>
          <Property Name="FeedbackId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="SessionMentoratId" Type="bigint" />
          <Property Name="Commentaire" Type="nvarchar(max)" />
          <Property Name="Note" Type="int" />
        </EntityType>
        <EntityType Name="FeedbackMentoree">
          <Key>
            <PropertyRef Name="FeedbackId" />
          </Key>
          <Property Name="FeedbackId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="SessionMentoratId" Type="bigint" />
          <Property Name="Commentaire" Type="nvarchar(max)" />
          <Property Name="Note" Type="int" />
        </EntityType>
        <EntityType Name="FichierMessage">
          <Key>
            <PropertyRef Name="FichierId" />
          </Key>
          <Property Name="FichierId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="MessageId" Type="int" />
          <Property Name="NomFichier" Type="nvarchar" MaxLength="255" />
          <Property Name="UrlFichier" Type="nvarchar" MaxLength="255" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="Financement">
          <Key>
            <PropertyRef Name="FinancementId" />
          </Key>
          <Property Name="FinancementId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PostId" Type="bigint" />
          <Property Name="Montant" Type="float" />
          <Property Name="Devise" Type="nvarchar" MaxLength="150" />
          <Property Name="Source" Type="nvarchar" MaxLength="150" />
          <Property Name="Intitulefinancement" Type="nvarchar" MaxLength="150" />
          <Property Name="PartenaireId" Type="int" />
          <Property Name="DateFinancement" Type="varchar" MaxLength="100" />
          <Property Name="Dateenreg" Type="datetime" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="MOIS" Type="varchar" MaxLength="100" />
          <Property Name="ANNEE" Type="varchar" MaxLength="100" />
          <Property Name="OrganisationId" Type="bigint" />
          <Property Name="name" Type="nvarchar(max)" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="etat" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="Formation">
          <Key>
            <PropertyRef Name="FormationId" />
          </Key>
          <Property Name="FormationId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Titre" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="DateFormation" Type="datetime" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="Fichier" Type="nvarchar" MaxLength="255" />
          <Property Name="AuteurId" Type="int" />
          <Property Name="photocouverture" Type="varchar" MaxLength="100" />
          <Property Name="nombrepage" Type="int" />
          <Property Name="typeressources" Type="varchar" MaxLength="50" />
          <Property Name="nbrevue" Type="int" />
          <Property Name="nbrelike" Type="int" />
          <Property Name="DateCreation" Type="datetime" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
          <Property Name="DatePublication" Type="varchar" MaxLength="100" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="OrganisationId" Type="bigint" />
          <Property Name="MOIS" Type="varchar" MaxLength="100" />
          <Property Name="ANNEE" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="Forum">
          <Key>
            <PropertyRef Name="ForumId" />
          </Key>
          <Property Name="ForumId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Nom" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="Langue">
          <Key>
            <PropertyRef Name="LangueId" />
          </Key>
          <Property Name="LangueId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Code" Type="nvarchar" MaxLength="10" Nullable="false" />
          <Property Name="Nom" Type="nvarchar" MaxLength="500" Nullable="false" />
          <Property Name="Langu" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="statut" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="MembreProfil">
          <Key>
            <PropertyRef Name="MembreProfilId" />
          </Key>
          <Property Name="MembreProfilId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="MembreId" Type="bigint" Nullable="false" />
          <Property Name="PhotoProfil" Type="nvarchar" MaxLength="255" />
          <Property Name="facebook" Type="varchar" MaxLength="100" />
          <Property Name="siteweb" Type="varchar" MaxLength="100" />
          <Property Name="twitter" Type="varchar" MaxLength="100" />
          <Property Name="instagramme" Type="varchar" MaxLength="100" />
          <Property Name="linkedin" Type="varchar" MaxLength="100" />
          <Property Name="youtube" Type="varchar" MaxLength="100" />
          <Property Name="Biographie" Type="nvarchar(max)" />
          <Property Name="DateInscription" Type="datetime" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="Membres">
          <Key>
            <PropertyRef Name="MembreId" />
          </Key>
          <Property Name="MembreId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Nom" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="Prenom" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="Email" Type="nvarchar" MaxLength="150" />
          <Property Name="Telephone" Type="nvarchar" MaxLength="50" />
          <Property Name="Sexe" Type="nvarchar" MaxLength="10" />
          <Property Name="DateNaissance" Type="date" />
          <Property Name="ProvinceId" Type="int" />
          <Property Name="CommuneId" Type="int" />
          <Property Name="CreatedAt" Type="datetime2" Precision="7" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="province" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="Adresse" Type="nvarchar" MaxLength="200" />
          <Property Name="commune" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="username" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="motpasse" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="statut" Type="nvarchar" MaxLength="50" />
          <Property Name="IsActive" Type="int" />
          <Property Name="IsVerified" Type="int" />
          <Property Name="LastLogin" Type="datetime2" Precision="7" />
          <Property Name="RoleMembreID" Type="int" />
          <Property Name="PhotoProfil" Type="nvarchar" MaxLength="255" />
          <Property Name="facebook" Type="varchar" MaxLength="100" />
          <Property Name="siteweb" Type="varchar" MaxLength="100" />
          <Property Name="twitter" Type="varchar" MaxLength="100" />
          <Property Name="instagramme" Type="varchar" MaxLength="100" />
          <Property Name="linkedin" Type="varchar" MaxLength="100" />
          <Property Name="youtube" Type="varchar" MaxLength="100" />
          <Property Name="Biographie" Type="nvarchar(max)" />
          <Property Name="DateInscription" Type="datetime" />
          <Property Name="ResetToken" Type="nvarchar" MaxLength="128" />
          <Property Name="ResetTokenExpiry" Type="datetime" />
          <Property Name="LanguePreferee" Type="nvarchar" MaxLength="10" />
          <Property Name="AccepteNotification" Type="int" />
        </EntityType>
        <EntityType Name="MembresOrganisation">
          <Key>
            <PropertyRef Name="MembresOrganisationId" />
          </Key>
          <Property Name="MembresOrganisationId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="MembreId" Type="bigint" Nullable="false" />
          <Property Name="OrganisationId" Type="bigint" Nullable="false" />
          <Property Name="Poste" Type="nvarchar" MaxLength="100" />
          <Property Name="DateAdhesion" Type="datetime" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="statut" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="RoleMembreID" Type="int" />
        </EntityType>
        <EntityType Name="Mentor">
          <Key>
            <PropertyRef Name="MentorId" />
          </Key>
          <Property Name="MentorId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="DomaineExpertise" Type="nvarchar(max)" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="ProgrammeMentoratId" Type="int" />
          <Property Name="status" Type="varchar" MaxLength="100" />
          <Property Name="DateInscription" Type="varchar" MaxLength="100" />
          <Property Name="DateAccept" Type="varchar" MaxLength="100" />
          <Property Name="OrganisationId" Type="bigint" />
          <Property Name="rate" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="Mentore">
          <Key>
            <PropertyRef Name="MentoreId" />
          </Key>
          <Property Name="MentoreId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="ProgrammeMentoratId" Type="int" />
          <Property Name="status" Type="varchar" MaxLength="100" />
          <Property Name="DateInscription" Type="varchar" MaxLength="100" />
          <Property Name="DateAccept" Type="varchar" MaxLength="100" />
          <Property Name="OrganisationId" Type="bigint" />
          <Property Name="rate" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="menu">
          <Key>
            <PropertyRef Name="MenuID" />
          </Key>
          <Property Name="MenuID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="NomMenu" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="UrlMenu" Type="nvarchar" MaxLength="255" />
          <Property Name="Icone" Type="nvarchar" MaxLength="50" />
          <Property Name="ParentID" Type="int" />
          <Property Name="Ordre" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="menupermission">
          <Key>
            <PropertyRef Name="MenuPermissionID" />
          </Key>
          <Property Name="MenuPermissionID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="MenuID" Type="int" Nullable="false" />
          <Property Name="PermissionID" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="Message">
          <Key>
            <PropertyRef Name="MessageId" />
          </Key>
          <Property Name="MessageId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ConversationId" Type="bigint" />
          <Property Name="SenderId" Type="bigint" />
          <Property Name="Contenu" Type="nvarchar(max)" />
          <Property Name="AttachmentUrl" Type="nvarchar" MaxLength="255" />
          <Property Name="DateEnvoi" Type="datetime" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="MessageStatus">
          <Key>
            <PropertyRef Name="MessagestatusID" />
          </Key>
          <Property Name="MessagestatusID" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="MessageId" Type="bigint" Nullable="false" />
          <Property Name="UserId" Type="bigint" Nullable="false" />
          <Property Name="IsRead" Type="int" />
          <Property Name="ReadAt" Type="datetime" />
        </EntityType>
        <EntityType Name="Notification">
          <Key>
            <PropertyRef Name="NotificationId" />
          </Key>
          <Property Name="NotificationId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="Titre" Type="nvarchar" MaxLength="150" />
          <Property Name="Message" Type="nvarchar(max)" />
          <Property Name="DateNotification" Type="datetime" />
          <Property Name="UrlRedirection" Type="nvarchar" MaxLength="255" />
          <Property Name="Lu" Type="int" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="statut" Type="varchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="Organisation">
          <Key>
            <PropertyRef Name="OrganisationId" />
          </Key>
          <Property Name="OrganisationId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Nom" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="TypeOrganisationId" Type="int" Nullable="false" />
          <Property Name="ProvinceId" Type="int" Nullable="false" />
          <Property Name="CommuneId" Type="int" Nullable="false" />
          <Property Name="Adresse" Type="nvarchar(max)" />
          <Property Name="Email" Type="nvarchar" MaxLength="150" />
          <Property Name="Telephone" Type="nvarchar" MaxLength="50" />
          <Property Name="SiteWeb" Type="nvarchar" MaxLength="150" />
          <Property Name="Logo" Type="nvarchar" MaxLength="255" />
          <Property Name="RS" Type="varchar" MaxLength="100" />
          <Property Name="sigle" Type="varchar" MaxLength="100" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="Vision" Type="nvarchar(max)" />
          <Property Name="Mission" Type="nvarchar(max)" />
          <Property Name="NbreHomme" Type="int" />
          <Property Name="NbreFemme" Type="int" />
          <Property Name="Enregistre" Type="varchar" MaxLength="100" />
          <Property Name="RC" Type="varchar" MaxLength="100" />
          <Property Name="RC_doc" Type="varchar" MaxLength="100" />
          <Property Name="NIF" Type="varchar" MaxLength="100" />
          <Property Name="NIF_doc" Type="varchar" MaxLength="100" />
          <Property Name="facebook" Type="varchar" MaxLength="100" />
          <Property Name="twitter" Type="varchar" MaxLength="100" />
          <Property Name="instagramme" Type="varchar" MaxLength="100" />
          <Property Name="linkedin" Type="varchar" MaxLength="100" />
          <Property Name="youtube" Type="varchar" MaxLength="100" />
          <Property Name="province" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="commune" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="DateCreation" Type="date" />
          <Property Name="Statut" Type="nvarchar" MaxLength="50" />
          <Property Name="CreatedAt" Type="datetime2" Precision="7" />
          <Property Name="UpdatedAt" Type="datetime2" Precision="7" />
          <Property Name="name" Type="nvarchar(max)" Nullable="false" />
          <Property Name="Latitude" Type="float" />
          <Property Name="Longitude" Type="float" />
        </EntityType>
        <EntityType Name="ParamettreApplication">
          <Key>
            <PropertyRef Name="ParamettreId" />
          </Key>
          <Property Name="ParamettreId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Cle" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="Valeur" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="Partenaires">
          <Key>
            <PropertyRef Name="PartenaireId" />
          </Key>
          <Property Name="PartenaireId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Nom" Type="nvarchar" MaxLength="150" />
          <Property Name="Contact" Type="nvarchar" MaxLength="150" />
          <Property Name="logo" Type="varchar" MaxLength="100" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
          <Property Name="etat" Type="varchar" MaxLength="100" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="lienwebsite" Type="nvarchar" MaxLength="150" />
          <Property Name="Email" Type="nvarchar" MaxLength="150" />
        </EntityType>
        <EntityType Name="PartenairesOrganisation">
          <Key>
            <PropertyRef Name="PartenaireorganisationId" />
          </Key>
          <Property Name="PartenaireorganisationId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Nom" Type="nvarchar" MaxLength="150" />
          <Property Name="Contact" Type="nvarchar" MaxLength="150" />
          <Property Name="logo" Type="varchar" MaxLength="100" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
          <Property Name="etat" Type="varchar" MaxLength="100" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="lienwebsite" Type="nvarchar" MaxLength="150" />
          <Property Name="Email" Type="nvarchar" MaxLength="150" />
          <Property Name="OrganisationId" Type="bigint" />
        </EntityType>
        <EntityType Name="ParticipantConversation">
          <Key>
            <PropertyRef Name="ParticipantId" />
          </Key>
          <Property Name="ParticipantId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ConversationId" Type="bigint" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="JoinedAt" Type="datetime" />
        </EntityType>
        <EntityType Name="permission">
          <Key>
            <PropertyRef Name="PermissionID" />
          </Key>
          <Property Name="PermissionID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CodePermission" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="Description" Type="nvarchar" MaxLength="255" />
          <Property Name="RoleMembreID" Type="int" Nullable="false" />
          <Property Name="Code_Menu" Type="bigint" Nullable="false" />
          <Property Name="Access" Type="varchar" MaxLength="100" />
          <Property Name="OrganisationId" Type="bigint" />
        </EntityType>
        <EntityType Name="PolitiqueConfidentialite">
          <Key>
            <PropertyRef Name="PolitiqueId" />
          </Key>
          <Property Name="PolitiqueId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Contenu" Type="nvarchar(max)" />
          <Property Name="DatePublication" Type="datetime" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="Post">
          <Key>
            <PropertyRef Name="PostId" />
          </Key>
          <Property Name="PostId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Titre" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="Contenu" Type="nvarchar(max)" />
          <Property Name="CategoriePostId" Type="int" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="DatePublication" Type="varchar" MaxLength="100" />
          <Property Name="summery" Type="nvarchar(max)" />
          <Property Name="author" Type="varchar" MaxLength="500" />
          <Property Name="photo" Type="varchar" MaxLength="100" />
          <Property Name="video" Type="varchar" MaxLength="100" />
          <Property Name="number_of_view" Type="int" />
          <Property Name="like" Type="int" />
          <Property Name="dislike" Type="int" />
          <Property Name="starttime" Type="varchar" MaxLength="100" />
          <Property Name="eventduration" Type="varchar" MaxLength="100" />
          <Property Name="eventplace" Type="varchar" MaxLength="100" />
          <Property Name="whoattend" Type="varchar" MaxLength="100" />
          <Property Name="qualificationattend" Type="text" />
          <Property Name="langueevent" Type="varchar" MaxLength="100" />
          <Property Name="externevent" Type="varchar" MaxLength="100" />
          <Property Name="MOIS" Type="varchar" MaxLength="100" />
          <Property Name="ANNEE" Type="varchar" MaxLength="100" />
          <Property Name="lien_isncription" Type="text" />
          <Property Name="pdf" Type="varchar" MaxLength="100" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="DateCreation" Type="datetime" />
          <Property Name="DateModification" Type="datetime" />
          <Property Name="EstPublie" Type="varchar" MaxLength="100" />
          <Property Name="EstPublieEvent" Type="varchar" MaxLength="100" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
          <Property Name="etat" Type="varchar" MaxLength="100" />
          <Property Name="OrganisationId" Type="bigint" />
        </EntityType>
        <EntityType Name="PreferenceCookies">
          <Key>
            <PropertyRef Name="PreferenceId" />
          </Key>
          <Property Name="PreferenceId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="Acceptation" Type="bit" />
          <Property Name="DateChoix" Type="datetime" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="Programmementorat">
          <Key>
            <PropertyRef Name="ProgrammeMentoratId" />
          </Key>
          <Property Name="ProgrammeMentoratId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Titre" Type="nvarchar" MaxLength="150" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="DateDebut" Type="datetime" />
          <Property Name="DateFin" Type="datetime" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="auteur" Type="varchar" MaxLength="150" />
          <Property Name="Dateenreg" Type="datetime" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="status" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="ProgrammesEtInitiative">
          <Key>
            <PropertyRef Name="ProgrammeId" />
          </Key>
          <Property Name="ProgrammeId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Nom" Type="nvarchar" MaxLength="150" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="DateLancement" Type="datetime" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="statut" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="Projet">
          <Key>
            <PropertyRef Name="ProjetId" />
          </Key>
          <Property Name="ProjetId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Titre" Type="nvarchar" MaxLength="150" />
          <Property Name="MontantProjet" Type="float" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="DateDebut" Type="datetime" />
          <Property Name="DateFin" Type="datetime" />
          <Property Name="OrganisationId" Type="bigint" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="status" Type="varchar" MaxLength="100" />
          <Property Name="etat" Type="varchar" MaxLength="100" />
          <Property Name="pdf" Type="varchar" MaxLength="100" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="Provinces">
          <Key>
            <PropertyRef Name="ProvinceId" />
          </Key>
          <Property Name="ProvinceId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Nom" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="RepliesForum">
          <Key>
            <PropertyRef Name="RepliesForumId" />
          </Key>
          <Property Name="RepliesForumId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="SujetForumId" Type="bigint" Nullable="false" />
          <Property Name="MembreId" Type="bigint" Nullable="false" />
          <Property Name="Contenu" Type="nvarchar(max)" />
          <Property Name="DateReply" Type="datetime" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="EstSolution" Type="int" />
          <Property Name="NombreLikes" Type="int" />
          <Property Name="NombreDislikes" Type="int" />
          <Property Name="NombreVues" Type="int" />
          <Property Name="statut" Type="varchar" MaxLength="150" />
          <Property Name="etat" Type="varchar" MaxLength="150" />
        </EntityType>
        <EntityType Name="Ressources">
          <Key>
            <PropertyRef Name="RessourceId" />
          </Key>
          <Property Name="RessourceId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Titre" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="Description" Type="nvarchar(max)" />
          <Property Name="DateFormation" Type="varchar" MaxLength="100" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="Fichier" Type="nvarchar" MaxLength="255" />
          <Property Name="AuteurId" Type="int" />
          <Property Name="photocouverture" Type="varchar" MaxLength="100" />
          <Property Name="nombrepage" Type="int" />
          <Property Name="typeressources" Type="varchar" MaxLength="50" />
          <Property Name="nbrevue" Type="int" />
          <Property Name="nbrelike" Type="int" />
          <Property Name="DateCreation" Type="datetime" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
          <Property Name="DatePublication" Type="varchar" MaxLength="100" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="OrganisationId" Type="bigint" />
          <Property Name="MOIS" Type="varchar" MaxLength="100" />
          <Property Name="ANNEE" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="rolemembre">
          <Key>
            <PropertyRef Name="RoleMembreID" />
          </Key>
          <Property Name="RoleMembreID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="NomRole" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="name" Type="nvarchar(max)" Nullable="false" />
          <Property Name="Description" Type="nvarchar" MaxLength="255" />
          <Property Name="TYPE" Type="varchar" MaxLength="100" />
          <Property Name="CDROLE" Type="varchar" MaxLength="100" />
          <Property Name="OrganisationId" Type="bigint" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="SessionMentorat">
          <Key>
            <PropertyRef Name="SessionMentoratId" />
          </Key>
          <Property Name="SessionMentoratId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ProgrammeMentoratId" Type="int" />
          <Property Name="MentorId" Type="int" />
          <Property Name="MentoreId" Type="int" />
          <Property Name="DateSession" Type="datetime" />
          <Property Name="Sujet" Type="nvarchar" MaxLength="150" />
          <Property Name="Notes" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="StatutUtilisateur">
          <Key>
            <PropertyRef Name="StatutId" />
          </Key>
          <Property Name="StatutId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="Libelle" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="Subscription">
          <Key>
            <PropertyRef Name="SubscriptionId" />
          </Key>
          <Property Name="SubscriptionId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="Email" Type="nvarchar" MaxLength="150" />
          <Property Name="DateAbonnement" Type="datetime" />
        </EntityType>
        <EntityType Name="SujetForum">
          <Key>
            <PropertyRef Name="SujetForumId" />
          </Key>
          <Property Name="SujetForumId" Type="bigint" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Titre" Type="nvarchar(max)" Nullable="false" />
          <Property Name="Contenu" Type="nvarchar(max)" Nullable="false" />
          <Property Name="MembreId" Type="bigint" Nullable="false" />
          <Property Name="DateCreation" Type="datetime" />
          <Property Name="NombreVues" Type="int" />
          <Property Name="Statut" Type="nvarchar" MaxLength="20" />
          <Property Name="etat" Type="nvarchar" MaxLength="150" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="SupportFormation">
          <Key>
            <PropertyRef Name="SupportFormationId" />
          </Key>
          <Property Name="SupportFormationId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="FormationId" Type="bigint" Nullable="false" />
          <Property Name="Titre" Type="nvarchar" MaxLength="150" />
          <Property Name="Fichier" Type="nvarchar" MaxLength="255" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="DateCreation" Type="datetime" />
          <Property Name="statut" Type="varchar" MaxLength="100" />
          <Property Name="DatePublication" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="TelechargementRessources">
          <Key>
            <PropertyRef Name="TelechargementId" />
          </Key>
          <Property Name="TelechargementId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="RessourceId" Type="bigint" />
          <Property Name="MembreId" Type="bigint" />
          <Property Name="DateTelechargement" Type="datetime" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="Traduction">
          <Key>
            <PropertyRef Name="TraductionId" />
          </Key>
          <Property Name="TraductionId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Cle" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="LangueId" Type="int" Nullable="false" />
          <Property Name="Texte" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="TypeOrganisation">
          <Key>
            <PropertyRef Name="TypeOrganisationId" />
          </Key>
          <Property Name="TypeOrganisationId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Libelle" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityContainer Name="LincomDBModelStoreContainer">
          <EntitySet Name="ActiviteProjet" EntityType="Self.ActiviteProjet" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="AvisRessources" EntityType="Self.AvisRessources" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="budgets_activite" EntityType="Self.budgets_activite" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CategoriePost" EntityType="Self.CategoriePost" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CommentPost" EntityType="Self.CommentPost" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Commune" EntityType="Self.Commune" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ConditionUtilisation" EntityType="Self.ConditionUtilisation" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Conversation" EntityType="Self.Conversation" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ConversationParticipants" EntityType="Self.ConversationParticipants" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DomaineFormation" EntityType="Self.DomaineFormation" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DomaineIntervention" EntityType="Self.DomaineIntervention" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DomaineInterventionOrganisation" EntityType="Self.DomaineInterventionOrganisation" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DomainePost" EntityType="Self.DomainePost" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DomaineProjet" EntityType="Self.DomaineProjet" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DomaineRessources" EntityType="Self.DomaineRessources" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="FAQ" EntityType="Self.FAQ" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="FeedbackMentor" EntityType="Self.FeedbackMentor" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="FeedbackMentoree" EntityType="Self.FeedbackMentoree" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="FichierMessage" EntityType="Self.FichierMessage" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Financement" EntityType="Self.Financement" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Formation" EntityType="Self.Formation" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Forum" EntityType="Self.Forum" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Langue" EntityType="Self.Langue" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="MembreProfil" EntityType="Self.MembreProfil" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Membres" EntityType="Self.Membres" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="MembresOrganisation" EntityType="Self.MembresOrganisation" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Mentor" EntityType="Self.Mentor" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Mentore" EntityType="Self.Mentore" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="menu" EntityType="Self.menu" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="menupermission" EntityType="Self.menupermission" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Message" EntityType="Self.Message" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="MessageStatus" EntityType="Self.MessageStatus" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Notification" EntityType="Self.Notification" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Organisation" EntityType="Self.Organisation" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ParamettreApplication" EntityType="Self.ParamettreApplication" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Partenaires" EntityType="Self.Partenaires" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="PartenairesOrganisation" EntityType="Self.PartenairesOrganisation" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ParticipantConversation" EntityType="Self.ParticipantConversation" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="permission" EntityType="Self.permission" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="PolitiqueConfidentialite" EntityType="Self.PolitiqueConfidentialite" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Post" EntityType="Self.Post" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="PreferenceCookies" EntityType="Self.PreferenceCookies" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Programmementorat" EntityType="Self.Programmementorat" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProgrammesEtInitiative" EntityType="Self.ProgrammesEtInitiative" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Projet" EntityType="Self.Projet" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Provinces" EntityType="Self.Provinces" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="RepliesForum" EntityType="Self.RepliesForum" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Ressources" EntityType="Self.Ressources" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="rolemembre" EntityType="Self.rolemembre" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="SessionMentorat" EntityType="Self.SessionMentorat" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="StatutUtilisateur" EntityType="Self.StatutUtilisateur" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Subscription" EntityType="Self.Subscription" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="SujetForum" EntityType="Self.SujetForum" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="SupportFormation" EntityType="Self.SupportFormation" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TelechargementRessources" EntityType="Self.TelechargementRessources" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Traduction" EntityType="Self.Traduction" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TypeOrganisation" EntityType="Self.TypeOrganisation" Schema="dbo" store:Type="Tables" />
        </EntityContainer>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="LincomDBModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="ActiviteProjet">
          <Key>
            <PropertyRef Name="ActiviteProjetId" />
          </Key>
          <Property Name="ActiviteProjetId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PostId" Type="Int64" />
          <Property Name="Titre" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DateActivite" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="OrganisationId" Type="Int64" />
          <Property Name="photo1" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="photo2" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="DateActiviteEnreg" Type="DateTime" Precision="3" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="summery" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="number_of_view" Type="Int32" />
          <Property Name="like" Type="Int32" />
          <Property Name="dislike" Type="Int32" />
          <Property Name="MOIS" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="ANNEE" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="DateCreation" Type="DateTime" Precision="3" />
          <Property Name="etat" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="AvisRessource">
          <Key>
            <PropertyRef Name="AvisId" />
          </Key>
          <Property Name="AvisId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="RessourceId" Type="Int64" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="Note" Type="Int32" />
          <Property Name="Commentaire" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DateAvis" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="budgets_activite">
          <Key>
            <PropertyRef Name="id_budget" />
          </Key>
          <Property Name="id_budget" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ActiviteProjetId" Type="Int64" />
          <Property Name="montant_prevu" Type="Double" />
          <Property Name="montant_realise" Type="Double" />
          <Property Name="observation" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="CategoriePost">
          <Key>
            <PropertyRef Name="CategoriePostId" />
          </Key>
          <Property Name="CategoriePostId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Libelle" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="CommentPost">
          <Key>
            <PropertyRef Name="CommentPostId" />
          </Key>
          <Property Name="CommentPostId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PostId" Type="Int64" Nullable="false" />
          <Property Name="MembreId" Type="Int64" Nullable="false" />
          <Property Name="Contenu" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DateCommentaire" Type="DateTime" Precision="3" />
          <Property Name="EstVisible" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="Nbrevue" Type="Int32" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="Commune">
          <Key>
            <PropertyRef Name="CommuneId" />
          </Key>
          <Property Name="CommuneId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Nom" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ProvinceId" Type="Int32" Nullable="false" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="ConditionUtilisation">
          <Key>
            <PropertyRef Name="ConditionId" />
          </Key>
          <Property Name="ConditionId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Contenu" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DatePublication" Type="DateTime" Precision="3" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="Conversation">
          <Key>
            <PropertyRef Name="ConversationId" />
          </Key>
          <Property Name="ConversationId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Sujet" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="IsGroup" Type="Int32" />
          <Property Name="CreatedAt" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="ConversationParticipant">
          <Key>
            <PropertyRef Name="ConversationparticipantsID" />
          </Key>
          <Property Name="ConversationparticipantsID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ConversationId" Type="Int64" Nullable="false" />
          <Property Name="UserId" Type="Int64" Nullable="false" />
          <Property Name="JoinedAt" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="DomaineFormation">
          <Key>
            <PropertyRef Name="DomaineFormationId" />
          </Key>
          <Property Name="DomaineFormationId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="FormationId" Type="Int64" Nullable="false" />
          <Property Name="DomaineInterventionId" Type="Int32" />
          <Property Name="DateCreation" Type="DateTime" Precision="3" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="DomaineIntervention">
          <Key>
            <PropertyRef Name="DomaineInterventionId" />
          </Key>
          <Property Name="DomaineInterventionId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Libelle" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="DomaineInterventionOrganisation">
          <Key>
            <PropertyRef Name="DomaineInterventionOrganisationId" />
          </Key>
          <Property Name="DomaineInterventionOrganisationId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="DomaineInterventionId" Type="Int32" Nullable="false" />
          <Property Name="OrganisationId" Type="Int64" Nullable="false" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="name" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" Nullable="false" />
        </EntityType>
        <EntityType Name="DomainePost">
          <Key>
            <PropertyRef Name="DomainePostId" />
          </Key>
          <Property Name="DomainePostId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PostId" Type="Int64" Nullable="false" />
          <Property Name="DomaineInterventionOrganisationId" Type="Int32" />
          <Property Name="DateCreation" Type="DateTime" Precision="3" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="OrganisationId" Type="Int64" />
        </EntityType>
        <EntityType Name="DomaineProjet">
          <Key>
            <PropertyRef Name="DomaineProjetId" />
          </Key>
          <Property Name="DomaineProjetId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ProjetId" Type="Int64" Nullable="false" />
          <Property Name="DomaineInterventionId" Type="Int32" />
          <Property Name="DateCreation" Type="DateTime" Precision="3" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="OrganisationId" Type="Int64" />
        </EntityType>
        <EntityType Name="DomaineRessource">
          <Key>
            <PropertyRef Name="DomaineResourceId" />
          </Key>
          <Property Name="DomaineResourceId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="RessourceId" Type="Int64" Nullable="false" />
          <Property Name="DomaineInterventionId" Type="Int32" />
          <Property Name="DateCreation" Type="DateTime" Precision="3" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="OrganisationId" Type="Int64" />
        </EntityType>
        <EntityType Name="FAQ">
          <Key>
            <PropertyRef Name="FAQId" />
          </Key>
          <Property Name="FAQId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Question" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Reponse" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="DatePublication" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="FeedbackMentor">
          <Key>
            <PropertyRef Name="FeedbackId" />
          </Key>
          <Property Name="FeedbackId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="SessionMentoratId" Type="Int64" />
          <Property Name="Commentaire" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Note" Type="Int32" />
        </EntityType>
        <EntityType Name="FeedbackMentoree">
          <Key>
            <PropertyRef Name="FeedbackId" />
          </Key>
          <Property Name="FeedbackId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="SessionMentoratId" Type="Int64" />
          <Property Name="Commentaire" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Note" Type="Int32" />
        </EntityType>
        <EntityType Name="FichierMessage">
          <Key>
            <PropertyRef Name="FichierId" />
          </Key>
          <Property Name="FichierId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="MessageId" Type="Int32" />
          <Property Name="NomFichier" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="UrlFichier" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="Financement">
          <Key>
            <PropertyRef Name="FinancementId" />
          </Key>
          <Property Name="FinancementId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PostId" Type="Int64" />
          <Property Name="Montant" Type="Double" />
          <Property Name="Devise" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Source" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Intitulefinancement" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="PartenaireId" Type="Int32" />
          <Property Name="DateFinancement" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="Dateenreg" Type="DateTime" Precision="3" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="MOIS" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="ANNEE" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="OrganisationId" Type="Int64" />
          <Property Name="name" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="etat" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="Formation">
          <Key>
            <PropertyRef Name="FormationId" />
          </Key>
          <Property Name="FormationId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Titre" Type="String" MaxLength="150" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DateFormation" Type="DateTime" Precision="3" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Fichier" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="AuteurId" Type="Int32" />
          <Property Name="photocouverture" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="nombrepage" Type="Int32" />
          <Property Name="typeressources" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="nbrevue" Type="Int32" />
          <Property Name="nbrelike" Type="Int32" />
          <Property Name="DateCreation" Type="DateTime" Precision="3" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="DatePublication" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="OrganisationId" Type="Int64" />
          <Property Name="MOIS" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="ANNEE" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="Forum">
          <Key>
            <PropertyRef Name="ForumId" />
          </Key>
          <Property Name="ForumId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Nom" Type="String" MaxLength="150" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="Langue">
          <Key>
            <PropertyRef Name="LangueId" />
          </Key>
          <Property Name="LangueId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Code" Type="String" MaxLength="10" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Nom" Type="String" MaxLength="500" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Langu" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="statut" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="MembreProfil">
          <Key>
            <PropertyRef Name="MembreProfilId" />
          </Key>
          <Property Name="MembreProfilId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="MembreId" Type="Int64" Nullable="false" />
          <Property Name="PhotoProfil" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="facebook" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="siteweb" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="twitter" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="instagramme" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="linkedin" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="youtube" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="Biographie" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DateInscription" Type="DateTime" Precision="3" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="Membre">
          <Key>
            <PropertyRef Name="MembreId" />
          </Key>
          <Property Name="MembreId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Nom" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Prenom" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Email" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Telephone" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Sexe" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="DateNaissance" Type="DateTime" Precision="0" />
          <Property Name="ProvinceId" Type="Int32" />
          <Property Name="CommuneId" Type="Int32" />
          <Property Name="CreatedAt" Type="DateTime" Precision="7" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="province" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Adresse" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="commune" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="username" Type="String" MaxLength="150" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="motpasse" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="statut" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="IsActive" Type="Int32" />
          <Property Name="IsVerified" Type="Int32" />
          <Property Name="LastLogin" Type="DateTime" Precision="7" />
          <Property Name="RoleMembreID" Type="Int32" />
          <Property Name="PhotoProfil" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="facebook" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="siteweb" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="twitter" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="instagramme" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="linkedin" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="youtube" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="Biographie" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DateInscription" Type="DateTime" Precision="3" />
          <Property Name="ResetToken" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
          <Property Name="ResetTokenExpiry" Type="DateTime" Precision="3" />
          <Property Name="LanguePreferee" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="AccepteNotification" Type="Int32" />
        </EntityType>
        <EntityType Name="MembresOrganisation">
          <Key>
            <PropertyRef Name="MembresOrganisationId" />
          </Key>
          <Property Name="MembresOrganisationId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="MembreId" Type="Int64" Nullable="false" />
          <Property Name="OrganisationId" Type="Int64" Nullable="false" />
          <Property Name="Poste" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="DateAdhesion" Type="DateTime" Precision="3" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="RoleMembreID" Type="Int32" />
        </EntityType>
        <EntityType Name="Mentor">
          <Key>
            <PropertyRef Name="MentorId" />
          </Key>
          <Property Name="MentorId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="DomaineExpertise" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ProgrammeMentoratId" Type="Int32" />
          <Property Name="status" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="DateInscription" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="DateAccept" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="OrganisationId" Type="Int64" />
          <Property Name="rate" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="Mentore">
          <Key>
            <PropertyRef Name="MentoreId" />
          </Key>
          <Property Name="MentoreId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="ProgrammeMentoratId" Type="Int32" />
          <Property Name="status" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="DateInscription" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="DateAccept" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="OrganisationId" Type="Int64" />
          <Property Name="rate" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="menu">
          <Key>
            <PropertyRef Name="MenuID" />
          </Key>
          <Property Name="MenuID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="NomMenu" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="UrlMenu" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Icone" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="ParentID" Type="Int32" />
          <Property Name="Ordre" Type="Int32" Nullable="false" />
        </EntityType>
        <EntityType Name="menupermission">
          <Key>
            <PropertyRef Name="MenuPermissionID" />
          </Key>
          <Property Name="MenuPermissionID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="MenuID" Type="Int32" Nullable="false" />
          <Property Name="PermissionID" Type="Int32" Nullable="false" />
        </EntityType>
        <EntityType Name="Message">
          <Key>
            <PropertyRef Name="MessageId" />
          </Key>
          <Property Name="MessageId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ConversationId" Type="Int64" />
          <Property Name="SenderId" Type="Int64" />
          <Property Name="Contenu" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="AttachmentUrl" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="DateEnvoi" Type="DateTime" Precision="3" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="MessageStatu">
          <Key>
            <PropertyRef Name="MessagestatusID" />
          </Key>
          <Property Name="MessagestatusID" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="MessageId" Type="Int64" Nullable="false" />
          <Property Name="UserId" Type="Int64" Nullable="false" />
          <Property Name="IsRead" Type="Int32" />
          <Property Name="ReadAt" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="Notification">
          <Key>
            <PropertyRef Name="NotificationId" />
          </Key>
          <Property Name="NotificationId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="Titre" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Message" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DateNotification" Type="DateTime" Precision="3" />
          <Property Name="UrlRedirection" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Lu" Type="Int32" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" Nullable="false" />
        </EntityType>
        <EntityType Name="Organisation">
          <Key>
            <PropertyRef Name="OrganisationId" />
          </Key>
          <Property Name="OrganisationId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Nom" Type="String" MaxLength="150" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TypeOrganisationId" Type="Int32" Nullable="false" />
          <Property Name="ProvinceId" Type="Int32" Nullable="false" />
          <Property Name="CommuneId" Type="Int32" Nullable="false" />
          <Property Name="Adresse" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Email" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Telephone" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="SiteWeb" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Logo" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="RS" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="sigle" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Vision" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Mission" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="NbreHomme" Type="Int32" />
          <Property Name="NbreFemme" Type="Int32" />
          <Property Name="Enregistre" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="RC" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="RC_doc" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="NIF" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="NIF_doc" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="facebook" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="twitter" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="instagramme" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="linkedin" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="youtube" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="province" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="commune" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="DateCreation" Type="DateTime" Precision="0" />
          <Property Name="Statut" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="CreatedAt" Type="DateTime" Precision="7" />
          <Property Name="UpdatedAt" Type="DateTime" Precision="7" />
          <Property Name="name" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Latitude" Type="Double" />
          <Property Name="Longitude" Type="Double" />
        </EntityType>
        <EntityType Name="ParamettreApplication">
          <Key>
            <PropertyRef Name="ParamettreId" />
          </Key>
          <Property Name="ParamettreId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Cle" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Valeur" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="Partenaire">
          <Key>
            <PropertyRef Name="PartenaireId" />
          </Key>
          <Property Name="PartenaireId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Nom" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Contact" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="logo" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="etat" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="lienwebsite" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Email" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="PartenairesOrganisation">
          <Key>
            <PropertyRef Name="PartenaireorganisationId" />
          </Key>
          <Property Name="PartenaireorganisationId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Nom" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Contact" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="logo" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="etat" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="lienwebsite" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Email" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="OrganisationId" Type="Int64" />
        </EntityType>
        <EntityType Name="ParticipantConversation">
          <Key>
            <PropertyRef Name="ParticipantId" />
          </Key>
          <Property Name="ParticipantId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ConversationId" Type="Int64" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="JoinedAt" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="permission">
          <Key>
            <PropertyRef Name="PermissionID" />
          </Key>
          <Property Name="PermissionID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CodePermission" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Description" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="RoleMembreID" Type="Int32" Nullable="false" />
          <Property Name="Code_Menu" Type="Int64" Nullable="false" />
          <Property Name="Access" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="OrganisationId" Type="Int64" />
        </EntityType>
        <EntityType Name="PolitiqueConfidentialite">
          <Key>
            <PropertyRef Name="PolitiqueId" />
          </Key>
          <Property Name="PolitiqueId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Contenu" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DatePublication" Type="DateTime" Precision="3" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="Post">
          <Key>
            <PropertyRef Name="PostId" />
          </Key>
          <Property Name="PostId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Titre" Type="String" MaxLength="150" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Contenu" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="CategoriePostId" Type="Int32" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="DatePublication" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="summery" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="author" Type="String" MaxLength="500" FixedLength="false" Unicode="false" />
          <Property Name="photo" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="video" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="number_of_view" Type="Int32" />
          <Property Name="like" Type="Int32" />
          <Property Name="dislike" Type="Int32" />
          <Property Name="starttime" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="eventduration" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="eventplace" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="whoattend" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="qualificationattend" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Name="langueevent" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="externevent" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="MOIS" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="ANNEE" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="lien_isncription" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Name="pdf" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="DateCreation" Type="DateTime" Precision="3" />
          <Property Name="DateModification" Type="DateTime" Precision="3" />
          <Property Name="EstPublie" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="EstPublieEvent" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="etat" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="OrganisationId" Type="Int64" />
        </EntityType>
        <EntityType Name="PreferenceCooky">
          <Key>
            <PropertyRef Name="PreferenceId" />
          </Key>
          <Property Name="PreferenceId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="Acceptation" Type="Boolean" />
          <Property Name="DateChoix" Type="DateTime" Precision="3" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="Programmementorat">
          <Key>
            <PropertyRef Name="ProgrammeMentoratId" />
          </Key>
          <Property Name="ProgrammeMentoratId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Titre" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DateDebut" Type="DateTime" Precision="3" />
          <Property Name="DateFin" Type="DateTime" Precision="3" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="auteur" Type="String" MaxLength="150" FixedLength="false" Unicode="false" />
          <Property Name="Dateenreg" Type="DateTime" Precision="3" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="status" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="ProgrammesEtInitiative">
          <Key>
            <PropertyRef Name="ProgrammeId" />
          </Key>
          <Property Name="ProgrammeId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Nom" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DateLancement" Type="DateTime" Precision="3" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="Projet">
          <Key>
            <PropertyRef Name="ProjetId" />
          </Key>
          <Property Name="ProjetId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Titre" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="MontantProjet" Type="Double" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DateDebut" Type="DateTime" Precision="3" />
          <Property Name="DateFin" Type="DateTime" Precision="3" />
          <Property Name="OrganisationId" Type="Int64" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="status" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="etat" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="pdf" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="Province">
          <Key>
            <PropertyRef Name="ProvinceId" />
          </Key>
          <Property Name="ProvinceId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Nom" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="RepliesForum">
          <Key>
            <PropertyRef Name="RepliesForumId" />
          </Key>
          <Property Name="RepliesForumId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="SujetForumId" Type="Int64" Nullable="false" />
          <Property Name="MembreId" Type="Int64" Nullable="false" />
          <Property Name="Contenu" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DateReply" Type="DateTime" Precision="3" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="EstSolution" Type="Int32" />
          <Property Name="NombreLikes" Type="Int32" />
          <Property Name="NombreDislikes" Type="Int32" />
          <Property Name="NombreVues" Type="Int32" />
          <Property Name="statut" Type="String" MaxLength="150" FixedLength="false" Unicode="false" />
          <Property Name="etat" Type="String" MaxLength="150" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="Ressource">
          <Key>
            <PropertyRef Name="RessourceId" />
          </Key>
          <Property Name="RessourceId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Titre" Type="String" MaxLength="150" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DateFormation" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Fichier" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="AuteurId" Type="Int32" />
          <Property Name="photocouverture" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="nombrepage" Type="Int32" />
          <Property Name="typeressources" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="nbrevue" Type="Int32" />
          <Property Name="nbrelike" Type="Int32" />
          <Property Name="DateCreation" Type="DateTime" Precision="3" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="DatePublication" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="OrganisationId" Type="Int64" />
          <Property Name="MOIS" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="ANNEE" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="rolemembre">
          <Key>
            <PropertyRef Name="RoleMembreID" />
          </Key>
          <Property Name="RoleMembreID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="NomRole" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="name" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Description" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="TYPE" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="CDROLE" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="OrganisationId" Type="Int64" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="SessionMentorat">
          <Key>
            <PropertyRef Name="SessionMentoratId" />
          </Key>
          <Property Name="SessionMentoratId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ProgrammeMentoratId" Type="Int32" />
          <Property Name="MentorId" Type="Int32" />
          <Property Name="MentoreId" Type="Int32" />
          <Property Name="DateSession" Type="DateTime" Precision="3" />
          <Property Name="Sujet" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Notes" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="StatutUtilisateur">
          <Key>
            <PropertyRef Name="StatutId" />
          </Key>
          <Property Name="StatutId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="Libelle" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="Subscription">
          <Key>
            <PropertyRef Name="SubscriptionId" />
          </Key>
          <Property Name="SubscriptionId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="Email" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="DateAbonnement" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="SujetForum">
          <Key>
            <PropertyRef Name="SujetForumId" />
          </Key>
          <Property Name="SujetForumId" Type="Int64" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Titre" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Contenu" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="MembreId" Type="Int64" Nullable="false" />
          <Property Name="DateCreation" Type="DateTime" Precision="3" />
          <Property Name="NombreVues" Type="Int32" />
          <Property Name="Statut" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="etat" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="SupportFormation">
          <Key>
            <PropertyRef Name="SupportFormationId" />
          </Key>
          <Property Name="SupportFormationId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="FormationId" Type="Int64" Nullable="false" />
          <Property Name="Titre" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Fichier" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="DateCreation" Type="DateTime" Precision="3" />
          <Property Name="statut" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="DatePublication" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="TelechargementRessource">
          <Key>
            <PropertyRef Name="TelechargementId" />
          </Key>
          <Property Name="TelechargementId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="RessourceId" Type="Int64" />
          <Property Name="MembreId" Type="Int64" />
          <Property Name="DateTelechargement" Type="DateTime" Precision="3" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="Traduction">
          <Key>
            <PropertyRef Name="TraductionId" />
          </Key>
          <Property Name="TraductionId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Cle" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="LangueId" Type="Int32" Nullable="false" />
          <Property Name="Texte" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="TypeOrganisation">
          <Key>
            <PropertyRef Name="TypeOrganisationId" />
          </Key>
          <Property Name="TypeOrganisationId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Libelle" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityContainer Name="Connection" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="ActiviteProjets" EntityType="Self.ActiviteProjet" />
          <EntitySet Name="AvisRessources" EntityType="Self.AvisRessource" />
          <EntitySet Name="budgets_activite" EntityType="Self.budgets_activite" />
          <EntitySet Name="CategoriePosts" EntityType="Self.CategoriePost" />
          <EntitySet Name="CommentPosts" EntityType="Self.CommentPost" />
          <EntitySet Name="Communes" EntityType="Self.Commune" />
          <EntitySet Name="ConditionUtilisations" EntityType="Self.ConditionUtilisation" />
          <EntitySet Name="Conversations" EntityType="Self.Conversation" />
          <EntitySet Name="ConversationParticipants" EntityType="Self.ConversationParticipant" />
          <EntitySet Name="DomaineFormations" EntityType="Self.DomaineFormation" />
          <EntitySet Name="DomaineInterventions" EntityType="Self.DomaineIntervention" />
          <EntitySet Name="DomaineInterventionOrganisations" EntityType="Self.DomaineInterventionOrganisation" />
          <EntitySet Name="DomainePosts" EntityType="Self.DomainePost" />
          <EntitySet Name="DomaineProjets" EntityType="Self.DomaineProjet" />
          <EntitySet Name="DomaineRessources" EntityType="Self.DomaineRessource" />
          <EntitySet Name="FAQs" EntityType="Self.FAQ" />
          <EntitySet Name="FeedbackMentors" EntityType="Self.FeedbackMentor" />
          <EntitySet Name="FeedbackMentorees" EntityType="Self.FeedbackMentoree" />
          <EntitySet Name="FichierMessages" EntityType="Self.FichierMessage" />
          <EntitySet Name="Financements" EntityType="Self.Financement" />
          <EntitySet Name="Formations" EntityType="Self.Formation" />
          <EntitySet Name="Fora" EntityType="Self.Forum" />
          <EntitySet Name="Langues" EntityType="Self.Langue" />
          <EntitySet Name="MembreProfils" EntityType="Self.MembreProfil" />
          <EntitySet Name="Membres" EntityType="Self.Membre" />
          <EntitySet Name="MembresOrganisations" EntityType="Self.MembresOrganisation" />
          <EntitySet Name="Mentors" EntityType="Self.Mentor" />
          <EntitySet Name="Mentores" EntityType="Self.Mentore" />
          <EntitySet Name="menus" EntityType="Self.menu" />
          <EntitySet Name="menupermissions" EntityType="Self.menupermission" />
          <EntitySet Name="Messages" EntityType="Self.Message" />
          <EntitySet Name="MessageStatus" EntityType="Self.MessageStatu" />
          <EntitySet Name="Notifications" EntityType="Self.Notification" />
          <EntitySet Name="Organisations" EntityType="Self.Organisation" />
          <EntitySet Name="ParamettreApplications" EntityType="Self.ParamettreApplication" />
          <EntitySet Name="Partenaires" EntityType="Self.Partenaire" />
          <EntitySet Name="PartenairesOrganisations" EntityType="Self.PartenairesOrganisation" />
          <EntitySet Name="ParticipantConversations" EntityType="Self.ParticipantConversation" />
          <EntitySet Name="permissions" EntityType="Self.permission" />
          <EntitySet Name="PolitiqueConfidentialites" EntityType="Self.PolitiqueConfidentialite" />
          <EntitySet Name="Posts" EntityType="Self.Post" />
          <EntitySet Name="PreferenceCookies" EntityType="Self.PreferenceCooky" />
          <EntitySet Name="Programmementorats" EntityType="Self.Programmementorat" />
          <EntitySet Name="ProgrammesEtInitiatives" EntityType="Self.ProgrammesEtInitiative" />
          <EntitySet Name="Projets" EntityType="Self.Projet" />
          <EntitySet Name="Provinces" EntityType="Self.Province" />
          <EntitySet Name="RepliesForums" EntityType="Self.RepliesForum" />
          <EntitySet Name="Ressources" EntityType="Self.Ressource" />
          <EntitySet Name="rolemembres" EntityType="Self.rolemembre" />
          <EntitySet Name="SessionMentorats" EntityType="Self.SessionMentorat" />
          <EntitySet Name="StatutUtilisateurs" EntityType="Self.StatutUtilisateur" />
          <EntitySet Name="Subscriptions" EntityType="Self.Subscription" />
          <EntitySet Name="SujetForums" EntityType="Self.SujetForum" />
          <EntitySet Name="SupportFormations" EntityType="Self.SupportFormation" />
          <EntitySet Name="TelechargementRessources" EntityType="Self.TelechargementRessource" />
          <EntitySet Name="Traductions" EntityType="Self.Traduction" />
          <EntitySet Name="TypeOrganisations" EntityType="Self.TypeOrganisation" />
        </EntityContainer>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="LincomDBModelStoreContainer" CdmEntityContainer="Connection">
          <EntitySetMapping Name="ActiviteProjets">
            <EntityTypeMapping TypeName="LincomDBModel.ActiviteProjet">
              <MappingFragment StoreEntitySet="ActiviteProjet">
                <ScalarProperty Name="ActiviteProjetId" ColumnName="ActiviteProjetId" />
                <ScalarProperty Name="PostId" ColumnName="PostId" />
                <ScalarProperty Name="Titre" ColumnName="Titre" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="DateActivite" ColumnName="DateActivite" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
                <ScalarProperty Name="photo1" ColumnName="photo1" />
                <ScalarProperty Name="photo2" ColumnName="photo2" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="DateActiviteEnreg" ColumnName="DateActiviteEnreg" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="summery" ColumnName="summery" />
                <ScalarProperty Name="number_of_view" ColumnName="number_of_view" />
                <ScalarProperty Name="like" ColumnName="like" />
                <ScalarProperty Name="dislike" ColumnName="dislike" />
                <ScalarProperty Name="MOIS" ColumnName="MOIS" />
                <ScalarProperty Name="ANNEE" ColumnName="ANNEE" />
                <ScalarProperty Name="DateCreation" ColumnName="DateCreation" />
                <ScalarProperty Name="etat" ColumnName="etat" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="AvisRessources">
            <EntityTypeMapping TypeName="LincomDBModel.AvisRessource">
              <MappingFragment StoreEntitySet="AvisRessources">
                <ScalarProperty Name="AvisId" ColumnName="AvisId" />
                <ScalarProperty Name="RessourceId" ColumnName="RessourceId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="Note" ColumnName="Note" />
                <ScalarProperty Name="Commentaire" ColumnName="Commentaire" />
                <ScalarProperty Name="DateAvis" ColumnName="DateAvis" />
                <ScalarProperty Name="statut" ColumnName="statut" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="budgets_activite">
            <EntityTypeMapping TypeName="LincomDBModel.budgets_activite">
              <MappingFragment StoreEntitySet="budgets_activite">
                <ScalarProperty Name="id_budget" ColumnName="id_budget" />
                <ScalarProperty Name="ActiviteProjetId" ColumnName="ActiviteProjetId" />
                <ScalarProperty Name="montant_prevu" ColumnName="montant_prevu" />
                <ScalarProperty Name="montant_realise" ColumnName="montant_realise" />
                <ScalarProperty Name="observation" ColumnName="observation" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CategoriePosts">
            <EntityTypeMapping TypeName="LincomDBModel.CategoriePost">
              <MappingFragment StoreEntitySet="CategoriePost">
                <ScalarProperty Name="CategoriePostId" ColumnName="CategoriePostId" />
                <ScalarProperty Name="Libelle" ColumnName="Libelle" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CommentPosts">
            <EntityTypeMapping TypeName="LincomDBModel.CommentPost">
              <MappingFragment StoreEntitySet="CommentPost">
                <ScalarProperty Name="CommentPostId" ColumnName="CommentPostId" />
                <ScalarProperty Name="PostId" ColumnName="PostId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="Contenu" ColumnName="Contenu" />
                <ScalarProperty Name="DateCommentaire" ColumnName="DateCommentaire" />
                <ScalarProperty Name="EstVisible" ColumnName="EstVisible" />
                <ScalarProperty Name="Nbrevue" ColumnName="Nbrevue" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Communes">
            <EntityTypeMapping TypeName="LincomDBModel.Commune">
              <MappingFragment StoreEntitySet="Commune">
                <ScalarProperty Name="CommuneId" ColumnName="CommuneId" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="ProvinceId" ColumnName="ProvinceId" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConditionUtilisations">
            <EntityTypeMapping TypeName="LincomDBModel.ConditionUtilisation">
              <MappingFragment StoreEntitySet="ConditionUtilisation">
                <ScalarProperty Name="ConditionId" ColumnName="ConditionId" />
                <ScalarProperty Name="Contenu" ColumnName="Contenu" />
                <ScalarProperty Name="DatePublication" ColumnName="DatePublication" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Conversations">
            <EntityTypeMapping TypeName="LincomDBModel.Conversation">
              <MappingFragment StoreEntitySet="Conversation">
                <ScalarProperty Name="ConversationId" ColumnName="ConversationId" />
                <ScalarProperty Name="Sujet" ColumnName="Sujet" />
                <ScalarProperty Name="IsGroup" ColumnName="IsGroup" />
                <ScalarProperty Name="CreatedAt" ColumnName="CreatedAt" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConversationParticipants">
            <EntityTypeMapping TypeName="LincomDBModel.ConversationParticipant">
              <MappingFragment StoreEntitySet="ConversationParticipants">
                <ScalarProperty Name="ConversationparticipantsID" ColumnName="ConversationparticipantsID" />
                <ScalarProperty Name="ConversationId" ColumnName="ConversationId" />
                <ScalarProperty Name="UserId" ColumnName="UserId" />
                <ScalarProperty Name="JoinedAt" ColumnName="JoinedAt" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DomaineFormations">
            <EntityTypeMapping TypeName="LincomDBModel.DomaineFormation">
              <MappingFragment StoreEntitySet="DomaineFormation">
                <ScalarProperty Name="DomaineFormationId" ColumnName="DomaineFormationId" />
                <ScalarProperty Name="FormationId" ColumnName="FormationId" />
                <ScalarProperty Name="DomaineInterventionId" ColumnName="DomaineInterventionId" />
                <ScalarProperty Name="DateCreation" ColumnName="DateCreation" />
                <ScalarProperty Name="statut" ColumnName="statut" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DomaineInterventions">
            <EntityTypeMapping TypeName="LincomDBModel.DomaineIntervention">
              <MappingFragment StoreEntitySet="DomaineIntervention">
                <ScalarProperty Name="DomaineInterventionId" ColumnName="DomaineInterventionId" />
                <ScalarProperty Name="Libelle" ColumnName="Libelle" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DomaineInterventionOrganisations">
            <EntityTypeMapping TypeName="LincomDBModel.DomaineInterventionOrganisation">
              <MappingFragment StoreEntitySet="DomaineInterventionOrganisation">
                <ScalarProperty Name="DomaineInterventionOrganisationId" ColumnName="DomaineInterventionOrganisationId" />
                <ScalarProperty Name="DomaineInterventionId" ColumnName="DomaineInterventionId" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="statut" ColumnName="statut" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DomainePosts">
            <EntityTypeMapping TypeName="LincomDBModel.DomainePost">
              <MappingFragment StoreEntitySet="DomainePost">
                <ScalarProperty Name="DomainePostId" ColumnName="DomainePostId" />
                <ScalarProperty Name="PostId" ColumnName="PostId" />
                <ScalarProperty Name="DomaineInterventionOrganisationId" ColumnName="DomaineInterventionOrganisationId" />
                <ScalarProperty Name="DateCreation" ColumnName="DateCreation" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DomaineProjets">
            <EntityTypeMapping TypeName="LincomDBModel.DomaineProjet">
              <MappingFragment StoreEntitySet="DomaineProjet">
                <ScalarProperty Name="DomaineProjetId" ColumnName="DomaineProjetId" />
                <ScalarProperty Name="ProjetId" ColumnName="ProjetId" />
                <ScalarProperty Name="DomaineInterventionId" ColumnName="DomaineInterventionId" />
                <ScalarProperty Name="DateCreation" ColumnName="DateCreation" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DomaineRessources">
            <EntityTypeMapping TypeName="LincomDBModel.DomaineRessource">
              <MappingFragment StoreEntitySet="DomaineRessources">
                <ScalarProperty Name="DomaineResourceId" ColumnName="DomaineResourceId" />
                <ScalarProperty Name="RessourceId" ColumnName="RessourceId" />
                <ScalarProperty Name="DomaineInterventionId" ColumnName="DomaineInterventionId" />
                <ScalarProperty Name="DateCreation" ColumnName="DateCreation" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="FAQs">
            <EntityTypeMapping TypeName="LincomDBModel.FAQ">
              <MappingFragment StoreEntitySet="FAQ">
                <ScalarProperty Name="FAQId" ColumnName="FAQId" />
                <ScalarProperty Name="Question" ColumnName="Question" />
                <ScalarProperty Name="Reponse" ColumnName="Reponse" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="DatePublication" ColumnName="DatePublication" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="FeedbackMentors">
            <EntityTypeMapping TypeName="LincomDBModel.FeedbackMentor">
              <MappingFragment StoreEntitySet="FeedbackMentor">
                <ScalarProperty Name="FeedbackId" ColumnName="FeedbackId" />
                <ScalarProperty Name="SessionMentoratId" ColumnName="SessionMentoratId" />
                <ScalarProperty Name="Commentaire" ColumnName="Commentaire" />
                <ScalarProperty Name="Note" ColumnName="Note" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="FeedbackMentorees">
            <EntityTypeMapping TypeName="LincomDBModel.FeedbackMentoree">
              <MappingFragment StoreEntitySet="FeedbackMentoree">
                <ScalarProperty Name="FeedbackId" ColumnName="FeedbackId" />
                <ScalarProperty Name="SessionMentoratId" ColumnName="SessionMentoratId" />
                <ScalarProperty Name="Commentaire" ColumnName="Commentaire" />
                <ScalarProperty Name="Note" ColumnName="Note" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="FichierMessages">
            <EntityTypeMapping TypeName="LincomDBModel.FichierMessage">
              <MappingFragment StoreEntitySet="FichierMessage">
                <ScalarProperty Name="FichierId" ColumnName="FichierId" />
                <ScalarProperty Name="MessageId" ColumnName="MessageId" />
                <ScalarProperty Name="NomFichier" ColumnName="NomFichier" />
                <ScalarProperty Name="UrlFichier" ColumnName="UrlFichier" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Financements">
            <EntityTypeMapping TypeName="LincomDBModel.Financement">
              <MappingFragment StoreEntitySet="Financement">
                <ScalarProperty Name="FinancementId" ColumnName="FinancementId" />
                <ScalarProperty Name="PostId" ColumnName="PostId" />
                <ScalarProperty Name="Montant" ColumnName="Montant" />
                <ScalarProperty Name="Devise" ColumnName="Devise" />
                <ScalarProperty Name="Source" ColumnName="Source" />
                <ScalarProperty Name="Intitulefinancement" ColumnName="Intitulefinancement" />
                <ScalarProperty Name="PartenaireId" ColumnName="PartenaireId" />
                <ScalarProperty Name="DateFinancement" ColumnName="DateFinancement" />
                <ScalarProperty Name="Dateenreg" ColumnName="Dateenreg" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="MOIS" ColumnName="MOIS" />
                <ScalarProperty Name="ANNEE" ColumnName="ANNEE" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="etat" ColumnName="etat" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Formations">
            <EntityTypeMapping TypeName="LincomDBModel.Formation">
              <MappingFragment StoreEntitySet="Formation">
                <ScalarProperty Name="FormationId" ColumnName="FormationId" />
                <ScalarProperty Name="Titre" ColumnName="Titre" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="DateFormation" ColumnName="DateFormation" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="Fichier" ColumnName="Fichier" />
                <ScalarProperty Name="AuteurId" ColumnName="AuteurId" />
                <ScalarProperty Name="photocouverture" ColumnName="photocouverture" />
                <ScalarProperty Name="nombrepage" ColumnName="nombrepage" />
                <ScalarProperty Name="typeressources" ColumnName="typeressources" />
                <ScalarProperty Name="nbrevue" ColumnName="nbrevue" />
                <ScalarProperty Name="nbrelike" ColumnName="nbrelike" />
                <ScalarProperty Name="DateCreation" ColumnName="DateCreation" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="DatePublication" ColumnName="DatePublication" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
                <ScalarProperty Name="MOIS" ColumnName="MOIS" />
                <ScalarProperty Name="ANNEE" ColumnName="ANNEE" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Fora">
            <EntityTypeMapping TypeName="LincomDBModel.Forum">
              <MappingFragment StoreEntitySet="Forum">
                <ScalarProperty Name="ForumId" ColumnName="ForumId" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Langues">
            <EntityTypeMapping TypeName="LincomDBModel.Langue">
              <MappingFragment StoreEntitySet="Langue">
                <ScalarProperty Name="LangueId" ColumnName="LangueId" />
                <ScalarProperty Name="Code" ColumnName="Code" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="Langu" ColumnName="Langu" />
                <ScalarProperty Name="statut" ColumnName="statut" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="MembreProfils">
            <EntityTypeMapping TypeName="LincomDBModel.MembreProfil">
              <MappingFragment StoreEntitySet="MembreProfil">
                <ScalarProperty Name="MembreProfilId" ColumnName="MembreProfilId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="PhotoProfil" ColumnName="PhotoProfil" />
                <ScalarProperty Name="facebook" ColumnName="facebook" />
                <ScalarProperty Name="siteweb" ColumnName="siteweb" />
                <ScalarProperty Name="twitter" ColumnName="twitter" />
                <ScalarProperty Name="instagramme" ColumnName="instagramme" />
                <ScalarProperty Name="linkedin" ColumnName="linkedin" />
                <ScalarProperty Name="youtube" ColumnName="youtube" />
                <ScalarProperty Name="Biographie" ColumnName="Biographie" />
                <ScalarProperty Name="DateInscription" ColumnName="DateInscription" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Membres">
            <EntityTypeMapping TypeName="LincomDBModel.Membre">
              <MappingFragment StoreEntitySet="Membres">
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="Prenom" ColumnName="Prenom" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="Telephone" ColumnName="Telephone" />
                <ScalarProperty Name="Sexe" ColumnName="Sexe" />
                <ScalarProperty Name="DateNaissance" ColumnName="DateNaissance" />
                <ScalarProperty Name="ProvinceId" ColumnName="ProvinceId" />
                <ScalarProperty Name="CommuneId" ColumnName="CommuneId" />
                <ScalarProperty Name="CreatedAt" ColumnName="CreatedAt" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="province" ColumnName="province" />
                <ScalarProperty Name="Adresse" ColumnName="Adresse" />
                <ScalarProperty Name="commune" ColumnName="commune" />
                <ScalarProperty Name="username" ColumnName="username" />
                <ScalarProperty Name="motpasse" ColumnName="motpasse" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="IsVerified" ColumnName="IsVerified" />
                <ScalarProperty Name="LastLogin" ColumnName="LastLogin" />
                <ScalarProperty Name="RoleMembreID" ColumnName="RoleMembreID" />
                <ScalarProperty Name="PhotoProfil" ColumnName="PhotoProfil" />
                <ScalarProperty Name="facebook" ColumnName="facebook" />
                <ScalarProperty Name="siteweb" ColumnName="siteweb" />
                <ScalarProperty Name="twitter" ColumnName="twitter" />
                <ScalarProperty Name="instagramme" ColumnName="instagramme" />
                <ScalarProperty Name="linkedin" ColumnName="linkedin" />
                <ScalarProperty Name="youtube" ColumnName="youtube" />
                <ScalarProperty Name="Biographie" ColumnName="Biographie" />
                <ScalarProperty Name="DateInscription" ColumnName="DateInscription" />
                <ScalarProperty Name="ResetToken" ColumnName="ResetToken" />
                <ScalarProperty Name="ResetTokenExpiry" ColumnName="ResetTokenExpiry" />
                <ScalarProperty Name="LanguePreferee" ColumnName="LanguePreferee" />
                <ScalarProperty Name="AccepteNotification" ColumnName="AccepteNotification" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="MembresOrganisations">
            <EntityTypeMapping TypeName="LincomDBModel.MembresOrganisation">
              <MappingFragment StoreEntitySet="MembresOrganisation">
                <ScalarProperty Name="MembresOrganisationId" ColumnName="MembresOrganisationId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
                <ScalarProperty Name="Poste" ColumnName="Poste" />
                <ScalarProperty Name="DateAdhesion" ColumnName="DateAdhesion" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="RoleMembreID" ColumnName="RoleMembreID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Mentors">
            <EntityTypeMapping TypeName="LincomDBModel.Mentor">
              <MappingFragment StoreEntitySet="Mentor">
                <ScalarProperty Name="MentorId" ColumnName="MentorId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="DomaineExpertise" ColumnName="DomaineExpertise" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="ProgrammeMentoratId" ColumnName="ProgrammeMentoratId" />
                <ScalarProperty Name="status" ColumnName="status" />
                <ScalarProperty Name="DateInscription" ColumnName="DateInscription" />
                <ScalarProperty Name="DateAccept" ColumnName="DateAccept" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
                <ScalarProperty Name="rate" ColumnName="rate" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Mentores">
            <EntityTypeMapping TypeName="LincomDBModel.Mentore">
              <MappingFragment StoreEntitySet="Mentore">
                <ScalarProperty Name="MentoreId" ColumnName="MentoreId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="ProgrammeMentoratId" ColumnName="ProgrammeMentoratId" />
                <ScalarProperty Name="status" ColumnName="status" />
                <ScalarProperty Name="DateInscription" ColumnName="DateInscription" />
                <ScalarProperty Name="DateAccept" ColumnName="DateAccept" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
                <ScalarProperty Name="rate" ColumnName="rate" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="menus">
            <EntityTypeMapping TypeName="LincomDBModel.menu">
              <MappingFragment StoreEntitySet="menu">
                <ScalarProperty Name="MenuID" ColumnName="MenuID" />
                <ScalarProperty Name="NomMenu" ColumnName="NomMenu" />
                <ScalarProperty Name="UrlMenu" ColumnName="UrlMenu" />
                <ScalarProperty Name="Icone" ColumnName="Icone" />
                <ScalarProperty Name="ParentID" ColumnName="ParentID" />
                <ScalarProperty Name="Ordre" ColumnName="Ordre" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="menupermissions">
            <EntityTypeMapping TypeName="LincomDBModel.menupermission">
              <MappingFragment StoreEntitySet="menupermission">
                <ScalarProperty Name="MenuPermissionID" ColumnName="MenuPermissionID" />
                <ScalarProperty Name="MenuID" ColumnName="MenuID" />
                <ScalarProperty Name="PermissionID" ColumnName="PermissionID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Messages">
            <EntityTypeMapping TypeName="LincomDBModel.Message">
              <MappingFragment StoreEntitySet="Message">
                <ScalarProperty Name="MessageId" ColumnName="MessageId" />
                <ScalarProperty Name="ConversationId" ColumnName="ConversationId" />
                <ScalarProperty Name="SenderId" ColumnName="SenderId" />
                <ScalarProperty Name="Contenu" ColumnName="Contenu" />
                <ScalarProperty Name="AttachmentUrl" ColumnName="AttachmentUrl" />
                <ScalarProperty Name="DateEnvoi" ColumnName="DateEnvoi" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="MessageStatus">
            <EntityTypeMapping TypeName="LincomDBModel.MessageStatu">
              <MappingFragment StoreEntitySet="MessageStatus">
                <ScalarProperty Name="MessagestatusID" ColumnName="MessagestatusID" />
                <ScalarProperty Name="MessageId" ColumnName="MessageId" />
                <ScalarProperty Name="UserId" ColumnName="UserId" />
                <ScalarProperty Name="IsRead" ColumnName="IsRead" />
                <ScalarProperty Name="ReadAt" ColumnName="ReadAt" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Notifications">
            <EntityTypeMapping TypeName="LincomDBModel.Notification">
              <MappingFragment StoreEntitySet="Notification">
                <ScalarProperty Name="NotificationId" ColumnName="NotificationId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="Titre" ColumnName="Titre" />
                <ScalarProperty Name="Message" ColumnName="Message" />
                <ScalarProperty Name="DateNotification" ColumnName="DateNotification" />
                <ScalarProperty Name="UrlRedirection" ColumnName="UrlRedirection" />
                <ScalarProperty Name="Lu" ColumnName="Lu" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="statut" ColumnName="statut" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Organisations">
            <EntityTypeMapping TypeName="LincomDBModel.Organisation">
              <MappingFragment StoreEntitySet="Organisation">
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="TypeOrganisationId" ColumnName="TypeOrganisationId" />
                <ScalarProperty Name="ProvinceId" ColumnName="ProvinceId" />
                <ScalarProperty Name="CommuneId" ColumnName="CommuneId" />
                <ScalarProperty Name="Adresse" ColumnName="Adresse" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="Telephone" ColumnName="Telephone" />
                <ScalarProperty Name="SiteWeb" ColumnName="SiteWeb" />
                <ScalarProperty Name="Logo" ColumnName="Logo" />
                <ScalarProperty Name="RS" ColumnName="RS" />
                <ScalarProperty Name="sigle" ColumnName="sigle" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="Vision" ColumnName="Vision" />
                <ScalarProperty Name="Mission" ColumnName="Mission" />
                <ScalarProperty Name="NbreHomme" ColumnName="NbreHomme" />
                <ScalarProperty Name="NbreFemme" ColumnName="NbreFemme" />
                <ScalarProperty Name="Enregistre" ColumnName="Enregistre" />
                <ScalarProperty Name="RC" ColumnName="RC" />
                <ScalarProperty Name="RC_doc" ColumnName="RC_doc" />
                <ScalarProperty Name="NIF" ColumnName="NIF" />
                <ScalarProperty Name="NIF_doc" ColumnName="NIF_doc" />
                <ScalarProperty Name="facebook" ColumnName="facebook" />
                <ScalarProperty Name="twitter" ColumnName="twitter" />
                <ScalarProperty Name="instagramme" ColumnName="instagramme" />
                <ScalarProperty Name="linkedin" ColumnName="linkedin" />
                <ScalarProperty Name="youtube" ColumnName="youtube" />
                <ScalarProperty Name="province" ColumnName="province" />
                <ScalarProperty Name="commune" ColumnName="commune" />
                <ScalarProperty Name="DateCreation" ColumnName="DateCreation" />
                <ScalarProperty Name="Statut" ColumnName="Statut" />
                <ScalarProperty Name="CreatedAt" ColumnName="CreatedAt" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UpdatedAt" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="Latitude" ColumnName="Latitude" />
                <ScalarProperty Name="Longitude" ColumnName="Longitude" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ParamettreApplications">
            <EntityTypeMapping TypeName="LincomDBModel.ParamettreApplication">
              <MappingFragment StoreEntitySet="ParamettreApplication">
                <ScalarProperty Name="ParamettreId" ColumnName="ParamettreId" />
                <ScalarProperty Name="Cle" ColumnName="Cle" />
                <ScalarProperty Name="Valeur" ColumnName="Valeur" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Partenaires">
            <EntityTypeMapping TypeName="LincomDBModel.Partenaire">
              <MappingFragment StoreEntitySet="Partenaires">
                <ScalarProperty Name="PartenaireId" ColumnName="PartenaireId" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="Contact" ColumnName="Contact" />
                <ScalarProperty Name="logo" ColumnName="logo" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="etat" ColumnName="etat" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="lienwebsite" ColumnName="lienwebsite" />
                <ScalarProperty Name="Email" ColumnName="Email" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PartenairesOrganisations">
            <EntityTypeMapping TypeName="LincomDBModel.PartenairesOrganisation">
              <MappingFragment StoreEntitySet="PartenairesOrganisation">
                <ScalarProperty Name="PartenaireorganisationId" ColumnName="PartenaireorganisationId" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="Contact" ColumnName="Contact" />
                <ScalarProperty Name="logo" ColumnName="logo" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="etat" ColumnName="etat" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="lienwebsite" ColumnName="lienwebsite" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ParticipantConversations">
            <EntityTypeMapping TypeName="LincomDBModel.ParticipantConversation">
              <MappingFragment StoreEntitySet="ParticipantConversation">
                <ScalarProperty Name="ParticipantId" ColumnName="ParticipantId" />
                <ScalarProperty Name="ConversationId" ColumnName="ConversationId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="JoinedAt" ColumnName="JoinedAt" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="permissions">
            <EntityTypeMapping TypeName="LincomDBModel.permission">
              <MappingFragment StoreEntitySet="permission">
                <ScalarProperty Name="PermissionID" ColumnName="PermissionID" />
                <ScalarProperty Name="CodePermission" ColumnName="CodePermission" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="RoleMembreID" ColumnName="RoleMembreID" />
                <ScalarProperty Name="Code_Menu" ColumnName="Code_Menu" />
                <ScalarProperty Name="Access" ColumnName="Access" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PolitiqueConfidentialites">
            <EntityTypeMapping TypeName="LincomDBModel.PolitiqueConfidentialite">
              <MappingFragment StoreEntitySet="PolitiqueConfidentialite">
                <ScalarProperty Name="PolitiqueId" ColumnName="PolitiqueId" />
                <ScalarProperty Name="Contenu" ColumnName="Contenu" />
                <ScalarProperty Name="DatePublication" ColumnName="DatePublication" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Posts">
            <EntityTypeMapping TypeName="LincomDBModel.Post">
              <MappingFragment StoreEntitySet="Post">
                <ScalarProperty Name="PostId" ColumnName="PostId" />
                <ScalarProperty Name="Titre" ColumnName="Titre" />
                <ScalarProperty Name="Contenu" ColumnName="Contenu" />
                <ScalarProperty Name="CategoriePostId" ColumnName="CategoriePostId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="DatePublication" ColumnName="DatePublication" />
                <ScalarProperty Name="summery" ColumnName="summery" />
                <ScalarProperty Name="author" ColumnName="author" />
                <ScalarProperty Name="photo" ColumnName="photo" />
                <ScalarProperty Name="video" ColumnName="video" />
                <ScalarProperty Name="number_of_view" ColumnName="number_of_view" />
                <ScalarProperty Name="like" ColumnName="like" />
                <ScalarProperty Name="dislike" ColumnName="dislike" />
                <ScalarProperty Name="starttime" ColumnName="starttime" />
                <ScalarProperty Name="eventduration" ColumnName="eventduration" />
                <ScalarProperty Name="eventplace" ColumnName="eventplace" />
                <ScalarProperty Name="whoattend" ColumnName="whoattend" />
                <ScalarProperty Name="qualificationattend" ColumnName="qualificationattend" />
                <ScalarProperty Name="langueevent" ColumnName="langueevent" />
                <ScalarProperty Name="externevent" ColumnName="externevent" />
                <ScalarProperty Name="MOIS" ColumnName="MOIS" />
                <ScalarProperty Name="ANNEE" ColumnName="ANNEE" />
                <ScalarProperty Name="lien_isncription" ColumnName="lien_isncription" />
                <ScalarProperty Name="pdf" ColumnName="pdf" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="DateCreation" ColumnName="DateCreation" />
                <ScalarProperty Name="DateModification" ColumnName="DateModification" />
                <ScalarProperty Name="EstPublie" ColumnName="EstPublie" />
                <ScalarProperty Name="EstPublieEvent" ColumnName="EstPublieEvent" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="etat" ColumnName="etat" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PreferenceCookies">
            <EntityTypeMapping TypeName="LincomDBModel.PreferenceCooky">
              <MappingFragment StoreEntitySet="PreferenceCookies">
                <ScalarProperty Name="PreferenceId" ColumnName="PreferenceId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="Acceptation" ColumnName="Acceptation" />
                <ScalarProperty Name="DateChoix" ColumnName="DateChoix" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Programmementorats">
            <EntityTypeMapping TypeName="LincomDBModel.Programmementorat">
              <MappingFragment StoreEntitySet="Programmementorat">
                <ScalarProperty Name="ProgrammeMentoratId" ColumnName="ProgrammeMentoratId" />
                <ScalarProperty Name="Titre" ColumnName="Titre" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="DateDebut" ColumnName="DateDebut" />
                <ScalarProperty Name="DateFin" ColumnName="DateFin" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="auteur" ColumnName="auteur" />
                <ScalarProperty Name="Dateenreg" ColumnName="Dateenreg" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="status" ColumnName="status" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProgrammesEtInitiatives">
            <EntityTypeMapping TypeName="LincomDBModel.ProgrammesEtInitiative">
              <MappingFragment StoreEntitySet="ProgrammesEtInitiative">
                <ScalarProperty Name="ProgrammeId" ColumnName="ProgrammeId" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="DateLancement" ColumnName="DateLancement" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="statut" ColumnName="statut" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Projets">
            <EntityTypeMapping TypeName="LincomDBModel.Projet">
              <MappingFragment StoreEntitySet="Projet">
                <ScalarProperty Name="ProjetId" ColumnName="ProjetId" />
                <ScalarProperty Name="Titre" ColumnName="Titre" />
                <ScalarProperty Name="MontantProjet" ColumnName="MontantProjet" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="DateDebut" ColumnName="DateDebut" />
                <ScalarProperty Name="DateFin" ColumnName="DateFin" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="status" ColumnName="status" />
                <ScalarProperty Name="etat" ColumnName="etat" />
                <ScalarProperty Name="pdf" ColumnName="pdf" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Provinces">
            <EntityTypeMapping TypeName="LincomDBModel.Province">
              <MappingFragment StoreEntitySet="Provinces">
                <ScalarProperty Name="ProvinceId" ColumnName="ProvinceId" />
                <ScalarProperty Name="Nom" ColumnName="Nom" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RepliesForums">
            <EntityTypeMapping TypeName="LincomDBModel.RepliesForum">
              <MappingFragment StoreEntitySet="RepliesForum">
                <ScalarProperty Name="RepliesForumId" ColumnName="RepliesForumId" />
                <ScalarProperty Name="SujetForumId" ColumnName="SujetForumId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="Contenu" ColumnName="Contenu" />
                <ScalarProperty Name="DateReply" ColumnName="DateReply" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="EstSolution" ColumnName="EstSolution" />
                <ScalarProperty Name="NombreLikes" ColumnName="NombreLikes" />
                <ScalarProperty Name="NombreDislikes" ColumnName="NombreDislikes" />
                <ScalarProperty Name="NombreVues" ColumnName="NombreVues" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="etat" ColumnName="etat" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Ressources">
            <EntityTypeMapping TypeName="LincomDBModel.Ressource">
              <MappingFragment StoreEntitySet="Ressources">
                <ScalarProperty Name="RessourceId" ColumnName="RessourceId" />
                <ScalarProperty Name="Titre" ColumnName="Titre" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="DateFormation" ColumnName="DateFormation" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="Fichier" ColumnName="Fichier" />
                <ScalarProperty Name="AuteurId" ColumnName="AuteurId" />
                <ScalarProperty Name="photocouverture" ColumnName="photocouverture" />
                <ScalarProperty Name="nombrepage" ColumnName="nombrepage" />
                <ScalarProperty Name="typeressources" ColumnName="typeressources" />
                <ScalarProperty Name="nbrevue" ColumnName="nbrevue" />
                <ScalarProperty Name="nbrelike" ColumnName="nbrelike" />
                <ScalarProperty Name="DateCreation" ColumnName="DateCreation" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="DatePublication" ColumnName="DatePublication" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
                <ScalarProperty Name="MOIS" ColumnName="MOIS" />
                <ScalarProperty Name="ANNEE" ColumnName="ANNEE" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="rolemembres">
            <EntityTypeMapping TypeName="LincomDBModel.rolemembre">
              <MappingFragment StoreEntitySet="rolemembre">
                <ScalarProperty Name="RoleMembreID" ColumnName="RoleMembreID" />
                <ScalarProperty Name="NomRole" ColumnName="NomRole" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="TYPE" ColumnName="TYPE" />
                <ScalarProperty Name="CDROLE" ColumnName="CDROLE" />
                <ScalarProperty Name="OrganisationId" ColumnName="OrganisationId" />
                <ScalarProperty Name="statut" ColumnName="statut" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="SessionMentorats">
            <EntityTypeMapping TypeName="LincomDBModel.SessionMentorat">
              <MappingFragment StoreEntitySet="SessionMentorat">
                <ScalarProperty Name="SessionMentoratId" ColumnName="SessionMentoratId" />
                <ScalarProperty Name="ProgrammeMentoratId" ColumnName="ProgrammeMentoratId" />
                <ScalarProperty Name="MentorId" ColumnName="MentorId" />
                <ScalarProperty Name="MentoreId" ColumnName="MentoreId" />
                <ScalarProperty Name="DateSession" ColumnName="DateSession" />
                <ScalarProperty Name="Sujet" ColumnName="Sujet" />
                <ScalarProperty Name="Notes" ColumnName="Notes" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="StatutUtilisateurs">
            <EntityTypeMapping TypeName="LincomDBModel.StatutUtilisateur">
              <MappingFragment StoreEntitySet="StatutUtilisateur">
                <ScalarProperty Name="StatutId" ColumnName="StatutId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="Libelle" ColumnName="Libelle" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Subscriptions">
            <EntityTypeMapping TypeName="LincomDBModel.Subscription">
              <MappingFragment StoreEntitySet="Subscription">
                <ScalarProperty Name="SubscriptionId" ColumnName="SubscriptionId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="DateAbonnement" ColumnName="DateAbonnement" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="SujetForums">
            <EntityTypeMapping TypeName="LincomDBModel.SujetForum">
              <MappingFragment StoreEntitySet="SujetForum">
                <ScalarProperty Name="SujetForumId" ColumnName="SujetForumId" />
                <ScalarProperty Name="Titre" ColumnName="Titre" />
                <ScalarProperty Name="Contenu" ColumnName="Contenu" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="DateCreation" ColumnName="DateCreation" />
                <ScalarProperty Name="NombreVues" ColumnName="NombreVues" />
                <ScalarProperty Name="Statut" ColumnName="Statut" />
                <ScalarProperty Name="etat" ColumnName="etat" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="SupportFormations">
            <EntityTypeMapping TypeName="LincomDBModel.SupportFormation">
              <MappingFragment StoreEntitySet="SupportFormation">
                <ScalarProperty Name="SupportFormationId" ColumnName="SupportFormationId" />
                <ScalarProperty Name="FormationId" ColumnName="FormationId" />
                <ScalarProperty Name="Titre" ColumnName="Titre" />
                <ScalarProperty Name="Fichier" ColumnName="Fichier" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="DateCreation" ColumnName="DateCreation" />
                <ScalarProperty Name="statut" ColumnName="statut" />
                <ScalarProperty Name="DatePublication" ColumnName="DatePublication" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TelechargementRessources">
            <EntityTypeMapping TypeName="LincomDBModel.TelechargementRessource">
              <MappingFragment StoreEntitySet="TelechargementRessources">
                <ScalarProperty Name="TelechargementId" ColumnName="TelechargementId" />
                <ScalarProperty Name="RessourceId" ColumnName="RessourceId" />
                <ScalarProperty Name="MembreId" ColumnName="MembreId" />
                <ScalarProperty Name="DateTelechargement" ColumnName="DateTelechargement" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Traductions">
            <EntityTypeMapping TypeName="LincomDBModel.Traduction">
              <MappingFragment StoreEntitySet="Traduction">
                <ScalarProperty Name="TraductionId" ColumnName="TraductionId" />
                <ScalarProperty Name="Cle" ColumnName="Cle" />
                <ScalarProperty Name="LangueId" ColumnName="LangueId" />
                <ScalarProperty Name="Texte" ColumnName="Texte" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TypeOrganisations">
            <EntityTypeMapping TypeName="LincomDBModel.TypeOrganisation">
              <MappingFragment StoreEntitySet="TypeOrganisation">
                <ScalarProperty Name="TypeOrganisationId" ColumnName="TypeOrganisationId" />
                <ScalarProperty Name="Libelle" ColumnName="Libelle" />
                <ScalarProperty Name="name" ColumnName="name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="true" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>