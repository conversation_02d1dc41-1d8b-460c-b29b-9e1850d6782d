﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class domaineressources : System.Web.UI.Page
    {

        private int info;
        int financementId;
        DomaineRessource_Class actpro = new DomaineRessource_Class();
        DomaineRessource_Class actproj = new DomaineRessource_Class();
        IDomaineRessource obj = new DomaineRessourceImp();
        ICommonCode co = new CommonCode();
        IRessource objpos = new RessourceImp();
        Ressources_Class pos = new Ressources_Class();

        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        DomaineRessource_Class actco = new DomaineRessource_Class();
        IDomaineRessource objactco = new DomaineRessourceImp();
        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        CategoriePost_Class catpost = new CategoriePost_Class();
        ICategoryPost objcatpost = new CategoriePostImp();

        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;
        static string nsco;

        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];
            financementId = Convert.ToInt32(nsco);
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin
                long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
                rolid = Convert.ToInt32(role.Value);//roleconnecte

            }
            else Response.Redirect("~/login.aspx");

            objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
            idorg = Convert.ToInt64(memorg.OrganisationId);
            objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
            nameorg = org.Nom;

            if (!IsPostBack)
            {
               
                InitialiserMessages();
                objdom.chargerDomaineInterventionOrganisation(drpddomai, idorg);
               // objcatpost.chargerCategoriePost(drpdty);

                // Vérifier si un ID est passé en paramètre pour l'édition
                if (nsco != null)
                {

                    btnEnregistrer.InnerText = "Modifier";
                    AfficherDetails();
                }
                else
                {
                    btnEnregistrer.InnerText = "Enregistrer";
                }
            }
        }

        private void InitialiserMessages()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
        }


        protected void btn_ajouter_ServerClick(object sender, EventArgs e)
        {
            if (nsco != null)
            {
                ModifierDomaineRessource();
            }
            else
            {
                AjouterDomaineRessources();
            }
        }


        private void AjouterDomaineRessources()
        {
            try
            {
                if (drpdtyperessources.SelectedValue=="-1" || drpdressource.SelectedValue == "-1" ||
                    drpddomai.SelectedValue == "-1" || drpdstatut.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }

                actproj.RessourceId = Convert.ToInt64(drpdressource.SelectedValue);
                actproj.DomaineInterventionId = Convert.ToInt32(drpddomai.SelectedValue);
                actproj.DateCreation = DateTime.Now;
                actproj.statut = drpdstatut.SelectedValue;
                actproj.MembreId = ide;
                actproj.OrganisationId = idorg;

                info = obj.Ajout(actproj);

                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    msg_succes.InnerText = "Le domaine d'intervention de la ressource a été enregistré avec succès";
                    ReinitialiserFormulaire();
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de l'enregistrement de la ressource";
                }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }

        protected void drpdcateg_SelectedIndexChanged(object sender, EventArgs e)
        {

            objpos.chargerRessourc(drpdressource,idorg,drpdtyperessources.SelectedValue,"Selectionner le/la"+drpdtyperessources.SelectedItem.ToString(),0);
            lblpost.InnerText = "Titre du " + drpdtyperessources.SelectedItem.ToString();

        }

        private void ModifierDomaineRessource()
        {
            try
            {
                if (drpdtyperessources.SelectedValue == "-1" || drpdressource.SelectedValue == "-1" ||
                    drpddomai.SelectedValue == "-1" || drpdstatut.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }


                actproj.RessourceId = Convert.ToInt64(drpdressource.SelectedValue);
                actproj.DomaineInterventionId = Convert.ToInt32(drpddomai.SelectedValue);
                actproj.statut = drpdstatut.SelectedValue;
                actproj.MembreId = ide;

                info = obj.edit(actproj, Convert.ToInt64(nsco), idorg);

                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    msg_succes.InnerText = "Le domaine d'intervention de la ressource a été modifié avec succès";
                    ReinitialiserFormulaire();
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de la modification";
                }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }
        protected void btnEnregistrer_ServerClick(object sender, EventArgs e)
        {
            // Vérifier la validation côté serveur
            if (!Page.IsValid)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Veuillez corriger les erreurs avant de continuer.";
                return;
            }

            if (nsco == null)
            {
                AjouterDomaineRessources();
            }
            else
            {
                ModifierDomaineRessource();
            }
        }
        private void AfficherDetails()
        {
            if (Convert.ToInt64(nsco) > 0)
            {
                obj.afficherDetails(Convert.ToInt64(nsco),idorg,-1,0,actproj);//


                if (actproj.RessourceId > 0)
                {
                    objdom.AfficherDetails(Convert.ToInt32(actproj.DomaineInterventionId), domai, idorg, 0);
                    objactdom.AfficherDetails(Convert.ToInt32(domai.DomaineInterventionId), actdom);
                    objpos.AfficherDetails(Convert.ToInt64(actproj.RessourceId), idorg, 0, pos);

                    drpdressource.SelectedValue = actproj.RessourceId.ToString();
                    drpddomai.SelectedValue = actproj.DomaineInterventionId.ToString();
                    drpdstatut.SelectedValue = actproj.statut;

                    drpdtyperessources.SelectedValue = pos.typeressources;
                   
                    objpos.chargerRessourc(drpdressource, idorg, drpdtyperessources.SelectedValue, "Selectionner le/la" + drpdtyperessources.SelectedItem.ToString(), 0);
                    lblpost.InnerText = "Titre du " + drpdtyperessources.SelectedItem.ToString();
                    drpdressource.SelectedValue = actproj.RessourceId.ToString();

                }
            }
        }

        private void ReinitialiserFormulaire()
        {
            drpdressource.SelectedValue = "-1";
            drpddomai.SelectedValue = "-1";
            drpdstatut.SelectedValue = "-1";


        }
    }
}