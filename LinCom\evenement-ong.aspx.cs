﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class evenement_ong : System.Web.UI.Page
    {

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IPoste objpost = new PosteImp();
        Post_Class post = new Post_Class();
        Post_Class pos = new Post_Class();
        IDomainePost objdompost = new DomainePostImp();
        DomainePost_Class dompost = new DomainePost_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();
        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                affich();
                parcour();

            }
        }
        void affich()
        {

            objpost.Chargement_GDV(listevent, -1, -1, "evenement", "publié", 1);
            objactdom.ChargerDomaines(listcategorie, "evenement");
            // objc.Chargement_GDVL(listinst, 2);
        }
        protected void listcategorie_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            long index = Convert.ToInt64(e.CommandArgument);
            if (e.CommandName == "viewdom")
            {
                objpost.Chargement_GDV(listevent, index, -1, "evenement", "publié", 2);



            }
        }
        protected void parcour()
        {//affichage 
            foreach (ListViewDataItem row in this.listevent.Items)
            {
                //if (row.ItemType == DataControlRowType.)
                //{
                try
                {
                    Label lbldate = (Label)row.FindControl("lbldate");
                    HtmlAnchor btnainscript = (HtmlAnchor)row.FindControl("btnainscript");
                  //  LinkButton btnainscript=(LinkButton)row.FindControl("btnainscript");
                    // Label lbldescript = (Label)row.FindControl("lbldescript");
                   
                    if (Convert.ToDateTime(lbldate.Text)< DateTime.Now )
                    {
                        btnainscript.Visible = false;
                    }
                  
                }
                catch (Exception e)
                {
                   // Label lbldescrit = (Label)row.FindControl("lbldescat");
                   // lbldescrit.Text = "";
                }

            }

        }
        protected void listevent_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "viewevent" || e.CommandName == "viewevent1")
            {

                objpost.AfficherDetailsname(index, -1, 1, pos);
                post.number_of_view = pos.number_of_view + 1;
                objpost.MiseajourData(post, pos.PostId, -1, 0);

                Response.Redirect("~/events.aspx?name=" + index);



            }
        }

    }
}