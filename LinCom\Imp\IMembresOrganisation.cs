﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IMembresOrganisation
    {
        void AfficherDetails(long membresOrganisationId, MembresOrganisation_Class membresOrganisationClass, int cd);
        int Ajouter(MembresOrganisation_Class membresOrganisationClass);
        void ChargerMembresGridview(GridView gdv, string code, int cd, string statut, long organisationId);
        void ChargerMembresListview(ListView gdv, string code, int cd, string statut, long organisationId);
        int Modifier(MembresOrganisation_Class membresOrganisationClass, long id);
        int Supprimer(long membresOrganisationId);
        int ChangerStatut(long membresOrganisationId, string statut);
        void AfficherDetails(string name, MembresOrganisation_Class membresOrganisationClass);
        int countAppartenanceMembreOrganisation(long idmem, int cd);
    }
}
