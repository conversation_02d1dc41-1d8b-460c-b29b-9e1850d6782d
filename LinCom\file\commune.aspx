﻿<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="commune.aspx.cs" Inherits="LinCom.file.commune" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="alert-list">
                    <div class="alert alert-success alert-dismissible" role="alert" runat="server" id="div_msg_succes">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                        <span runat="server" id="msg_succes">Enregistrement reussi</span>
                    </div>
                    <div class="alert alert-danger alert-dismissible" role="alert" runat="server" id="div_msg_error">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                        <span runat="server" id="msg_error">Enregistrement echoué</span>
                        important.
                    </div>

                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">
                                    <div class="breadcomb-icon">
                                        <i class="notika-icon notika-form"></i>
                                    </div>
                                    <div class="breadcomb-ctn">
                                        <h2>Communes</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">

                                    <a data-toggle="tooltip" data-placement="left" href="listcommune.aspx" title="Clique sur ce button pour visualiser la liste des communes" class="btn">Liste des Communes</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcomb area End-->
    <!-- Form Element area Start-->
    <div class="form-element-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">

                        <div class="cmp-tb-hd bcs-hd">
                            <p style="color: red">Remplissez les champs obligatoires avec asterix</p>
                        </div>
                        <div class="row">
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-example-int">
                                    <div class="form-group">
                                        <label>Nom de la Commune *</label>
                                        <div class="nk-int-st">
                                            <input type="text" runat="server" id="txtnm" class="form-control input-sm" placeholder="Nom de la province *">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-example-int">
                                    <div class="form-group">
                                        <label>Nom de la Province *</label>
                                        <div class="nk-int-st">
                                            <asp:DropDownList class="form-control input-sm" ID="drpdprov" runat="server">
                                                <asp:ListItem Value="-1">Selectionner la Province</asp:ListItem>
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <button type="button" runat="server" id="btn_enreg" onserverclick="btn_enreg_ServerClick" class="btn btn-success notika-gp-success">Ajouter</button>
                    
                            </div>
                        </div>

                    </div>
                </div>
            </div>

        </div>
    </div>
    <!-- Form Element area End-->
</asp:Content>
