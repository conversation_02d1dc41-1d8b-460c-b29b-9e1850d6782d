using LinCom.Classe;
using LinCom.file;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.Helpers;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class MembreImp : IMembre
    {
        int msg;
        private Membre m = new Membre();
        ICommonCode co = new CommonCode();
        public void AfficherDetails(long membreId, Membre_Class membre)
        {
            using (Connection con = new Connection())
            {
                var m = con.Membres.FirstOrDefault(x => x.MembreId == membreId);
                if (m != null)
                {
                    membre.MembreId = m.MembreId;
                    membre.Nom = m.Nom;
                    membre.Prenom = m.Prenom;
                    membre.Email = m.Email;
                    membre.Telephone = m.Telephone;
                    membre.Sexe = m.Sexe;
                    membre.DateNaissance = m.DateNaissance;
                    membre.ProvinceId = m.ProvinceId;
                    membre.CommuneId = m.CommuneId;
                    membre.CreatedAt = m.CreatedAt;
                    membre.name = m.name;
                    membre.province = m.province;
                    membre.username = m.username;
                    membre.motpasse = m.motpasse;
                    membre.statut = m.statut;
                    membre.IsActive = m.IsActive;
                    membre.IsVerified = m.IsVerified;
                    membre.LastLogin = m.LastLogin;
                    membre.RoleMembreID = m.RoleMembreID;
                    membre.PhotoProfil = m.PhotoProfil;
                    membre.facebook = m.facebook;
                    membre.siteweb = m.siteweb;
                    membre.twitter = m.twitter;
                    membre.instagramme = m.instagramme;
                    membre.linkedin = m.linkedin;
                    membre.youtube = m.youtube;
                    membre.Biographie = m.Biographie;
                    membre.DateInscription = m.DateInscription;
                    membre.ResetToken = m.ResetToken;
                    membre.ResetTokenExpiry = m.ResetTokenExpiry;
                    membre.Adresse = m.Adresse;
                    membre.LanguePreferee = m.LanguePreferee;
                    
    }
            }
        }
        public int Connect(Membre_Class membre, string usernm, string pwsd, int code)
        {
            using (Connection con = new Connection())
            {
                m = con.Membres.Where(x => x.username == usernm && x.statut == "actif").FirstOrDefault();

                if (m != null)
                {
                    membre.MembreId = m.MembreId;
                    membre.Nom = m.Nom;
                    membre.Prenom = m.Prenom;
                    membre.Email = m.Email;
                    membre.Telephone = m.Telephone;
                    membre.Sexe = m.Sexe;
                    membre.DateNaissance = m.DateNaissance;
                    membre.ProvinceId = m.ProvinceId;
                    membre.CommuneId = m.CommuneId;
                    membre.name = m.name;
                    membre.province = m.province;
                    membre.username = m.username;
                    membre.motpasse = m.motpasse;
                    membre.statut = m.statut;
                    membre.IsActive = m.IsActive;
                    membre.IsVerified = m.IsVerified;
                    membre.LastLogin = m.LastLogin;
                    membre.RoleMembreID = m.RoleMembreID;
                    membre.PhotoProfil = m.PhotoProfil;
                    membre.ResetToken = m.ResetToken;
                    membre.ResetTokenExpiry = m.ResetTokenExpiry;
                    membre.Adresse = m.Adresse;
                    membre.LanguePreferee = m.LanguePreferee;


                    return msg = 1;
                }
                else return msg = 0;

            }
         
        }

     
        public bool VerifierMotDePasse(string email, string motDePasseEntree)
        {
            using (Connection con = new Connection())
            {
                var membre = con.Membres.FirstOrDefault(m => m.username == email);
                if (membre != null)
                {
                    return BCrypt.Net.BCrypt.Verify(motDePasseEntree, membre.motpasse);
                }
            }
            return false;
        }

        public void AfficherDetails(string name, Membre_Class membre,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var m = con.Membres.FirstOrDefault(x => x.name == name);
                    if (m != null)
                    {
                        membre.MembreId = m.MembreId;
                        membre.Nom = m.Nom;
                        membre.Prenom = m.Prenom;
                        membre.Email = m.Email;
                        membre.Telephone = m.Telephone;
                        membre.Sexe = m.Sexe;
                        membre.DateNaissance = m.DateNaissance;
                        membre.ProvinceId = m.ProvinceId;
                        membre.CommuneId = m.CommuneId;
                        membre.CreatedAt = m.CreatedAt;
                        membre.name = m.name;
                        membre.province = m.province;
                        membre.username = m.username;
                        membre.motpasse = m.motpasse;
                        membre.statut = m.statut;
                        membre.IsActive = m.IsActive;
                        membre.IsVerified = m.IsVerified;
                        membre.LastLogin = m.LastLogin;
                        membre.RoleMembreID = m.RoleMembreID;
                        membre.PhotoProfil = m.PhotoProfil;
                        membre.facebook = m.facebook;
                        membre.siteweb = m.siteweb;
                        membre.twitter = m.twitter;
                        membre.instagramme = m.instagramme;
                        membre.linkedin = m.linkedin;
                        membre.youtube = m.youtube;
                        membre.Biographie = m.Biographie;
                        membre.DateInscription = m.DateInscription;
                        membre.ResetToken = m.ResetToken;
                        membre.ResetTokenExpiry = m.ResetTokenExpiry;
                        membre.Adresse = m.Adresse;
                        membre.LanguePreferee = m.LanguePreferee;
                    }
                    else membre.name="No name";

                }
                else if(cd==1)
                {
                    var m = con.Membres.FirstOrDefault(x => x.Email == name);
                    if (m != null)
                    {
                        membre.MembreId = m.MembreId;
                        membre.Nom = m.Nom;
                        membre.Prenom = m.Prenom;
                        membre.Email = m.Email;
                        membre.Telephone = m.Telephone;
                        membre.Sexe = m.Sexe;
                        membre.DateNaissance = m.DateNaissance;
                        membre.ProvinceId = m.ProvinceId;
                        membre.CommuneId = m.CommuneId;
                        membre.CreatedAt = m.CreatedAt;
                        membre.name = m.name;
                        membre.province = m.province;
                        membre.username = m.username;
                        membre.motpasse = m.motpasse;
                        membre.statut = m.statut;
                        membre.IsActive = m.IsActive;
                        membre.IsVerified = m.IsVerified;
                        membre.LastLogin = m.LastLogin;
                        membre.RoleMembreID = m.RoleMembreID;
                        membre.PhotoProfil = m.PhotoProfil;
                        membre.facebook = m.facebook;
                        membre.siteweb = m.siteweb;
                        membre.twitter = m.twitter;
                        membre.instagramme = m.instagramme;
                        membre.linkedin = m.linkedin;
                        membre.youtube = m.youtube;
                        membre.Biographie = m.Biographie;
                        membre.DateInscription = m.DateInscription;
                        membre.ResetToken = m.ResetToken;
                        membre.ResetTokenExpiry = m.ResetTokenExpiry;
                        membre.Adresse = m.Adresse;
                        membre.LanguePreferee = m.LanguePreferee;
                    }
                    else membre.Email = "No email";
                }
                 
           
            
            }
        }

        public int Ajouter(Membre_Class membre)
        {
            using (Connection con = new Connection())
            {
                m.Nom = membre.Nom;
                m.Prenom = membre.Prenom;
                m.Email = membre.Email;
                m.Telephone = membre.Telephone;
                m.Sexe = membre.Sexe;
                m.DateNaissance = membre.DateNaissance;
                m.ProvinceId = membre.ProvinceId;
                m.CommuneId = membre.CommuneId;
                m.name = membre.name;
                m.province = membre.province;
                m.commune = membre.commune;
               
                m.username = membre.username;
                m.motpasse = membre.motpasse;
                m.statut = membre.statut;
                m.IsActive = membre.IsActive;
                m.IsVerified = membre.IsVerified;
                m.LastLogin = membre.LastLogin;
                m.RoleMembreID = membre.RoleMembreID;

                m.PhotoProfil = membre.PhotoProfil;
                m.facebook = membre.facebook;
                m.siteweb = membre.siteweb;
                m.twitter = membre.twitter;
                m.instagramme = membre.instagramme;
                m.linkedin = membre.linkedin;
                m.youtube = membre.youtube;
                m.Biographie = membre.Biographie;
                m.DateInscription = membre.DateInscription;
                m.ResetToken = membre.ResetToken;
                m.ResetTokenExpiry = membre.ResetTokenExpiry;
                m.Adresse = membre.Adresse;
                m.LanguePreferee = membre.LanguePreferee;



                try
                {
                    con.Membres.Add(m);
                    if (con.SaveChanges() == 1)
                    {
                        con.Membres.Add(m);

                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerGridView(GridView gdv, string filtre = "", string name = "")
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Membres
                            select new
                            {
                                MembreId = m.MembreId,
                                Nom = m.Nom,
                                Prenom = m.Prenom,
                                Email = m.Email,
                                Telephone = m.Telephone,
                                Sexe = m.Sexe,
                                DateNaissance = m.DateNaissance,
                                ProvinceId = m.ProvinceId,
                                CommuneId = m.CommuneId,
                                CreatedAt = m.CreatedAt,
                                name = m.name,
                                province = m.province,
                                username = m.username,
                                motpasse = m.motpasse,
                                statut = m.statut,
                                IsActive = m.IsActive,
                                IsVerified = m.IsVerified,
                                LastLogin = m.LastLogin,
                                RoleMembreID = m.RoleMembreID,
                                PhotoProfil = m.PhotoProfil,
                                facebook = m.facebook,
                                siteweb = m.siteweb,
                                twitter = m.twitter,
                                instagramme = m.instagramme,
                                linkedin = m.linkedin,
                                youtube = m.youtube,
                                Biographie = m.Biographie,
                                DateInscription = m.DateInscription,
                                ResetToken = m.ResetToken,
                                ResetTokenExpiry = m.ResetTokenExpiry,
                                Adresse = m.Adresse,
                                LanguePreferee = m.LanguePreferee,

                            }
            ;

                if (!string.IsNullOrEmpty(filtre))
                {
                    query = query.Where(x => x.Nom.Contains(filtre) ||
                                           x.Prenom.Contains(filtre) ||
                                           x.Email.Contains(filtre));
                }

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }

        public void ChargerListview(ListView gdv, long id,string statut, string name)
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Membres
                            where m.statut==statut
                            orderby m.Nom descending
                            select new
                            {
                                id = m.MembreId,
                                Nom = m.Nom,
                                Prenom = m.Prenom,
                                Membre=m.Nom+" "+m.Prenom,
                                Email = m.Email,
                                Telephone = m.Telephone,
                                Sexe = m.Sexe,
                                DateNaissance = m.DateNaissance,
                                ProvinceId = m.ProvinceId,
                                CommuneId = m.CommuneId,
                                CreatedAt = m.CreatedAt,
                                name = m.name,
                                province = m.province,
                                username = m.username,
                                motpasse = m.motpasse,
                                statut = m.statut,
                                IsActive = m.IsActive,
                                IsVerified = m.IsVerified,
                                LastLogin = m.LastLogin,
                                RoleMembreID = m.RoleMembreID,
                                PhotoProfil = m.PhotoProfil,
                                facebook = m.facebook,
                                siteweb = m.siteweb,
                                twitter = m.twitter,
                                instagramme = m.instagramme,
                                linkedin = m.linkedin,
                                youtube = m.youtube,
                                Biographie = m.Biographie,
                                DateInscription = m.DateInscription,
                                ResetToken = m.ResetToken,
                                ResetTokenExpiry = m.ResetTokenExpiry,
                                Adresse = m.Adresse,
                                LanguePreferee = m.LanguePreferee,


                            }
            ;

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }

        // Nouvelle méthode pour charger les contacts avec informations de messagerie
        public void ChargerContactsAvecMessagerie(ListView gdv, long membreConnecteId, string statut)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    // Version simplifiée pour commencer - juste les membres avec des valeurs par défaut
                    var query = from m in con.Membres
                                where m.statut == statut && m.MembreId != membreConnecteId
                                orderby m.Nom, m.Prenom
                                select new
                                {
                                    id = m.MembreId,
                                    Nom = m.Nom ?? "",
                                    Prenom = m.Prenom ?? "",
                                    Membre = (m.Nom ?? "") + " " + (m.Prenom ?? ""),
                                    PhotoProfil = m.PhotoProfil ?? "default-avatar.png",
                                    DernierMessage = "Aucun message",
                                    DateDernierMessage = (DateTime?)null,
                                    MessagesNonLus = 0,
                                    EstEnLigne = m.LastLogin.HasValue &&
                                               m.LastLogin.Value > DateTime.Now.AddMinutes(-15)
                                };

                    gdv.DataSource = query.ToList();
                    gdv.DataBind();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur dans ChargerContactsAvecMessagerie: {ex.Message}");
                    throw; // Relancer l'exception pour que le fallback soit utilisé
                }
            }
        }

        public int Modifier(Membre_Class membre,string email, long code,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var m = con.Membres.FirstOrDefault(x => x.MembreId == code);
                    if (m != null)
                    {
                        m.Nom = membre.Nom;
                        m.Prenom = membre.Prenom;
                        m.Email = membre.Email;
                        m.Telephone = membre.Telephone;
                        m.Sexe = membre.Sexe;
                        m.DateNaissance = membre.DateNaissance;
                        m.ProvinceId = membre.ProvinceId;
                        m.CommuneId = membre.CommuneId;
                        m.name = membre.name;
                        m.province = membre.province;
                        m.commune = membre.commune;
                        m.Adresse = membre.Adresse;


                        m.Nom = membre.Nom;
                        m.Prenom = membre.Prenom;
                        m.Email = membre.Email;
                        m.Telephone = membre.Telephone;
                        m.Sexe =membre.Sexe;
                        m.DateNaissance = membre.DateNaissance;
                        m.ProvinceId =membre.ProvinceId;
                        m.CommuneId = membre.CommuneId;
                        m.name = membre.name;
                        m.province = membre.province;
                        m.commune = membre.commune;
                        m.Adresse = membre.Adresse;
                        m.username = membre.username;
                        m.motpasse = membre.motpasse;
                        m.statut = membre.statut;

                        m.RoleMembreID = membre.RoleMembreID;

                        m.PhotoProfil = membre.PhotoProfil;

                        m.Biographie = membre.Biographie;
                        try
                        {
                            if (con.SaveChanges() == 1)
                            {
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return msg = 0;
                        }
                    }
                    return msg = 0;
                }
                else if (cd==1)
                {
                    var m = con.Membres.FirstOrDefault(x => x.username == email);
                    if (m != null)
                    {
                        m.ResetToken = membre.ResetToken;
                        m.ResetTokenExpiry = membre.ResetTokenExpiry;
                       
                        try
                        {
                            if (con.SaveChanges() == 1)
                            {
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return msg = 0;
                        }
                    }
                  
                }
                else if (cd == 2)
                {
                    var m = con.Membres.FirstOrDefault(x => x.ResetToken == email && x.ResetTokenExpiry > DateTime.Now);
                    if (m != null)
                    {
                        m.motpasse = membre.motpasse;
                        m.ResetToken = null;
                        m.ResetTokenExpiry = null;

                        try
                        {
                            if (con.SaveChanges() == 1)
                            {
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return msg = 0;
                        }
                    }

                }
                return msg;
            }
        }
        public int count(int cd, string publie, string code)
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var b = (from p in con.Membres

                             select p).Count();
                    n = b;
                }
                else if (cd == 1)
                {
                    var b = (from p in con.Membres

                             where p.statut == publie
                             select p).Count();
                    n = b;
                }

            }
            return n;
        }
        public int Supprimer(long membreId)
        {
            using (Connection con = new Connection())
            {
                var m = con.Membres.FirstOrDefault(x => x.MembreId == membreId);
                if (m != null)
                {
                    con.Membres.Remove(m);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public void chargerMembre(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Membres
                           where p.statut == "actif"
                           select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "";
                    item0.Text = "-- Sélectionnez un membre --";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.MembreId.ToString();
                        item.Text = data.Nom + " " + data.Prenom + " (" + data.Email + ")";
                        lst.Items.Add(item);
                    }
                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "";
                    item0.Text = "Aucun membre disponible";
                    lst.Items.Add(item0);
                }
            }
        }

        // Méthode pour rechercher des membres
        public IEnumerable<object> RechercherMembres(string termeRecherche, long membreExclu)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var membres = from m in con.Membres
                                 where m.statut == "actif"
                                       && m.MembreId != membreExclu
                                       && (m.Nom.Contains(termeRecherche)
                                           || m.Prenom.Contains(termeRecherche)
                                           || m.Email.Contains(termeRecherche)
                                           || (m.Nom + " " + m.Prenom).Contains(termeRecherche))
                                 orderby m.Nom, m.Prenom
                                 select new
                                 {
                                     id = m.MembreId,
                                     Membre = (m.Nom ?? "") + " " + (m.Prenom ?? ""),
                                     PhotoProfil = m.PhotoProfil ?? "default-avatar.png",
                                     Email = m.Email ?? "",
                                     Statut = m.statut ?? ""
                                 };

                    return membres.Take(20).ToList(); // Limiter à 20 résultats
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur lors de la recherche de membres: {ex.Message}");
                    return new List<object>();
                }
            }
        }

        // Nouvelle méthode de recherche avec informations de messagerie
        public IEnumerable<object> RechercherMembresAvecMessagerie(string termeRecherche, long membreConnecteId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var query = from m in con.Membres
                                where m.statut == "actif" &&
                                      m.MembreId != membreConnecteId &&
                                      (m.Nom.Contains(termeRecherche) ||
                                       m.Prenom.Contains(termeRecherche) ||
                                       m.Email.Contains(termeRecherche) ||
                                       (m.Nom + " " + m.Prenom).Contains(termeRecherche))
                                let conversation = (from c in con.Conversations
                                                  join pc1 in con.ParticipantConversations on c.ConversationId equals pc1.ConversationId
                                                  join pc2 in con.ParticipantConversations on c.ConversationId equals pc2.ConversationId
                                                  where pc1.MembreId == membreConnecteId && pc2.MembreId == m.MembreId
                                                  select c).FirstOrDefault()
                                let dernierMessage = conversation != null ?
                                    (from msg in con.Messages
                                     where msg.ConversationId == conversation.ConversationId
                                     orderby msg.DateEnvoi descending
                                     select msg).FirstOrDefault() : null
                                let messagesNonLus = conversation != null ?
                                    (from ms in con.MessageStatus
                                     join msg in con.Messages on ms.MessageId equals msg.MessageId
                                     where msg.ConversationId == conversation.ConversationId
                                           && ms.UserId == membreConnecteId
                                           && ms.IsRead == 0
                                     select ms).Count() : 0
                                orderby m.Nom, m.Prenom
                                select new
                                {
                                    id = m.MembreId,
                                    Nom = m.Nom,
                                    Prenom = m.Prenom,
                                    Membre = m.Nom + " " + m.Prenom,
                                    PhotoProfil = m.PhotoProfil ?? "default-avatar.png",
                                    DernierMessage = dernierMessage != null ?
                                        (dernierMessage.Contenu != null && dernierMessage.Contenu.Length > 50 ?
                                         dernierMessage.Contenu.Substring(0, 50) + "..." :
                                         dernierMessage.Contenu ?? "Aucun message") : "Aucun message",
                                    DateDernierMessage = dernierMessage != null ? dernierMessage.DateEnvoi : null,
                                    MessagesNonLus = messagesNonLus,
                                    EstEnLigne = m.LastLogin.HasValue &&
                                               m.LastLogin.Value > DateTime.Now.AddMinutes(-15)
                                };

                    return query.Take(20).ToList(); // Limiter à 20 résultats
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur lors de la recherche de membres avec messagerie: {ex.Message}");
                    return new List<object>();
                }
            }
        }
    }
}