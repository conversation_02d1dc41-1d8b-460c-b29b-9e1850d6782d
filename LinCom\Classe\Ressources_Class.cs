﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace LinCom.Classe
{
    public class Ressources_Class
    {

        public long RessourceId { get; set; }
        public string Titre { get; set; }
        public string Description { get; set; }
        public string DateFormation { get; set; }
        public string name { get; set; }
        public string Fichier { get; set; }
        public Nullable<int> AuteurId { get; set; }
        public string photocouverture { get; set; }
        public Nullable<int> nombrepage { get; set; }
        public string typeressources { get; set; }
        public Nullable<int> nbrevue { get; set; }
        public Nullable<int> nbrelike { get; set; }
        public Nullable<System.DateTime> DateCreation { get; set; }
        public string statut { get; set; }
        public string DatePublication { get; set; }
        public Nullable<long> MembreId { get; set; }
        public Nullable<long> OrganisationId { get; set; }
        public string MOIS { get; set; }
        public string ANNEE { get; set; }
    }
}