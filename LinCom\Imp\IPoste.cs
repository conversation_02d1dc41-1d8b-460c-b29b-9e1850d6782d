﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IPoste
    {
        int Ajout(Post_Class add);
        void Chargement_GDV(GridView GV_apv, long id, long idorg, string code, int cd);
        
        void AfficherDetails(long id, long idorg, int cd, Post_Class pr);
        void AfficherDetailsname(string name, long idorg, int cd, Post_Class pr);
        int edit(Post_Class add, long id,long idorg);
        int supprimer(long id, long idorg);
        void Chargement_GDV(ListView GV_apv, long id, long idorg, string code, string publie, int cd);
        void chargerPost(DropDownList ddw, long id, long idorg, string code, string intitule, int cd);
        int count();
        int count(int cd, int ct, long idorg, string publie, string code);
        int MiseajourData(Post_Class add, long id, long idorg, int cd);
        void searchListview(ListView GV_apv, long id, long idorg, string code, string publie, string srch, int cd);
    }
}
