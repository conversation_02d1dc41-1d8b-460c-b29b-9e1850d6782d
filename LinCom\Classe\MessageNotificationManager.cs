using LinCom.Class;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;

namespace LinCom.Classe
{
    /// <summary>
    /// Gestionnaire des notifications pour la messagerie
    /// </summary>
    public class MessageNotificationManager
    {
        private static readonly NotificationImp notificationService = new NotificationImp();

        /// <summary>
        /// Types de notifications de messagerie
        /// </summary>
        public enum TypeNotificationMessage
        {
            NouveauMessage,
            MessageLu,
            ConversationCreee,
            MembreAjoute,
            MembreQuitte,
            PieceJointeRecue
        }

        /// <summary>
        /// Envoie une notification pour un nouveau message
        /// </summary>
        public static void NotifierNouveauMessage(long expediteurId, long destinataireId, string contenuMessage, long conversationId)
        {
            try
            {
                using (var con = new Connection())
                {
                    // Récupérer les informations de l'expéditeur
                    var expediteur = con.Membres.FirstOrDefault(m => m.MembreId == expediteurId);
                    var destinataire = con.Membres.FirstOrDefault(m => m.MembreId == destinataireId);

                    if (expediteur == null || destinataire == null)
                        return;

                    // Vérifier si le destinataire accepte les notifications
                    if (destinataire.AccepteNotification != 1)
                        return;

                    // Créer la notification
                    var notification = new Notification_Class
                    {
                        MembreId = destinataireId,
                        Titre = $"Nouveau message de {expediteur.Nom} {expediteur.Prenom}",
                        Message = TronquerMessage(contenuMessage, 100),
                        DateNotification = DateTime.Now,
                        UrlRedirection = $"messagerie.aspx?conv={conversationId}",
                        Lu = 0,
                        name = "MessageNotification",
                        statut = "actif"
                    };

                    notificationService.Ajouter(notification);

                    // Envoyer notification en temps réel si activé
                    if (MessagerieConfig.ActiverNotificationsTempsReel)
                    {
                        EnvoyerNotificationTempsReel(destinataireId, notification);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur notification nouveau message: {ex.Message}");
            }
        }

        /// <summary>
        /// Notifie quand un message a été lu
        /// </summary>
        public static void NotifierMessageLu(long expediteurId, long lecteurId, long messageId)
        {
            try
            {
                using (var con = new Connection())
                {
                    var lecteur = con.Membres.FirstOrDefault(m => m.MembreId == lecteurId);
                    var expediteur = con.Membres.FirstOrDefault(m => m.MembreId == expediteurId);

                    if (lecteur == null || expediteur == null)
                        return;

                    // Vérifier si l'expéditeur veut être notifié des lectures
                    if (expediteur.AccepteNotification != 1)
                        return;

                    var notification = new Notification_Class
                    {
                        MembreId = expediteurId,
                        Titre = "Message lu",
                        Message = $"{lecteur.Nom} {lecteur.Prenom} a lu votre message",
                        DateNotification = DateTime.Now,
                        UrlRedirection = $"messagerie.aspx?msg={messageId}",
                        Lu = 0,
                        name = "MessageReadNotification",
                        statut = "actif"
                    };

                    notificationService.Ajouter(notification);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur notification message lu: {ex.Message}");
            }
        }

        /// <summary>
        /// Notifie la création d'une nouvelle conversation de groupe
        /// </summary>
        public static void NotifierNouvelleConversation(long createurId, List<long> participantIds, string sujetConversation, long conversationId)
        {
            try
            {
                using (var con = new Connection())
                {
                    var createur = con.Membres.FirstOrDefault(m => m.MembreId == createurId);
                    if (createur == null) return;

                    foreach (var participantId in participantIds.Where(id => id != createurId))
                    {
                        var participant = con.Membres.FirstOrDefault(m => m.MembreId == participantId);
                        if (participant?.AccepteNotification == 1)
                        {
                            var notification = new Notification_Class
                            {
                                MembreId = participantId,
                                Titre = "Nouvelle conversation de groupe",
                                Message = $"{createur.Nom} {createur.Prenom} vous a ajouté à la conversation '{sujetConversation}'",
                                DateNotification = DateTime.Now,
                                UrlRedirection = $"messagerie.aspx?conv={conversationId}",
                                Lu = 0,
                                name = "GroupConversationNotification",
                                statut = "actif"
                            };

                            notificationService.Ajouter(notification);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur notification nouvelle conversation: {ex.Message}");
            }
        }

        /// <summary>
        /// Notifie la réception d'une pièce jointe
        /// </summary>
        public static void NotifierPieceJointe(long expediteurId, long destinataireId, string nomFichier, long conversationId)
        {
            try
            {
                using (var con = new Connection())
                {
                    var expediteur = con.Membres.FirstOrDefault(m => m.MembreId == expediteurId);
                    var destinataire = con.Membres.FirstOrDefault(m => m.MembreId == destinataireId);

                    if (expediteur == null || destinataire?.AccepteNotification != 1)
                        return;

                    var notification = new Notification_Class
                    {
                        MembreId = destinataireId,
                        Titre = "Nouvelle pièce jointe",
                        Message = $"{expediteur.Nom} {expediteur.Prenom} vous a envoyé le fichier '{nomFichier}'",
                        DateNotification = DateTime.Now,
                        UrlRedirection = $"messagerie.aspx?conv={conversationId}",
                        Lu = 0,
                        name = "AttachmentNotification",
                        statut = "actif"
                    };

                    notificationService.Ajouter(notification);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur notification pièce jointe: {ex.Message}");
            }
        }

        /// <summary>
        /// Obtient le nombre de notifications non lues pour un utilisateur
        /// </summary>
        public static int CompterNotificationsNonLues(long membreId)
        {
            try
            {
                using (var con = new Connection())
                {
                    return con.Notifications.Count(n => n.MembreId == membreId && n.Lu == 0 && n.statut == "actif");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur comptage notifications: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Obtient les notifications récentes pour un utilisateur
        /// </summary>
        public static List<NotificationInfo> ObtenirNotificationsRecentes(long membreId, int limite = 10)
        {
            try
            {
                using (var con = new Connection())
                {
                    var notifications = con.Notifications
                        .Where(n => n.MembreId == membreId && n.statut == "actif")
                        .OrderByDescending(n => n.DateNotification)
                        .Take(limite)
                        .Select(n => new NotificationInfo
                        {
                            Id = n.NotificationId,
                            Titre = n.Titre,
                            Message = n.Message,
                            DateNotification = n.DateNotification ?? DateTime.Now,
                            UrlRedirection = n.UrlRedirection,
                            EstLue = n.Lu == 1,
                            Type = n.name
                        })
                        .ToList();

                    return notifications;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur récupération notifications: {ex.Message}");
                return new List<NotificationInfo>();
            }
        }

        /// <summary>
        /// Marque une notification comme lue
        /// </summary>
        public static bool MarquerCommeLue(long notificationId, long membreId)
        {
            try
            {
                using (var con = new Connection())
                {
                    var notification = con.Notifications.FirstOrDefault(n => 
                        n.NotificationId == notificationId && n.MembreId == membreId);

                    if (notification != null)
                    {
                        notification.Lu = 1;
                        con.SaveChanges();
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur marquage notification: {ex.Message}");
            }
            return false;
        }

        /// <summary>
        /// Marque toutes les notifications comme lues pour un utilisateur
        /// </summary>
        public static int MarquerToutesCommeLues(long membreId)
        {
            try
            {
                using (var con = new Connection())
                {
                    var notifications = con.Notifications.Where(n => 
                        n.MembreId == membreId && n.Lu == 0 && n.statut == "actif").ToList();

                    foreach (var notification in notifications)
                    {
                        notification.Lu = 1;
                    }

                    con.SaveChanges();
                    return notifications.Count;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur marquage toutes notifications: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Supprime les anciennes notifications
        /// </summary>
        public static int SupprimerAnciennesNotifications(int joursConservation = 30)
        {
            try
            {
                using (var con = new Connection())
                {
                    var dateLimit = DateTime.Now.AddDays(-joursConservation);
                    var anciennesNotifications = con.Notifications
                        .Where(n => n.DateNotification < dateLimit && n.Lu == 1)
                        .ToList();

                    con.Notifications.RemoveRange(anciennesNotifications);
                    con.SaveChanges();

                    return anciennesNotifications.Count;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur suppression anciennes notifications: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Tronque un message pour l'affichage dans les notifications
        /// </summary>
        private static string TronquerMessage(string message, int longueurMax)
        {
            if (string.IsNullOrEmpty(message))
                return "";

            // Convertir les émojis en codes pour éviter les problèmes d'affichage
            message = EmojiManager.ConvertirEnCodes(message);

            if (message.Length <= longueurMax)
                return message;

            return message.Substring(0, longueurMax - 3) + "...";
        }

        /// <summary>
        /// Envoie une notification en temps réel (à implémenter avec SignalR)
        /// </summary>
        private static void EnvoyerNotificationTempsReel(long membreId, Notification_Class notification)
        {
            // TODO: Implémenter avec SignalR pour les notifications en temps réel
            // Pour l'instant, juste un log
            System.Diagnostics.Debug.WriteLine($"Notification temps réel pour membre {membreId}: {notification.Titre}");
        }
    }

    /// <summary>
    /// Informations sur une notification
    /// </summary>
    public class NotificationInfo
    {
        public long Id { get; set; }
        public string Titre { get; set; }
        public string Message { get; set; }
        public DateTime DateNotification { get; set; }
        public string UrlRedirection { get; set; }
        public bool EstLue { get; set; }
        public string Type { get; set; }
        public string TempsEcoule => CalculerTempsEcoule();

        private string CalculerTempsEcoule()
        {
            var duree = DateTime.Now - DateNotification;
            
            if (duree.TotalMinutes < 1)
                return "À l'instant";
            if (duree.TotalMinutes < 60)
                return $"Il y a {(int)duree.TotalMinutes} min";
            if (duree.TotalHours < 24)
                return $"Il y a {(int)duree.TotalHours}h";
            if (duree.TotalDays < 7)
                return $"Il y a {(int)duree.TotalDays}j";
            
            return DateNotification.ToString("dd/MM/yyyy");
        }
    }
}
