﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="espaceforum.aspx.cs" Inherits="LinCom.espaceforum" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

       <!-- Titre de la page -->
<div class="page-title">
  <div class="heading">
    <div class="container">
      <div class="row d-flex justify-content-center text-center">
        <div class="col-lg-8">
          <h2 class="display-5 fw-bold">Forum</h2>
          <p class="lead">Rejoignez notre communauté dans ce forume des discussions et développez vos compétences et votre réseau.</p>
        </div>
      </div>
    </div>
  </div>
  <nav class="breadcrumbs">
    <div class="container">
      <ol>
        <li><a href="home.aspx">Home</a></li>
        <li class="current">Forum</li>
      </ol>
    </div>
  </nav>
</div>
   <main class="main">

  <!-- Section Forum -->
  <section class="forum-posts section">
    <div class="container">
      <div class="row">

        <!-- Colonne gauche : Sujets de discussion -->
        <div class="col-lg-8">

         
            <!-- Filtres -->
<div class="d-flex justify-content-between align-items-center mb-3">
  <h4 class="mb-0">Questions du Forum</h4>

  <div>
    <button class="btn btn-outline-primary btn-sm me-2" runat="server" id="btnrecent" onserverclick="btnrecent_ServerClick">Forums récents</button>
    <button class="btn btn-outline-secondary btn-sm" runat="server" id="btnnonrepondu" onserverclick="btnnonrepondu_ServerClick">Non répondus</button>
    <button class="btn btn-outline-secondary btn-sm" runat="server" id="btnvu" onserverclick="btnvu_ServerClick">Plus vus</button>
  </div>
</div>

          <div class="row gy-4">

            <!-- Sujet de forum -->
                        <asp:ListView ID="listforum" runat="server" OnItemCommand="listforum_ItemCommand">
<EmptyItemTemplate>Aucune donnée pour le moment.</EmptyItemTemplate>
<ItemTemplate>
            <div class="col-md-12">
              <div class="forum-card">
                <div class="forum-header d-flex align-items-center">
                  <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/",Eval("Photomembre"))) %>' class="user-avatar" alt="Avatar utilisateur">
                  <div class="user-info ms-2">
                    <h5 class="user-name"><%# Eval("Nommembre") %></h5>
                    <small class="text-muted">Posté le <time datetime="2025-05-29"> <%# Convert.ToDateTime( Eval("DateCreation")).ToString("dd/MM/yyyy") %>,  <%# GetRelativeDate(Convert.ToDateTime(Eval("DateCreation"))) %></time></small>
                  </div>
                </div>
                <div class="forum-body mt-2">
                  <p><strong>Sujet :</strong>
                       <asp:LinkButton runat="server" class="forum-title" CommandName="view" CommandArgument='<%# Eval("name") %>'><%# HttpUtility.HtmlEncode( Eval("Titre")) %></asp:LinkButton>


                  </p>
                  <p class="forum-snippet"><%# Eval("Contenu") %></p>
                </div>
                <div class="forum-footer">
                  <span class="text-muted"><i class="bi bi-chat-left-text"></i> <%# Eval("NombreReponses") %> réponse(s)</span>
                  <span class="text-muted"><i class="bi bi-eye me-1"></i> <%# Eval("NombreReponses") %> vu(s)</span>
                    <asp:LinkButton runat="server" class="btn btn-light btn-sm" CommandName="view" CommandArgument='<%# Eval("name") %>'>Répondre</asp:LinkButton>

                  <button class="btn btn-light btn-sm"><i class="bi bi-share"></i> Partager</button>
                  
                </div>
              </div>
            </div>
                </ItemTemplate>
</asp:ListView>
            <!-- Fin Sujet -->

             

            <!-- Tu peux dupliquer ce bloc pour chaque sujet -->

          </div>
        </div>

        <!-- Colonne droite : Filtrage -->
        <div class="col-lg-4">
         <div class="card mb-4 p-4 shadow-sm new-question-box">
  <h5 class="mb-3 d-flex align-items-center text-008374">
    <i class="bi bi-question-circle-fill me-2"></i> Poser une nouvelle question
  </h5>
  <div id="new-question-form">
    <div class="mb-3">
      <label class="form-label fw-semibold">Titre de la question</label>
      <input type="text" runat="server" id="txttitre" class="form-control input-custom"
        placeholder="Ex: Comment lancer un projet pour les jeunes ?">
    </div>
  
    <div class="mb-3">
      <label class="form-label fw-semibold">Contenu</label>
      <textarea class="form-control input-custom" runat="server" id="txtquestion" rows="5"
        placeholder="Expliquez votre problème ou votre question..."></textarea>
    </div>

    <div class="text-end">
      <button type="submit" class="btn btn-008374 rounded-pill px-4" runat="server" id="btnenreg" onserverclick="btnenreg_ServerClick">
        <i class="bi bi-send me-1"></i> Publier la question
      </button>
    </div>
  </div>
</div>

          <div class="search-sidebar p-3 bg-light rounded shadow-sm">
                          <!-- Formulaire de nouvelle question -->

            <h5>Filtrer les discussions</h5>
            <div id="forumFilterForm">
              <div class="mb-3">
                <label for="categoryFilter" class="form-label">Catégorie</label>
                <select id="categoryFilter" class="form-select">
                  <option value="">Toutes</option>
                  <option value="Jeunesse">Jeunesse</option>
                  <option value="Santé">Santé</option>
                  <option value="Éducation">Éducation</option>
                </select>
              </div>
              <div class="mb-3">
                <label for="dateFilter" class="form-label">Date</label>
                <input type="date" id="dateFilter" class="form-control">
              </div>
              <div class="mb-3">
                <label for="authorFilter" class="form-label">Auteur</label>
                <input type="text" id="authorFilter" class="form-control" placeholder="Nom de l’auteur">
              </div>
              <button type="submit" class="btn btn-success w-100">Appliquer</button>
            </div>
          </div>
        </div>

      </div>
    </div>
  </section>

  <!-- Pagination -->
  <!-- Pagination -->
    <nav class="d-flex justify-content-center mt-4">
      <ul class="pagination pagination-sm">
        <li class="page-item"><a class="page-link text-008374" href="#"><i class="bi bi-chevron-left"></i></a></li>
        <li class="page-item active"><a class="page-link bg-008374 border-0" href="#">1</a></li>
        <li class="page-item"><a class="page-link text-008374" href="#">2</a></li>
        <li class="page-item"><a class="page-link text-008374" href="#"><i class="bi bi-chevron-right"></i></a></li>
      </ul>
    </nav>
</main>

<style>
.forum-card {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  font-size: 0.9rem;
}

.forum-header .user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-info .user-name {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.forum-title {
  font-weight: 600;
  color: #0a66c2;
  text-decoration: none;
}

.forum-title:hover {
  text-decoration: underline;
}

.forum-snippet {
  color: #444;
  font-size: 0.88rem;
  margin-top: 5px;
}

.forum-footer {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 10px;
  font-size: 0.85rem;
}

.forum-footer .btn {
  padding: 4px 10px;
  border: 1px solid #ddd;
}

.search-sidebar {
  background: #f9f9f9;
  max-height: 80vh;
  overflow-y: auto;
}

.search-sidebar h5 {
  margin-bottom: 15px;
}
/* Couleur personnalisée */
.text-008374 {
  color: #008374;
}
.btn-008374 {
  background-color: #008374;
  color: white;
  border: none;
}
.btn-008374:hover {
  background-color: #006e63;
  color: white;
}

/* Champ avec un style doux */
.input-custom {
  border-radius: 8px;
  border: 1px solid #ccc;
  transition: all 0.2s;
}
.input-custom:focus {
  border-color: #008374;
  box-shadow: 0 0 0 0.2rem rgba(0, 131, 116, 0.2);
}

/* Carte plus moderne */
.new-question-box {
  background: #ffffff;
  border-left: 5px solid #008374;
  border-radius: 12px;
}

</style>
</asp:Content>
