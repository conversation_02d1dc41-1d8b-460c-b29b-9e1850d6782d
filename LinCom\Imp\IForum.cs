﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IForum
    {
        void AfficherDetails(long forumId, Forum_Class forum);
        int Ajouter(Forum_Class forum);
        int Modifier(Forum_Class forum);
        int Supprimer(long forumId);
        void ChargerGridView(GridView gdv, string name = "");
        void ChargerSujetsRecents(Repeater rpt, long forumId);
        int CompterParticipants(long forumId);
        int ChangerStatut(long forumId, string nouveauStatut);
        void ChargerStatistiques(Repeater rpt, long forumId);
        void ChargerForumsPopulaires(Repeater rpt, int nombreForums = 5);

       
    }
}
