﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class login : System.Web.UI.Page
    {

        private int info;

        Membre_Class mem = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        IMembre obj = new MembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IRoleMembre objrl = new RoleMembreImp();
        RoleMembre_Class rl = new RoleMembre_Class();
        CommonCode co = new CommonCode();

        //HttpCookie iduser = new HttpCookie("iduser");
        //HttpCookie rs = new HttpCookie("rs");
        //HttpCookie role = new HttpCookie("role");
        //HttpCookie cop = new HttpCookie("cop");
        //HttpCookie usernm = new HttpCookie("usernm");
        //HttpCookie photo = new HttpCookie("photo");
        //HttpCookie typeagc = new HttpCookie("typeagc");
        //HttpCookie tel = new HttpCookie("tel");
        //HttpCookie email = new HttpCookie("email");


        static int typ;
        protected void btn_connect_Click(object sender, EventArgs e)
        {
            connect();
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                if (Request.Cookies["usernm"] != null && Request.Cookies["pswd"] != null && Request.Cookies["pro"] != null)
                {
                    txt_usernm.Value = Request.Cookies["usernm"].Value;
                    txt_pswd.Attributes["value"] = Request.Cookies["pswd"].Value;
                }
                initial_msg();

            }
        }
        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;

        }
        protected void connect()
        {
            try
            {


                if (txt_usernm.Value == "" || txt_pswd.Value == "")
                {
                    div_msg_error.Visible = true; msg_error.InnerText = "Renseignez tous les champs";

                }
                else
                {


                    info = obj.Connect(mem, txt_usernm.Value, co.HasherMotDePasse(txt_pswd.Value.Trim()), 0);

                    if (info == 1)
                    {
                        if (BCrypt.Net.BCrypt.Verify(txt_pswd.Value.Trim(), mem.motpasse))
                        {
                            if (mem.statut == "actif")
                            {

                             
                                objrl.AfficherDetailRole((int)mem.RoleMembreID, rl);
                                if (rl.statut == "actif")
                                {
                                    if (rl.TYPE == "administrateur")
                                    {
                                        //  Session["UserId"] = mem.MembreId;
                                        //  Session["iduser"] = mem.MembreId;
                                        // Session["RoleId"] = mem.RoleMembreID;
                                        //  Session["role"] = mem.RoleMembreID;
                                        HttpCookie iduser = new HttpCookie("iduser");
                                        iduser.Value = mem.MembreId.ToString();
                                        iduser.Expires = DateTime.Now.AddHours(1); // définis bien la date
                                        iduser.HttpOnly = true; // sécurise le cookie
                                        iduser.Secure = Request.IsSecureConnection;
                                        iduser.SameSite = SameSiteMode.Strict;
                                        Response.Cookies.Add(iduser);

                                        //iduser.Value = mem.MembreId.ToString();
                                        //iduser.Expires.Add(new TimeSpan(1, 0, 0));
                                        //Response.Cookies.Add(iduser);


                                        //role.Value = mem.RoleMembreID.ToString();
                                        //role.Expires.Add(new TimeSpan(1, 0, 0));
                                        //Response.Cookies.Add(role);
                                        HttpCookie role = new HttpCookie("role");
                                        role.Value = mem.RoleMembreID.ToString();
                                        role.Expires = DateTime.Now.AddHours(1);
                                        role.HttpOnly = true;
                                        role.Secure = Request.IsSecureConnection;
                                        role.SameSite = SameSiteMode.Strict;
                                        Response.Cookies.Add(role);

                                        if (rl.TYPE == "admin")
                                            typ = 1;//admin
                                        else typ = 2;//pharma

                                        //typeagc.Value = typ.ToString();
                                        //typeagc.Expires.Add(new TimeSpan(1, 0, 0));
                                        //Response.Cookies.Add(typeagc);
                                        Response.Redirect("~/file/organisation.aspx");
                                    }
                                    else if(rl.TYPE == "membre")
                                    {
                                        //Session["UserId"] = mem.MembreId;
                                        //Session["RoleId"] = mem.RoleMembreID;
                                        //il faut ici tester le membre est dans combien de organisations

                                        //  Session["UserId"] = mem.MembreId;
                                        // Session["iduser"] = mem.MembreId;
                                        // Session["RoleId"] = mem.RoleMembreID;
                                        // Session["role"] = mem.RoleMembreID;

                                        // int countAppartenanceMembreOrganisation(long idmem, int cd)

                                        HttpCookie iduser = new HttpCookie("iduser");
                                        iduser.Value = mem.MembreId.ToString();
                                        iduser.Expires = DateTime.Now.AddHours(1); // définis bien la date
                                        iduser.HttpOnly = true; // sécurise le cookie
                                        iduser.Secure = Request.IsSecureConnection;
                                        iduser.SameSite = SameSiteMode.Strict;
                                        Response.Cookies.Add(iduser);

                                        //iduser.Value = mem.MembreId.ToString();
                                        //iduser.Expires.Add(new TimeSpan(1, 0, 0));
                                        //Response.Cookies.Add(iduser);


                                        //role.Value = mem.RoleMembreID.ToString();
                                        //role.Expires.Add(new TimeSpan(1, 0, 0));
                                        //Response.Cookies.Add(role);
                                        HttpCookie role = new HttpCookie("role");
                                        role.Value = mem.RoleMembreID.ToString();
                                        role.Expires = DateTime.Now.AddHours(1);
                                        role.HttpOnly = true;
                                        role.Secure = Request.IsSecureConnection;
                                        role.SameSite = SameSiteMode.Strict;
                                        Response.Cookies.Add(role);
                                        if (rl.TYPE == "admin")
                                            typ = 1;//admin
                                        else typ = 2;//pharma

                                        //typeagc.Value = typ.ToString();
                                        //typeagc.Expires.Add(new TimeSpan(1, 0, 0));
                                        //Response.Cookies.Add(typeagc);

                                        Response.Redirect("~/home.aspx");
                                    }

                                }
                                else
                                {
                                    div_msg_error.Visible = true; msg_error.InnerText = "Votre rôle n'est pas activé, contactez l'administrateur du système.";
                                }
                            }
                            else
                            {
                                div_msg_error.Visible = true; msg_error.InnerText = "Votre compte est désactivé. Attendez la confirmation de l'administrateur.";
                            }
                        }
                        else
                        {
                            div_msg_error.Visible = true; msg_error.InnerText = "le Mot de Passe est incorecte";

                        }

                    }
                    else
                    {
                        div_msg_error.Visible = true; msg_error.InnerText = "Le Username est incorecte ou cet utilisateur n'existe pas";

                    }
                }

            }
            catch (Exception ex)
            {
                // logguer ou afficher les erreurs pour le débogage
                System.Diagnostics.Debug.WriteLine("Erreur de Connection : " + ex.Message);
            }
        }

        protected void btn_enreg_ServerClick(object sender, EventArgs e)
        {
            connect();
        }
        protected void aci_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#hero");
        }

        protected void prop_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#about");
        }

        protected void serv_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#services");
        }

        protected void inst_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#team");
        }
        protected void cont_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#contact");
        }

        protected void btnreng_ServerClick(object sender, EventArgs e)
        {
            connect();
        }
    }
}