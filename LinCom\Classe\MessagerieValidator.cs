using System;
using System.Text.RegularExpressions;
using System.Web;

namespace LinCom.Classe
{
    /// <summary>
    /// Classe utilitaire pour la validation des données de messagerie
    /// </summary>
    public static class MessagerieValidator
    {
        /// <summary>
        /// Valide le contenu d'un message
        /// </summary>
        public static ValidationResult ValiderMessage(string contenu)
        {
            if (string.IsNullOrWhiteSpace(contenu))
            {
                return new ValidationResult(false, MessagerieConfig.Messages.MessageVide);
            }

            if (contenu.Trim().Length > MessagerieConfig.MaxLongueurMessage)
            {
                return new ValidationResult(false, 
                    MessagerieConfig.FormatMessage(MessagerieConfig.Messages.MessageTropLong, 
                    MessagerieConfig.MaxLongueurMessage));
            }

            // Vérifier les caractères dangereux (XSS basique)
            if (ContientCaracteresDangereux(contenu))
            {
                return new ValidationResult(false, "Le message contient des caractères non autorisés");
            }

            return new ValidationResult(true, "Message valide");
        }

        /// <summary>
        /// Valide un ID de membre
        /// </summary>
        public static ValidationResult ValiderMembreId(string membreIdStr)
        {
            if (string.IsNullOrWhiteSpace(membreIdStr))
            {
                return new ValidationResult(false, MessagerieConfig.Messages.DestinataireInvalide);
            }

            if (!long.TryParse(membreIdStr, out long membreId) || membreId <= 0)
            {
                return new ValidationResult(false, MessagerieConfig.Messages.DestinataireInvalide);
            }

            return new ValidationResult(true, "ID membre valide", membreId);
        }

        /// <summary>
        /// Valide une recherche de contact
        /// </summary>
        public static ValidationResult ValiderRechercheContact(string termeRecherche)
        {
            if (string.IsNullOrWhiteSpace(termeRecherche))
            {
                return new ValidationResult(false, "Terme de recherche vide");
            }

            if (termeRecherche.Trim().Length < 2)
            {
                return new ValidationResult(false, "Le terme de recherche doit contenir au moins 2 caractères");
            }

            if (termeRecherche.Length > 50)
            {
                return new ValidationResult(false, "Le terme de recherche est trop long");
            }

            // Nettoyer le terme de recherche
            string termeNettoye = NettoyerTexte(termeRecherche);
            
            return new ValidationResult(true, "Recherche valide", termeNettoye);
        }

        /// <summary>
        /// Vérifie si le texte contient des caractères dangereux
        /// </summary>
        private static bool ContientCaracteresDangereux(string texte)
        {
            // Patterns XSS basiques
            string[] patternsXSS = {
                @"<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>",
                @"javascript:",
                @"vbscript:",
                @"onload\s*=",
                @"onerror\s*=",
                @"onclick\s*=",
                @"<iframe\b",
                @"<object\b",
                @"<embed\b"
            };

            foreach (string pattern in patternsXSS)
            {
                if (Regex.IsMatch(texte, pattern, RegexOptions.IgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Nettoie un texte en supprimant les caractères dangereux
        /// </summary>
        public static string NettoyerTexte(string texte)
        {
            if (string.IsNullOrEmpty(texte))
                return string.Empty;

            // Encoder HTML pour éviter XSS
            string texteNettoye = HttpUtility.HtmlEncode(texte.Trim());
            
            // Supprimer les caractères de contrôle
            texteNettoye = Regex.Replace(texteNettoye, @"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "");
            
            return texteNettoye;
        }

        /// <summary>
        /// Valide un nom de fichier
        /// </summary>
        public static ValidationResult ValiderNomFichier(string nomFichier)
        {
            if (string.IsNullOrWhiteSpace(nomFichier))
            {
                return new ValidationResult(false, "Nom de fichier vide");
            }

            string extension = System.IO.Path.GetExtension(nomFichier);
            
            if (!MessagerieConfig.EstExtensionAutorisee(extension))
            {
                return new ValidationResult(false, MessagerieConfig.Messages.ExtensionNonAutorisee);
            }

            return new ValidationResult(true, "Nom de fichier valide");
        }
    }

    /// <summary>
    /// Résultat d'une validation
    /// </summary>
    public class ValidationResult
    {
        public bool EstValide { get; set; }
        public string Message { get; set; }
        public object Valeur { get; set; }

        public ValidationResult(bool estValide, string message, object valeur = null)
        {
            EstValide = estValide;
            Message = message;
            Valeur = valeur;
        }
    }
}
