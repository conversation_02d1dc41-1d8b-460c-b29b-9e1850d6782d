﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IReplyForum
    {
        int Ajout(ReplyForum_Class add);
        void Chargement_GDV(GridView GV_apv);
        void search(GridView GV_apv, string code);
        int edit(ReplyForum_Class cl, long id);
        int supprimer(int id);
        void afficherDetails(long id, long idfor, string name, string statut, int cd, ReplyForum_Class add);
        void chargerReplyFroum(DropDownList lst);
        int count(long id, long idmem, int cd, string statut);
        void ChargementListview(ListView GV_apv, long id, long idmem, string name, string statut, int cd);



    }
}
