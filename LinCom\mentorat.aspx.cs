﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.file;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Linq;
using System.Reflection;
using System.Web;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class mentorat : System.Web.UI.Page
    {
        private int info;
        Membre_Class membreClass = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objMembre = new MembreImp();

        Mentor_Class ment = new Mentor_Class();
        IMentor objment = new MentorImp();

        Mentore_Class mento=new Mentore_Class();
        IMentore objmento=new MentoreImp();

        IProvince objProvince = new ProvinceImp();
        ICommune objCommune = new CommuneImp();
        ICommonCode co = new CommonCode();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();

        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();

        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();

        IPoste obj = new PosteImp();
        Post_Class pos = new Post_Class();
        Post_Class post = new Post_Class();
        Post_Class p = new Post_Class();
        CommentairePoste_Class com = new CommentairePoste_Class();
        ICommentairePoste objcom = new CommentaireImp();

        DomainePost_Class actco = new DomainePost_Class();
        IDomainePost objactco = new DomainePostImp();

        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();

        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();

        CategoriePost_Class catpost = new CategoriePost_Class();
        ICategoryPost objcatpost = new CategoriePostImp();

        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;

        static string slug;
        protected void Page_Load(object sender, EventArgs e)
        {

                slug = Request.QueryString["name"];

                if (slug == null)
                {
                    Response.Redirect("~/programmementorat.aspx");
                }
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
               
                }
            objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
            idorg = Convert.ToInt64(memorg.OrganisationId);
            objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
            nameorg = org.Nom;

            if (!IsPostBack)
            {
                //if (slug != null)
                //{
                initial_msg();
               
                hfUrlPage.Value = Request.Url.AbsoluteUri;
                parcours();
                //LoadString();

                //}
                //else Response.Redirect("~/news.aspx");



            }
        }
     
        void commentaire()
        {

            try
            {
                if (txtmessage.Value == "")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Veuillez renseigner  commentaire.!!!')</script>");

                }
                else
                {


                    obj.AfficherDetailsname(slug, -1, 1, p);//appel du post

                    com.PostId = Convert.ToInt64(p.PostId);
                    com.MembreId = ide;
                    com.Contenu = txtmessage.Value;
                    com.DateCommentaire = DateTime.Now;
                    com.EstVisible = "vu";
                    com.Nbrevue = 0;
                    com.name = com.CommentPostId.ToString();
                    info = objcom.Ajout(com);

                    if (info > 0)
                    {
                        objcom.ChargementListview(listcommentairementore, p.PostId, -1, -1, "vu", 1);
                        //  txtcomment.InnerText = objcom.count(-1, Convert.ToInt64(p.PostId), 0) + " Commentaire(s)";
                        txtcommentpost.InnerText = objcom.count(-1, Convert.ToInt64(p.PostId), 1) + " Commentaire(s)";

                        txtmessage.Value = "";
                        Response.Write("<script LANGUAGE=JavaScript>alert('Votre commentaire a été bien envoyé')</script>");

                    }
                    else
                    {
                        Response.Write("<script LANGUAGE=JavaScript>alert('Problème Technique.!!!')</script>");

                    }

                }
            }
            catch (Exception e)
            {

            }
        }
        //protected void OnPagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        //{
        //    (listblog.FindControl("DataPager1") as DataPager).SetPageProperties(e.StartRowIndex, e.MaximumRows, false);
        //    obj.Chargement_GDV(listblog, 0, "blog", 7);
        //}
        protected void listpost_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "view")
            {

                obj.AfficherDetailsname(index, -1, 1, pos);
                post.number_of_view = pos.number_of_view + 1;
                obj.MiseajourData(post, pos.PostId, -1, 0);

                Response.Redirect("~/mentorat.aspx?name=" + index);
            }
        }
        public string GetRelativeDate(DateTime date)
        {
            return co.GetRelativeDate(date);
        }



        void initial_msg()
        {

            //blog
            obj.Chargement_GDV(listprogram, -1, -1, "programmementorat", "publié", 1);
            //obj.Chargement_GDV(listprogram, -1, -1, "programmementorat", "fnuap", 1);

            //blog
            //  obj.Chargement_GDV(listblog, 0, "blog", 3);
            //event
            // obj.Chargement_GDV(listevent, 0, "blog", 4);
            if (!string.IsNullOrEmpty(slug))
            {

                // obj.afficherDetails(Convert.ToInt32(nscno), "blog", pc);
                obj.AfficherDetailsname(slug, -1, 1, p);//appel du post


                // objcom.Chargement_GDV(listcomment, 0, "", Convert.ToInt64(nscno), 0);
                // obj.Chargement_GDV(listv1, (int)pc.IDCAT, 1);
                txttitle.InnerText = p.Titre;
                txttitle1.InnerText = p.Titre;
                txttitle2.InnerText = p.Titre;
                txtdateinscrption.InnerText =Convert.ToDateTime( p.DateModification).ToString("dd/MM/yyyy");
                txtstatut.InnerText = p.EstPublieEvent;
                txtauteur.InnerText = p.author;
                txtduree.InnerText = p.eventduration;
                txtcible.InnerText = p.whoattend;
                txtauteur.InnerText = p.Titre;
                txtauteur.InnerText = p.Titre;
                txtauteur.InnerText = p.Titre;
                txtnbrementor.InnerText = objment.count(-1,p.PostId,-1,-1,0).ToString();
                txtnbrementore.InnerText = objmento.count(-1,p.PostId,-1,-1,0).ToString();

                //   txttitle1.InnerText = pc.titre;
                txtduree.InnerText = Convert.ToDateTime(p.DatePublication).ToString("dd/MM/yyyy") + " - " + Convert.ToDateTime(p.DateModification).ToString("dd/MM/yyyy");
              //  txtzone.InnerText = p.eventplace;
              //  txtbeneficiaire.InnerText = p.whoattend;

                txtdescription.InnerHtml = p.Contenu;

                // resumepost.InnerHtml = pc.description;
                ;
                txtdatepublication.InnerText = Convert.ToDateTime(p.DateCreation).ToString("dd/MM/yyyy") + ", " + co.GetRelativeDate(Convert.ToDateTime(p.DateCreation));
                txtauteur.InnerText = p.author;
              //  imgauteur.Src = "~/file/program/" + p.video;
                txtcommentpost.InnerText = objcom.count(-1, Convert.ToInt64(p.PostId), 1) + " Commentaire(s)";
                // sectblogdetail.Attributes.Add("style", "background-image(~/file/site/)"+ pc.video);
                // sectblogdetail.Style["background-image"] = "~/file/site/"+p.video;
                //sectblogdetail.Style["background-image"] = "url(../file/post/"+p.photo+")";

              //  sectblogdetail.Style["background-image"] = Page.ResolveUrl("~/file/programmentorat/" + p.photo);


                objOrganisation.AfficherDetails(Convert.ToInt64(p.OrganisationId), org);
                objcom.ChargementListview(listcommentairementore, p.PostId, -1, -1, "vu", 1);
                objment.ChargerMentorsListview(listcommentmentor, -1,p.PostId,-1,"","actif",0);
                //objmento.ChargerMentoreListview(listcommentairementore, -1,p.PostId,-1,"","actif",0);
                
                objment.ChargerMentorsListview(listmentor, -1,p.PostId,-1,"","actif",0);
                objmento.ChargerMentoreListview(listmentore, -1,p.PostId,-1,"","actif",0);

                // nbrecomment.InnerText = objcom.count("", Convert.ToInt32(nscno), 0) + " Commentaire(s)";

                // objactdom.ChargerDomaines(listcategorie, "article");
              //  objactco.ChargerListView(listdompost, p.PostId, -1, 1, "actif");
               // objcom.ChargementListview(listcomment, p.PostId, -1, -1, "vu", 1);
                // objtabl.Chargement_GDV(gdvtag, Convert.ToInt64(nscno), 0);

            }
        }
        void ViderMentor()
        {
            txttitre.Value = "";
            txtqualification.Value = "";
            envoiementor.Visible = false;

        }
        void InscriptionMentor()
        {
            try
            {
                if (ide > 0)
                {
                    
                    if (txtqualification.Value!="" || txttitre.Value!="")
                    {
                        obj.AfficherDetailsname(slug, -1, 1, p);//appel du post

                        if (objment.count(-1, p.PostId, idorg, ide,1) > 0)
                        {
                            Response.Write("<script LANGUAGE=JavaScript>alert('Vous êtes deja inscrit dans ce programme en tant que mentor')</script>");
                            return;
                        }

                        ment.MembreId = ide;
                        ment.DomaineExpertise = txtqualification.Value;
                        ment.name = txttitre.Value;
                        obj.AfficherDetailsname(slug, -1, 1, p);//appel du post
                        objMembre.AfficherDetails(ide, mem);
                        ment.ProgrammeMentoratId = Convert.ToInt32(p.PostId);
                        ment.DateAccept = "";
                        ment.DateInscription = DateTime.Now.ToString();
                        ment.OrganisationId = idorg;
                        ment.rate = "";
                        ment.status = "en attente";


                        info = objment.Ajouter(ment);

                        if (info == 1)
                        {
                            Response.Write("<script LANGUAGE=JavaScript>alert('Demande a été envoyé, attendez votre confirmation et vous recevrez un message dans votre boîte mail')</script>");
                            ViderMentor();
                        }
                        else
                        {
                            Response.Write("<script LANGUAGE=JavaScript>alert('Demande a été envoyé')</script>");

                        }
                    }
                    else
                    {
                        Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez le titre et vos qualifications')</script>");

                    }


                }
                else {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Vous devez vous connecter pour faire cette demande')</script>");

                }
            }
            catch (Exception e)
            {

            }
           
        }

        void InscriptionMentoree()
        {
            try
            {
                if (ide > 0)
                {
                    obj.AfficherDetailsname(slug, -1, 1, p);//appel du post

                    if (objmento.count(p.PostId,idorg,ide,"","",1) >0)
                    {
                        Response.Write("<script LANGUAGE=JavaScript>alert('Vous êtes deja inscrit dans ce programme en tant que mentorée')</script>");
                        return;
                    }
                    mento.MembreId =Convert.ToInt32( ide);

                   
                    objMembre.AfficherDetails(ide, mem);
                    mento.ProgrammeMentoratId = Convert.ToInt32(p.PostId);
                    mento.DateAccept = "";
                    mento.DateInscription = DateTime.Now.ToString();
                    mento.OrganisationId = idorg;
                    mento.rate = "";
                    ment.status = "actif";

                    info = objmento.Ajouter(mento);

                    if (info == 1)
                    {
                        Response.Write("<script LANGUAGE=JavaScript>alert('Demande a été envoyé')</script>");

                    }
                    else
                    {
                        Response.Write("<script LANGUAGE=JavaScript>alert('Demande a été envoyé')</script>");

                    }

                }
                else {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Vous devez vous connecter pour faire cette demande')</script>");

                }
            }
            catch (Exception e)
            {

            }

        }
        public void parcours()
        {
            if (slug !=null)
            {
                obj.AfficherDetailsname(slug, -1, 1, p);//appel du post
                                                        // Span contenant le texte "Ouvert" ou "Fermé"
                
                if (p.EstPublieEvent == "cloturé")
                {
                    spanStatut.Attributes["class"] = "badge";
                    spanStatut.Style["background-color"] = "red";

                }
                else if (p.EstPublie == "ouvert")
                {
                    spanStatut.Attributes["class"] = "badge bg-success";
                    // couleur verte maintenue
                }
                if (Convert.ToDateTime(p.DateModification) < DateTime.Now)
                {
                    envoiementor.Visible = false;
                    linkmentor.Visible=false;
                    linkmentore.Visible=false;
                    //linkmentor.Disabled = false;
                    //linkmentore.Disabled = false;
                    //  txtstatut. il faut aussi desactivé le statut

                }

            } else Response.Redirect("~/programmementorat.aspx");

        }
        protected void btn_srch_Click(object sender, EventArgs e)
        {
            //if (txt_srch.Value == "")
            //    obj.Chargement_GDV(listv, 0, "blog", 5);
            //else obj.searchlist(listv, "blog", 0, 0, txt_srch.Value);
        }

        protected void voirtouract_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/news.aspx");
        }

        protected void linkmentor_ServerClick(object sender, EventArgs e)
        {
            envoiementor.Visible = true;

        }

        protected void btninscriptionmentor_ServerClick(object sender, EventArgs e)
        {
            InscriptionMentor();
        }

        protected void linkmentore_ServerClick(object sender, EventArgs e)
        {
            InscriptionMentoree();
        }

        protected void voirtouractblog_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/newsupcome.aspx");
        }
        protected void bntcomment_Click(object sender, EventArgs e)
        {
            commentaire();
            //nbrecomment.InnerText = objcom.count("", Convert.ToInt32(nscno), 0) + " Commentaire(s)";

        }


        protected void listcategorie_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            //int index = Convert.ToInt32(e.CommandArgument);
            //if (e.CommandName == "viewcat")
            //{
            //    princip.Visible = false;
            //    second.Visible = true;

            //    obj.Chargement_GDV(listblog, index, "blog", 1);

            //}
        }

        protected void btnenreg_ServerClick(object sender, EventArgs e)
        {
            if (slug != null && ide > 0)
            {
                commentaire();
            }
            else Response.Redirect("~/login.aspx");
        }
    }
}