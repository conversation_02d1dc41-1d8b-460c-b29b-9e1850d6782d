﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="program.aspx.cs" Inherits="LinCom.program" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
<main class="main">

  <!-- HÉRO -->
  <section class="hero d-flex align-items-center text-white" runat="server" id="sectblogdetail" style="background: linear-gradient(rgba(0,131,116,0.8), rgba(0,131,116,0.8)), url('assets/img/blog/skills.png') center/cover no-repeat; height: 80vh;">
    <div class="container text-center">
      <h1 class="display-4 fw-bold" runat="server" id="txttitle">Programme "Développement des Compétences"</h1>
     </div>
  </section>

<section class="py-5" style="background: linear-gradient(135deg, #f0fdfa, #e6f7f5);">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-10 text-center">
        <div class="p-5 rounded-4 shadow-sm bg-white border-top border-4 border-008374">
          <div class="mb-4">
            <i class="bi bi-lightbulb-fill fs-1 text-008374"></i>
          </div>
          <h2 class="text-008374 fw-bold mb-3">Aperçu du Programme</h2>
          <p class="fs-5 mb-0 text-muted" runat="server" id="txtdescription">
            Ce programme vise à former 500 jeunes et femmes dans les domaines du <strong>numérique</strong>,
            de <strong>l'agriculture intelligente</strong> et de <strong>l'entrepreneuriat</strong>
            dans les régions rurales.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>



  <!-- CARDS -->
  <section class="py-5">
    <div class="container">
      <div class="row text-center g-4">
        <div class="col-md-4">
          <div class="p-4 rounded-4 shadow-sm border-top border-4 border-008374">
            <i class="bi bi-calendar-event-fill fs-1 text-008374 mb-3"></i>
            <h5><b>Durée</b></h5>
            <p runat="server" id="txtduree">Janvier - Décembre 2025</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="p-4 rounded-4 shadow-sm border-top border-4 border-008374">
            <i class="bi bi-geo-alt-fill fs-1 text-008374 mb-3"></i>
            <h5><b>Zones ciblées</b></h5>
            <p runat="server" id="txtzone">Hauts-Bassins, Boucle du Mouhoun</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="p-4 rounded-4 shadow-sm border-top border-4 border-008374">
            <i class="bi bi-people-fill fs-1 text-008374 mb-3"></i>
            <h5><b>Bénéficiaires</b></h5>
            <p runat="server" id="txtbeneficiaire">500 jeunes et femmes</p>
          </div>
        </div>
      </div>
    </div>
  </section>

 <section class="py-5" style="background: #f8fdfa;">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="text-008374 fw-bold">Domaines d’intervention de ce programme</h2>
       </div>
    <div class="row g-4">
          <!-- Domaine 1 -->
        <asp:ListView ID="listdompost" runat="server">
    <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
    <ItemTemplate>
  <div class="col-md-6 col-lg-3">
    <div class="card h-100 border-top border-4 border-008374 shadow-sm rounded-4 text-center p-4">
      <div class="mb-3"><i class="bi bi-lightbulb-fill fs-1 text-008374"></i></div>
      <h5 class="fw-bold"><%# Eval("PostTitre") %></h5>
     </div>
  </div>
            </ItemTemplate>
</asp:ListView>
     
    </div>
  </div>
</section>


  <!-- AUTEUR -->
  <section class="py-5">
    <div class="container">
      <div class="d-flex align-items-center">
        <img src="assets/img/avatar.png" runat="server" id="imgauteur" alt="Auteur" class="rounded-circle me-4" width="80" height="80">
        <div>
          <h5 class="mb-1 text-008374 fw-bold" runat="server" id="txtauteur">Jean Kaboré</h5>
        
        </div>
      </div>
    </div>
  </section>

  <!-- AUTRES PROGRAMMES -->
  <section class="py-5 bg-light">
    <div class="container">
      <h3 class="text-center text-008374 fw-bold mb-5">Autres Programmes</h3>
      <div class="row g-4">

           <asp:ListView ID="listprogram" runat="server" OnItemCommand="listpost_ItemCommand">
     <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
     <ItemTemplate>
        <div class="col-md-4">
          <div class="card border-0 shadow-sm rounded-4 h-100">
            <img src='<%# HttpUtility.HtmlEncode( string.Concat("../file/actualit/",Eval("photo"))) %>' class="card-img-top rounded-top-4" alt="Programme">
            <div class="card-body">
                  <h5 class="card-title text-008374 fw-bold"><%# HttpUtility.HtmlEncode( Eval("Titre")) %></h5>
                
                <asp:LinkButton  runat="server" class="btn btn-outline-008374" CommandName="view" CommandArgument='<%# Eval("name") %>'>Lire Plus</asp:LinkButton>
            
            </div>
          </div>
        </div>
             </ItemTemplate>
</asp:ListView>

      </div>
    </div>
  </section>

  <!-- COMMENTAIRES -->
 <section class="py-5 bg-light">
  <div class="container">
       <h4 class="comments-count"><span runat="server" id="txtcommentpost">8</span></h4>

    <!-- Liste des commentaires -->
    <div class="mb-4">
         <asp:ListView ID="listcomment" runat="server">
     <EmptyDataTemplate>
     <p class="text-muted fst-italic">Soyez le premier à réagir !</p>
 </EmptyDataTemplate>
     <ItemTemplate>
      <div class="d-flex mb-4">
        <img src='<%# HttpUtility.HtmlEncode( string.Concat("../file/membr/",Eval("photocommentateur"))) %>' alt='<%#  HttpUtility.HtmlEncode(Eval("commentateur")) %>' class="rounded-circle me-3" width="50" height="50">
        <div class="bg-white p-3 rounded shadow-sm w-100">
          <h6 class="mb-1 text-008374"><%#  HttpUtility.HtmlEncode(Eval("commentateur")) %></h6>
          <small class="text-muted"><%#  HttpUtility.HtmlEncode(Eval("dateComm")) %></small>
          <p class="mt-2 mb-0"> <%# Eval("contenu") %></p>
        </div>
      </div>
             </ItemTemplate>
</asp:ListView>

    </div>

    <!-- Formulaire de commentaire -->
     <!-- Contact Section -->
  <section id="contact" class="contact section">

      <!-- Section Title -->
      <div class="container section-title" data-aos="fade-up">
          <h2>Poster votre Commentaire</h2>

      </div>
      <!-- End Section Title -->

      <div class="container" data-aos="fade-up" data-aos-delay="100">

          <div class="row gx-lg-0 gy-4">


              <div class="col-lg-12">
                  <div class="php-email-form" data-aos="fade" data-aos-delay="100">
                      <div class="row gy-4">


                          <div class="col-md-12">
                              <textarea class="form-control" runat="server" id="txtmessage" name="message" rows="8" placeholder="Message" required=""></textarea>
                          </div>

                          <div class="col-md-12 text-center">
                              <div class="loading">Loading</div>
                              <div class="error-message"></div>
                              <div class="sent-message">Your message has been sent. Thank you!</div>

                              <button type="submit" runat="server" id="btnenreg" onserverclick="btnenreg_ServerClick">Poster votre commentaire</button>
                          </div>

                      </div>
                  </div>
              </div>
              <!-- End Contact Form -->

          </div>

      </div>

  </section>
  <!-- /Contact Section -->
  </div>
</section>


<section class="pb-5 text-center">
  <div class="container">
       <h6><span>Publié le </span><span runat="server" id="txtdatepublication"></span></h6>

    <h5 class="text-008374 fw-bold mb-3">Partager ce programme sur </h5>
   
    <div class="d-flex justify-content-center flex-wrap gap-3 mt-4">
        <asp:HiddenField ID="hfUrlPage" runat="server" />

      <!-- Facebook -->
      <a href='https://www.facebook.com/sharer/sharer.php?u=<%= hfUrlPage.Value %>'
         class="btn btn-lg rounded-circle shadow-sm text-white bg-primary"
         target="_blank" title="Partager sur Facebook">
        <i class="bi bi-facebook fs-4"></i>
      </a>

      <!-- Twitter/X -->
      <a href='https://twitter.com/intent/tweet?url=<%= hfUrlPage.Value %>&text=Découvrez ce programme !'
         class="btn btn-lg rounded-circle shadow-sm text-white"
         style="background-color: #000000;" target="_blank"
         title="Partager sur X (Twitter)">
        <i class="bi bi-twitter-x fs-4"></i>
      </a>

      <!-- WhatsApp -->
      <a href='https://wa.me/?text=<%= hfUrlPage.Value %>'
         class="btn btn-lg rounded-circle shadow-sm text-white"
         style="background-color: #25D366;" target="_blank"
         title="Partager sur WhatsApp">
        <i class="bi bi-whatsapp fs-4"></i>
      </a>

      <!-- LinkedIn -->
      <a href='https://www.linkedin.com/sharing/share-offsite/?url=<%= hfUrlPage.Value %>'
         class="btn btn-lg rounded-circle shadow-sm text-white"
         style="background-color: #0077b5;" target="_blank"
         title="Partager sur LinkedIn">
        <i class="bi bi-linkedin fs-4"></i>
      </a>

      <!-- Copier le lien -->
      <button type="button" class="btn btn-lg rounded-circle shadow-sm text-white bg-secondary"
              title="Copier le lien" onclick="copyToClipboard()">
        <i class="bi bi-link-45deg fs-4"></i>
      </button>

    </div>
  </div>
</section>

</main>

<!-- STYLE -->
<style>
  .text-008374 { color: #008374; }
  .bg-008374 { background-color: #008374; }
  .border-008374 { border-color: #008374 !important; }
  .btn-008374 { background-color: #008374; color: #fff; }
  .btn-outline-008374 { border: 1px solid #008374; color: #008374; }
  .btn-outline-008374:hover { background-color: #008374; color: #fff; }

    .text-008374 { color: #008374; }
  .btn-008374 {
    background-color: #008374;
    color: #fff;
    border: none;
  }
  .btn-008374:hover {
    background-color: #006e64;
  }
   .text-008374 { color: #008374; }
 .border-008374 { border-color: #008374 !important; }

</style>

    <script>
  function copyToClipboard() {
    const url = document.getElementById('<%= hfUrlPage.ClientID %>').value;
    navigator.clipboard.writeText(url).then(() => {
      alert("Lien copié dans le presse-papiers !");
    });
  }
    </script>

</asp:Content>

