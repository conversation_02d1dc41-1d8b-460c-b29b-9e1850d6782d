﻿using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;


namespace LinCom.Imp
{
    public class CommonCode : ICommonCode
    {
        public string RemoveDiacritics(string text)
        {
            var normalized = text.Normalize(NormalizationForm.FormD);
            var sb = new StringBuilder();

            foreach (var c in normalized)
            {
                var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    sb.Append(c);
                }
            }

            return sb.ToString().Normalize(NormalizationForm.FormC);
        }

        public string GenerateSlug(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return string.Empty;

            // Convertir en minuscules et supprimer les accents
            string str = RemoveDiacritics(input.ToLowerInvariant());

            // Supprimer tout sauf les lettres et les chiffres
            str = Regex.Replace(str, @"[^a-z0-9]", "");

            return str;
        }

        public  void EnvoyerEmail(string destinataire, string sujet, string message, string fournisseur)
        {
            string host = "";
            int port = 587;
            string username = "";
            string password = "";
            bool enableSsl = true;

            if (fournisseur == "gmail")
            {
                host = ConfigurationManager.AppSettings["MailHostGmail"];
                port = int.Parse(ConfigurationManager.AppSettings["MailPortGmail"]);
                username = ConfigurationManager.AppSettings["MailUserGmail"];
                password = ConfigurationManager.AppSettings["MailPassGmail"];
                enableSsl = bool.Parse(ConfigurationManager.AppSettings["MailEnableSslGmail"]);
            }
            else if (fournisseur == "one")
            {
                host = ConfigurationManager.AppSettings["MailHostOne"];
                port = int.Parse(ConfigurationManager.AppSettings["MailPortOne"]);
                username = ConfigurationManager.AppSettings["MailUserOne"];
                password = ConfigurationManager.AppSettings["MailPassOne"];
                enableSsl = bool.Parse(ConfigurationManager.AppSettings["MailEnableSslOne"]);
            }

            MailMessage mail = new MailMessage();
            mail.From = new MailAddress(username);
            mail.To.Add(destinataire);
            mail.Subject = sujet;
            mail.Body = message;
            mail.IsBodyHtml = true;

            SmtpClient smtp = new SmtpClient(host, port);
            smtp.Credentials = new NetworkCredential(username, password);
            smtp.EnableSsl = enableSsl;

            smtp.Send(mail);
        }
        public void EnvoyerEmailTousMembres(long idmem, string messageenvoye, string sujet,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    // Récupérer tous les membres actifs avec un email valide
                    var membres = con.Membres
                                     .Where(m => m.statut == "actif" && !string.IsNullOrEmpty(m.Email) &&
                                     m.MembreId != idmem) // exclure l'expéditeur)
                                     .ToList();

                    foreach (var membre in membres)
                    {
                        string fournisseur = membre.Email.EndsWith("@gmail.com") ? "gmail" : "one";

                        string message = $"<p>Bonjour {membre.Nom} {membre.Prenom},</p><br/><br/> {messageenvoye}";

                        // string message = messageenvoye;

                        try
                        {

                            EnvoyerEmail(membre.Email, sujet, message, fournisseur);
                        }
                        catch (Exception ex)
                        {
                            // Tu peux logguer l'erreur ici si nécessaire
                            System.Diagnostics.Debug.WriteLine($"Erreur lors de l'envoi à {membre.Email}: {ex.Message}");
                        }
                    }
                }
                else if (cd==1)
                {
                    //envoie aux organisations
                    // Récupérer tous les membres actifs avec un email valide
                    var membres = con.Organisations
                                     .Where(m => m.Statut == "actif" && !string.IsNullOrEmpty(m.Email) &&
                                     m.OrganisationId != idmem) // exclure l'expéditeur)
                                     .ToList();

                    foreach (var membre in membres)
                    {
                        string fournisseur = membre.Email.EndsWith("@gmail.com") ? "gmail" : "one";

                        string message = messageenvoye;

                        try
                        {

                            EnvoyerEmail(membre.Email, sujet, message, fournisseur);
                        }
                        catch (Exception ex)
                        {
                            // Tu peux logguer l'erreur ici si nécessaire
                            System.Diagnostics.Debug.WriteLine($"Erreur lors de l'envoi à {membre.Email}: {ex.Message}");
                        }
                    }
                }
                  
            }
        }

        public string HasherMotDePasse(string motDePasse)
        {
            return BCrypt.Net.BCrypt.HashPassword(motDePasse);
        }

        public bool VerifierMotDePasse(string motDePasseEntree, string motDePasseHache)
        {
            return BCrypt.Net.BCrypt.Verify(motDePasseEntree, motDePasseHache);
        }
        public string GenererToken()
        {
            using (var rng = new RNGCryptoServiceProvider())
            {
                byte[] tokenData = new byte[32];
                rng.GetBytes(tokenData);
                return Convert.ToBase64String(tokenData).Replace("+", "").Replace("/", "").Replace("=", "");
            }
        }
        public string GetRelativeDate(DateTime date)
        {
            var now = DateTime.Now.Date;
            var diff = now - date.Date;

            if (diff.TotalDays == 0)
                return "aujourd’hui";
            else if (diff.TotalDays == 1)
                return "hier";
            else if (diff.TotalDays < 7)
                return $"il y a {diff.Days} jours";
            else if (diff.TotalDays < 31)
                return $"il y a {diff.Days / 7} semaine(s)";
            else if (diff.TotalDays < 365)
                return $"il y a {diff.Days / 30} mois";
            else
                return $"il y a {diff.Days / 365} an(s)";
        }
        public  string TronquerTexte(object texteObj, int longueurMax)
        {
            if (texteObj == null) return string.Empty;
            string texte = texteObj.ToString();

            if (texte.Length <= longueurMax) return texte;

            int dernierEspace = texte.LastIndexOf(' ', longueurMax);
            if (dernierEspace > 0)
                return texte.Substring(0, dernierEspace) + "...";

            return texte.Substring(0, longueurMax) + "...";
        }

    }
}