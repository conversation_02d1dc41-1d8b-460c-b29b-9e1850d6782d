﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface INotification
    {
        void AfficherDetails(long notificationId, Notification_Class notification);
        int Ajouter(Notification_Class n);
        int Modifier(Notification_Class n);
        int Supprimer(long notificationId);
        void ChargerNotificationsGdv(GridView gdv, long membreId);
        void ChargerNotificationsRepeater(Repeater gdv, long membreId, int lu, string statut, int cd);
        void ChargerNotificationsListview(ListView gdv, long membreId);
        int MarquerCommeLue(long notificationId);
        void ChargerNotificationsParType(GridView gdv, string type);
        int EnvoyerNotificationGroupe(Notification_Class model, long[] membreIds);
        void ChargerStatistiques(Repeater rpt, long membreId);
        void PurgerAnciennesNotifications(int joursConservation);
        int MarquerToutCommeLues(long membreId);
        int CompterNonLues(long membreId);
        void EnvoyerNotificationAction(long idAuteur, string titreSujet, string contenu, string lienSujet, string name, string statut);
        void EnvoyerNotificationActionOrganisation(long idAuteur, string titreSujet, string contenu, string lienSujet, string name, string statut);

    }
}
