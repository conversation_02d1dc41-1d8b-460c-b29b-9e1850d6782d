using LinCom.Model;
using System;

namespace LinCom.Class
{
    public class ParticipantConversation_Class
    {
        public long ParticipantId { get; set; }
        public long? ConversationId { get; set; }
        public long? MembreId { get; set; }
        public Nullable<System.DateTime> JoinedAt { get; set; }

        // Navigation properties
        public virtual Conversation Conversation { get; set; }

        public virtual Membre Membre { get; set; }
    }
}
