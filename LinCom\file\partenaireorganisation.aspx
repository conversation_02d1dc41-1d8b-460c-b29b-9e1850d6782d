﻿<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="partenaireorganisation.aspx.cs" Inherits="LinCom.file.partenaireorganisation" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
      <div class="breadcomb-area">
      <div class="container">
          <div class="row">
              <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                  <div class="breadcomb-list">
                      <div class="row">
                          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                              <div class="breadcomb-wp">
                                  <div class="breadcomb-icon">
                                      <i class="notika-icon notika-form"></i>
                                  </div>
                                  <div class="breadcomb-ctn">
                                      <h2>Partenaire de la Plateforme</h2>
                                  </div>
                              </div>
                          </div>
                          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                              <div class="breadcomb-report">

                                  <a data-toggle="tooltip" data-placement="left" href="listpartenaire.aspx" title="Clique sur ce button pour visualiser la liste des Partenaire de la Plateforme" class="btn">Liste des Partenaire de la Plateforme</a>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>
  <!-- Breadcomb area End-->
  <!-- Form Element area Start-->
  <div class="form-element-area">
      <div class="container">
          <div class="row">
               <div class="alert-list">
    <asp:Panel ID="div_msg_succes" runat="server" CssClass="alert alert-success alert-dismissible" Visible="false">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
        <asp:Label ID="msg_succes" runat="server" Text="Enregistrement réussi"></asp:Label>
    </asp:Panel>
    <asp:Panel ID="div_msg_error" runat="server" CssClass="alert alert-danger alert-dismissible" Visible="false">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
        <asp:Label ID="msg_error" runat="server" Text="Enregistrement échoué"></asp:Label>
    </asp:Panel>
</div>
              <div class="row">

                  <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                      <div class="form-group ic-cmp-int">
                          <div class="form-ic-cmp">
                              <i class="notika-icon notika-house"></i>
                          </div>
                          <div class="nk-int-st">
                              <label>Nom du partenaire *</label>
                              <input type="text" runat="server" id="txtnm" class="form-control" placeholder="Nom du partenaire *">
                          </div>
                      </div>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                      <div class="form-group ic-cmp-int">
                          <div class="form-ic-cmp">
                              <i class="notika-icon notika-house"></i>
                          </div>
                          <div class="nk-int-st">
                              <label>Contact(Téléphone) </label>
                              <input type="text" runat="server" id="txttel" class="form-control" placeholder="Contact(Téléphone) ">
                          </div>
                      </div>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                      <div class="form-group ic-cmp-int">
                          <div class="form-ic-cmp">
                              <i class="notika-icon notika-house"></i>
                          </div>
                          <div class="nk-int-st">
                              <label>Email</label>
                              <input type="email" runat="server" id="txtemail" class="form-control" placeholder="Email">
                          </div>
                      </div>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                      <div class="form-group ic-cmp-int">
                          <div class="form-ic-cmp">
                              <i class="notika-icon notika-house"></i>
                          </div>
                          <div class="nk-int-st">
                              <label>Type de partenaire (ex. Financier, Technique,Compétence,etc...) *</label>
                              <input type="text" runat="server" id="txttype" class="form-control" placeholder="Type de partenaire *">
                          </div>
                      </div>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                      <div class="form-group ic-cmp-int">
                          <div class="form-ic-cmp">
                              <i class="notika-icon notika-house"></i>
                          </div>
                          <div class="nk-int-st">
                              <label>Lien du Site web </label>
                              <input type="text" runat="server" id="txtlien" class="form-control" placeholder="Lien du Site web">
                          </div>
                      </div>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                      <div class="form-group ic-cmp-int">
                          <div class="form-ic-cmp">
                              <i class="notika-icon notika-edit"></i>
                          </div>
                          <div class="nk-int-st">
                              <label>Statut *</label>
                              <asp:DropDownList class="form-control" ID="drpdstatut" runat="server">

                                  <asp:ListItem Value="-1">Selectionner l'etat du Programmes & Initiatives</asp:ListItem>
                                  <asp:ListItem Value="en attente">En attente</asp:ListItem>
                                  <asp:ListItem Value="publié">Publié</asp:ListItem>
                                  <asp:ListItem Value="non publié">Non publié</asp:ListItem>
                              </asp:DropDownList>
                          </div>
                      </div>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                      <div class="form-group ic-cmp-int">
                          <div class="form-ic-cmp">
                              <i class="notika-icon notika-picture"></i>
                          </div>
                          <div class="nk-int-st">
                              <label>Logo *</label>
                              <asp:FileUpload ID="fileupd" runat="server" class="form-control" />

                          </div>
                      </div>
                  </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
    <div class="form-group ic-cmp-int">
        <div class="form-ic-cmp">
            <i class="notika-icon notika-form"></i>
        </div>
        <div class="nk-int-st">
            <label>Resumé de description</label>
            <div>

                <textarea class="html-editor" runat="server" id="txtresume" rows="10"></textarea>

            </div>

        </div>
    </div>
</div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
    <button type="button" runat="server" id="btnEnregistrer" onserverclick="btnEnregistrer_ServerClick" class="btn btn-success notika-gp-success">Ajouter</button>
</div>
              </div>
             
          </div>
      </div>

  </div>
  <!-- Form Element area End-->
</asp:Content>
