﻿<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" ValidateRequest="false" AutoEventWireup="true" CodeBehind="envoiemail.aspx.cs" Inherits="LinCom.file.envoiemail" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Breadcomb area Start-->
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">
                                    <div class="breadcomb-icon">
                                        <i class="notika-icon notika-mail"></i>
                                    </div>
                                    <div class="breadcomb-ctn">
                                        <h2>Composer les Emails </h2>

                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcomb area End-->
    <!-- Compose email area Start-->
    <div class="inbox-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-12">
                    <div class="inbox-left-sd">
                        <div class="compose-ml">
                            <a class="btn" href="#">Compose</a>
                        </div>
                        <div class="inbox-status">
                            <ul class="inbox-st-nav inbox-ft">
                                <li><a href="#"><i class="notika-icon notika-mail"></i>Inbox<span class="pull-right">12</span></a></li>
                                <li><a href="#"><i class="notika-icon notika-sent"></i>Sent</a></li>
                                <li><a href="#"><i class="notika-icon notika-draft"></i>Draft</a></li>
                                <li><a href="#"><i class="notika-icon notika-trash"></i>Trash</a></li>
                            </ul>
                        </div>
                        <hr>
                        <div class="inbox-status">
                            <ul class="inbox-st-nav">
                                <li><a href="#"><i class="notika-icon notika-travel"></i>Travel</a></li>
                                <li><a href="#"><i class="notika-icon notika-finance"></i>Finance</a></li>
                                <li><a href="#"><i class="notika-icon notika-social"></i>Social</a></li>
                                <li><a href="#"><i class="notika-icon notika-promos"></i>Promos</a></li>
                                <li><a href="#"><i class="notika-icon notika-flag"></i>Updates</a></li>
                            </ul>
                        </div>
                        <hr>
                        <div class="inbox-status">
                            <ul class="inbox-st-nav inbox-nav-mg">
                                <li><a href="#"><i class="notika-icon notika-success"></i>Forum</a></li>
                                <li><a href="#"><i class="notika-icon notika-chat"></i>Chat</a></li>
                                <li><a href="#"><i class="notika-icon notika-star"></i>Work</a></li>
                                <li><a href="#"><i class="notika-icon notika-settings"></i>Settings</a></li>
                                <li><a href="#"><i class="notika-icon notika-support"></i>Support</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-9 col-md-9 col-sm-8 col-xs-12">
                     <div class="alert-list">
     <asp:Panel ID="div_msg_succes" runat="server" CssClass="alert alert-success alert-dismissible" Visible="false">
         <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
         <asp:Label ID="msg_succes" runat="server" Text="Enregistrement réussi"></asp:Label>
     </asp:Panel>
     <asp:Panel ID="div_msg_error" runat="server" CssClass="alert alert-danger alert-dismissible" Visible="false">
         <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
         <asp:Label ID="msg_error" runat="server" Text="Enregistrement échoué"></asp:Label>
     </asp:Panel>
 </div>
                    <div class="view-mail-list sm-res-mg-t-30">
                        <div class="view-mail-hd">
                            <div class="view-mail-hrd">
                                <h2>Nouveau Message</h2>
                            </div>
                        </div>
                        <div class="cmp-int mg-t-20">
                            <div class="row">
                                <div class="col-lg-1 col-md-2 col-sm-2 col-xs-12">
                                    <div class="cmp-int-lb cmp-int-lb1 text-right">
                                        <span>Type: </span>
                                    </div>
                                </div>
                                <div class="col-lg-11 col-md-10 col-sm-10 col-xs-12">

                                    <asp:DropDownList ID="drpdtype" runat="server" class="form-control">
                                        <asp:ListItem Value="tous">Envoyez à tous(organisations, membres, abonnés)</asp:ListItem>
                                        <asp:ListItem Value="organisation">Envoyez aux organisations</asp:ListItem>
                                       
                                    </asp:DropDownList>

                                </div>
                                </div>
                                <div class="row">
                                <div class="col-lg-1 col-md-2 col-sm-2 col-xs-12">
                                    <div class="cmp-int-lb cmp-int-lb1 text-right">
                                        <span>To: </span>
                                    </div>
                                </div>
                                <div class="col-lg-11 col-md-10 col-sm-10 col-xs-12">
                                    <div class="form-group">
                                        <div class="nk-int-st cmp-int-in cmp-email-over">
                                            <input type="email" class="form-control" placeholder="<EMAIL>" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                           
                            <div class="row">
                                <div class="col-lg-1 col-md-2 col-sm-2 col-xs-12">
                                    <div class="cmp-int-lb text-right">
                                        <span>Subject: </span>
                                    </div>
                                </div>
                                <div class="col-lg-11 col-md-10 col-sm-10 col-xs-12">
                                    <div class="form-group cmp-em-mg">
                                        <div class="nk-int-st cmp-int-in cmp-email-over">
                                            <input type="text" runat="server" id="txttitre" class="form-control" placeholder="" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="cmp-int-box mg-t-20">
                            <div>
                                  <textarea runat="server" id="txtDescription" class="html-editor-cm" placeholder="Contenu message  *" rows="4"></textarea>
                   
                            </div>
                        </div>
                      
                        <div class="vw-ml-action-ls text-right mg-t-20">
                            <div class="btn-group ib-btn-gp active-hook nk-email-inbox">
                                  <button type="button" runat="server" id="btn_ajouter" onserverclick="btn_ajouter_ServerClick" class="btn btn-success notika-gp-success">Ajouter</button>
       
                                 </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Compose email area End-->
</asp:Content>
