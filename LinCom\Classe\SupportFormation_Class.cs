﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace LinCom.Classe
{
    public class SupportFormation_Class
    {
        public long SupportFormationId { get; set; }
        public long FormationId { get; set; }
        public string Titre { get; set; }
        public string Fichier { get; set; }
        public string name { get; set; }
        public Nullable<System.DateTime> DateCreation { get; set; }

        public string statut { get; set; }

        public string DatePublication { get; set; }

        public string NomSupport { get; set; }

        public string Statut { get; set; }

        public Nullable<long> MembreId { get; set; }
    }
}