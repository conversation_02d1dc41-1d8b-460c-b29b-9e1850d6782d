﻿using LinCom.Class;
using LinCom.file;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class MentorImp: IMentor
    {
        int msg,n;
        private Mentor mentor = new Mentor();

        public void AfficherDetails(int mentorId, Mentor_Class mentorClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Mentors.FirstOrDefault(x => x.MentorId == mentorId);
                if (m != null)
                {
                    mentorClass.MentorId = m.MentorId;
                    mentorClass.MembreId = m.MembreId;
                    mentorClass.DomaineExpertise = m.DomaineExpertise;
                    mentorClass.name = m.name;
                    mentorClass.ProgrammeMentoratId = m.ProgrammeMentoratId;
                    mentorClass.status = m.status;
                    mentorClass.DateInscription = m.DateInscription;
                    mentorClass.DateAccept = m.DateAccept;
                    mentorClass.OrganisationId = m.OrganisationId;
                    mentorClass.rate = m.rate;


    }
            }
        }

        public int Ajouter(Mentor_Class mentorClass)
        {
            using (Connection con = new Connection())
            {
                mentor.MembreId = mentorClass.MembreId;
                mentor.DomaineExpertise = mentorClass.DomaineExpertise;
                mentor.name = mentorClass.name;
                mentor.ProgrammeMentoratId = mentorClass.ProgrammeMentoratId;
                mentor.status = mentorClass.status;
                mentor.DateInscription = mentorClass.DateInscription;
                mentor.DateAccept = mentorClass.DateAccept;
                mentor.OrganisationId = mentorClass.OrganisationId;
                mentor.rate = mentorClass.rate;

                try
                {
                    con.Mentors.Add(mentor);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

       
        public void ChargerMentorsGridview(GridView gdv, int id, long idprog, long idorg, string name, string status, int cd)
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Mentors
                            join mb in con.Membres on m.MembreId equals mb.MembreId into membres
                            from membre in membres.DefaultIfEmpty()
                            join p in con.Programmementorats on m.ProgrammeMentoratId equals p.ProgrammeMentoratId into programmes
                            from programme in programmes.DefaultIfEmpty()
                            where m.ProgrammeMentoratId == idprog && m.OrganisationId==idorg && m.status == status
                            select new
                            {
                                id=m.MentorId,
                                idmembre=m.MembreId,
                                Membre = membre != null ? membre.Nom+" "+membre.Prenom : "Inconnu",
                                domaineexpertise=m.DomaineExpertise,
                                name =m.name,
                                PostId =m.ProgrammeMentoratId,
                                Programme = programme != null ? programme.Titre : "Aucun",
                                statut=m.status,
                                DateInscription = m.DateInscription,
                                DateAccept = m.DateAccept,
                                OrganisationId = m.OrganisationId,
                                rate=m.rate,

            }
            ;

                gdv.DataSource = query.OrderBy(x => x.Membre).ToList();
                gdv.DataBind();
            }
        }
        public void ChargerMentorsListview(ListView gdv, int id,long idprog, long idorg,string name, string status,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var query = from m in con.Mentors
                                join mb in con.Membres on m.MembreId equals mb.MembreId into membres
                                from membre in membres.DefaultIfEmpty()
                                join p in con.Programmementorats on m.ProgrammeMentoratId equals p.ProgrammeMentoratId into programmes
                                from programme in programmes.DefaultIfEmpty()
                                where m.ProgrammeMentoratId==idprog && m.status==status
                                select new
                                {
                                    id = m.MentorId,
                                    idmembre = m.MembreId,
                                    Membre = membre != null ? membre.Nom + " " + membre.Prenom : "Inconnu",
                                    Photomembre = membre.PhotoProfil,
                                    domaineexpertise = m.DomaineExpertise,
                                    name = m.name,
                                    PostId = m.ProgrammeMentoratId,
                                    Programme = programme != null ? programme.Titre : "Aucun",
                                    statut = m.status,
                                    DateInscription = m.DateInscription,
                                    DateAccept = m.DateAccept,
                                    OrganisationId = m.OrganisationId,
                                    rate = m.rate,

                                }
            ;

                    gdv.DataSource = query.OrderBy(x => x.Membre).ToList();
                    gdv.DataBind();
                }
                
            }
        }

        public void chargerMentors(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Mentors select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner le Mentor";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.MentorId.ToString();
                        item.Text = data.name;
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }
        public int count(int id, long idprog, long idmem, long idorg, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var b = con.Mentors.Count(l => l.ProgrammeMentoratId == idprog);

                    n = b;
                }
                else if (cd==1)
                {
                    var b = (from l in con.Mentors
                             where l.ProgrammeMentoratId == id && l.OrganisationId == idorg && l.MembreId == idmem
                             select l).Count();
                    n = b;
                }
                
                  
            }
            return n;
        }


        public void ChargerMentorsParProgramme(GridView gdv, int programmeMentoratId)
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Mentors
                            join mb in con.Membres on m.MembreId equals mb.MembreId into membres
                            from membre in membres.DefaultIfEmpty()
                            join p in con.Programmementorats on m.ProgrammeMentoratId equals p.ProgrammeMentoratId into programmes
                            from programme in programmes.DefaultIfEmpty()
                            where m.ProgrammeMentoratId == programmeMentoratId
                            select new
                            {
                                id = m.MentorId,
                                idmembre = m.MembreId,
                                Membre = membre != null ? membre.Nom + " " + membre.Prenom : "Inconnu",
                                domaineexpertise = m.DomaineExpertise,
                                name = m.name,
                                PostId = m.ProgrammeMentoratId,
                                Programme = programme != null ? programme.Titre : "Aucun",
                                statut = m.status,
                                DateInscription = m.DateInscription,
                                DateAccept = m.DateAccept,
                                OrganisationId = m.OrganisationId,
                                rate = m.rate,
                            };

                gdv.DataSource = query.OrderBy(x => x.Membre).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerMentorsParMembre(GridView gdv, long membreId)
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Mentors
                            join mb in con.Membres on m.MembreId equals mb.MembreId into membres
                            from membre in membres.DefaultIfEmpty()
                            join p in con.Programmementorats on m.ProgrammeMentoratId equals p.ProgrammeMentoratId into programmes
                            from programme in programmes.DefaultIfEmpty()
                            where m.MembreId == membreId
                            select new
                            {
                                id = m.MentorId,
                                idmembre = m.MembreId,
                                Membre = membre != null ? membre.Nom + " " + membre.Prenom : "Inconnu",
                                domaineexpertise = m.DomaineExpertise,
                                name = m.name,
                                PostId = m.ProgrammeMentoratId,
                                Programme = programme != null ? programme.Titre : "Aucun",
                                statut = m.status,
                                DateInscription = m.DateInscription,
                                DateAccept = m.DateAccept,
                                OrganisationId = m.OrganisationId,
                                rate = m.rate,
                            };

                gdv.DataSource = query.OrderBy(x => x.Programme).ToList();
                gdv.DataBind();
            }
        }

        public int Modifier(Mentor_Class mentorClass,int id, long idorg,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var m = con.Mentors.FirstOrDefault(x => x.MentorId == id);
                    if (m != null)
                    {
                        m.MembreId = mentorClass.MembreId;
                        m.DomaineExpertise = mentorClass.DomaineExpertise;
                        m.name = mentorClass.name;
                        m.ProgrammeMentoratId = mentorClass.ProgrammeMentoratId;
                        m.status = mentorClass.status;


                        if (con.SaveChanges() == 1)
                        {
                            con.Mentors.Add(m);
                            con.Entry(m).State = EntityState.Modified;
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                }
                else if (cd==1)
                {
                    var m = con.Mentors.FirstOrDefault(x => x.MentorId == id);
                    if (m != null)
                    {
                        m.DateAccept = mentorClass.DateAccept;
                       

                        if (con.SaveChanges() == 1)
                        {
                            con.Mentors.Add(m);
                            con.Entry(m).State = EntityState.Modified;
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                }

                    return msg;
            }
        }

        public int Supprimer(int mentorId)
        {
            using (Connection con = new Connection())
            {
                var m = con.Mentors.FirstOrDefault(x => x.MentorId == mentorId);
                if (m != null)
                {
                    con.Mentors.Remove(m);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}