﻿using LinCom.Class;
using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public interface IPartenaireorganisation
    {
        int Ajout(PartenaireOrganisation_Class add);
        void Chargement_GDV(GridView GV_apv,long idorg, string statu);
        void search(GridView GV_apv, string code,long idorg, string statu);
        void afficherDetails(long idorg, int code, PartenaireOrganisation_Class pr);
        int edit(PartenaireOrganisation_Class cl,long idorg, int id);
        int supprimer(long idorg, int id);
        int count(long idorg,string statu);
        void Chargement_GDV(ListView GV_apv,long idorg, string statu);
    }
}
