using System;
using System.Configuration;

namespace LinCom.Classe
{
    /// <summary>
    /// Configuration pour le module de messagerie
    /// </summary>
    public static class MessagerieConfig
    {
        // Paramètres de sécurité
        public static readonly int MaxLongueurMessage = 1000;
        public static readonly int DelaiAntiSpamSecondes = 5;
        public static readonly int MaxMessagesParConversation = 1000;
        public static readonly int MaxPieceJointeTailleMo = 10;

        // Paramètres de performance
        public static readonly int NombreMessagesParPage = 50;
        public static readonly int DelaiActualisationSecondes = 30;
        public static readonly int MaxResultatsRecherche = 20;

        // Paramètres des fonctionnalités avancées
        public static readonly bool ActiverPiecesJointes = true;
        public static readonly bool ActiverEmojis = true;
        public static readonly bool ActiverNotificationsTempsReel = false; // À activer avec SignalR
        public static readonly bool ActiverStatutsLecture = true;
        public static readonly bool ActiverThumbnails = true;

        // Paramètres de nettoyage
        public static readonly int JoursConservationMessages = 365; // 1 an
        public static readonly int JoursConservationConversationsVides = 30;

        // Extensions de fichiers autorisées
        public static readonly string[] ExtensionsAutorisees = 
        {
            ".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", 
            ".txt", ".zip", ".rar", ".mp3", ".mp4", ".avi"
        };

        // Messages d'erreur
        public static class Messages
        {
            public const string MessageTropLong = "Le message est trop long (maximum {0} caractères)";
            public const string DestinataireInvalide = "Destinataire invalide";
            public const string MessageVide = "Veuillez saisir un message";
            public const string AntiSpam = "Veuillez attendre avant d'envoyer le même message";
            public const string ErreurEnvoi = "Erreur lors de l'envoi du message";
            public const string MessageEnvoye = "Message envoyé avec succès";
            public const string ConversationIntrouvable = "Conversation introuvable";
            public const string MembreIntrouvable = "Membre introuvable";
            public const string AccesRefuse = "Accès refusé";
            public const string FichierTropGros = "Le fichier est trop volumineux (maximum {0} Mo)";
            public const string ExtensionNonAutorisee = "Type de fichier non autorisé";
            public const string PieceJointeEnvoyee = "Pièce jointe envoyée avec succès";
            public const string ErreurUpload = "Erreur lors de l'upload du fichier";
            public const string NotificationEnvoyee = "Notification envoyée";
            public const string StatutMisAJour = "Statut mis à jour";
        }

        // Méthodes utilitaires
        public static string FormatMessage(string template, params object[] args)
        {
            return string.Format(template, args);
        }

        public static bool EstExtensionAutorisee(string extension)
        {
            return Array.Exists(ExtensionsAutorisees, ext => 
                ext.Equals(extension, StringComparison.OrdinalIgnoreCase));
        }

        public static bool EstTailleFichierValide(long tailleBytesOctets)
        {
            long tailleMaxBytes = MaxPieceJointeTailleMo * 1024 * 1024;
            return tailleBytesOctets <= tailleMaxBytes;
        }
    }
}
