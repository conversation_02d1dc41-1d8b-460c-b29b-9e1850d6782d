﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class BudgetsActiviteImp : IBudgetsActivite
    {
        private budgets_activite budget = new budgets_activite();
        int msg;

        public void AfficherDetails(int idBudget, BudgetsActivite_Class budgetsActiviteClass)
        {
            using (Connection con = new Connection())
            {
                var b = con.budgets_activite.FirstOrDefault(x => x.id_budget == idBudget);
                if (b != null)
                {
                    budgetsActiviteClass.IdBudget = b.id_budget;
                    budgetsActiviteClass.ActiviteProjetId = b.ActiviteProjetId;
                    budgetsActiviteClass.MontantPrevu = b.montant_prevu;
                    budgetsActiviteClass.MontantRealise = b.montant_realise;
                    budgetsActiviteClass.Observation = b.observation;
                }
            }
        }

        public int Ajouter(BudgetsActivite_Class budgetsActiviteClass)
        {
            using (Connection con = new Connection())
            {
                budget.ActiviteProjetId = budgetsActiviteClass.ActiviteProjetId;
                budget.montant_prevu = budgetsActiviteClass.MontantPrevu;
                budget.montant_realise = budgetsActiviteClass.MontantRealise;
                budget.observation = budgetsActiviteClass.Observation;

                try
                {
                    con.budgets_activite.Add(budget);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerBudgets(GridView gdv, long activiteProjetId)
        {
            using (Connection con = new Connection())
            {
                var query = from b in con.budgets_activite
                            join a in con.ActiviteProjets on b.ActiviteProjetId equals a.ActiviteProjetId
                            where b.ActiviteProjetId == activiteProjetId
                            select new
                            {
                                b.id_budget,
                                Activite = a.Titre,
                                MontantPrevu = b.montant_prevu,
                                MontantRealise = b.montant_realise,
                                Ecart = (b.montant_prevu ?? 0) - (b.montant_realise ?? 0),
                                PourcentageUtilisation = b.montant_prevu.HasValue && b.montant_prevu.Value != 0
                                    ? (b.montant_realise ?? 0) / b.montant_prevu.Value * 100
                                    : 0,
                                Observation = b.observation
                            };

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }

        public void chargerBudget(DropDownList ddw)
        {
            ddw.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.budgets_activite select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    ddw.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner le Budget de l'activité";
                    ddw.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.ActiviteProjetId.ToString();
                        item.Text = data.observation;
                        item.Value=data.ActiviteProjetId.ToString();
                        item.Text = data.montant_realise.ToString();
                        ddw.Items.Add(item);
                    }

                }
                else
                {
                    ddw.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    ddw.Items.Add(item0);
                }

            }
        }


        public int Modifier(BudgetsActivite_Class budgetsActiviteClass)
        {
            using (Connection con = new Connection())
            {
                var b = con.budgets_activite.FirstOrDefault(x => x.id_budget == budgetsActiviteClass.IdBudget);
                if (b != null)
                {
                    b.ActiviteProjetId = budgetsActiviteClass.ActiviteProjetId;
                    b.montant_prevu = budgetsActiviteClass.MontantPrevu;
                    b.montant_realise = budgetsActiviteClass.MontantRealise;
                    b.observation = budgetsActiviteClass.Observation;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(int idBudget)
        {
            using (Connection con = new Connection())
            {
                var b = con.budgets_activite.FirstOrDefault(x => x.id_budget == idBudget);
                if (b != null)
                {
                    con.budgets_activite.Remove(b);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public void chargerCommentairePost(DropDownList lst)
        {
            throw new NotImplementedException();
        }
    }
}