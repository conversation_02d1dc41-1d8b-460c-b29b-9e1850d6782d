﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.Services.Description;
using System.Web.UI;
using System.Web.UI.WebControls;



namespace LinCom.file
{
    public partial class domaineinterventionorganisation : System.Web.UI.Page
    {
        private int info;
        string nscno;
        DomaineInterventionOrganisation_Class domaineInterventionOrganisation = new DomaineInterventionOrganisation_Class();
        DomaineInterventionOrganisation_Class domaineInterventionOrganisationDetails = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objDIO = new DomaineInterventionOrganisationImp();
        ICommonCode co = new CommonCode();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        IDomaineIntervention objDomaineIntervention = new DomaineInterventionImp();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg =new MembresOrganisationImp();

        long ide,idorg;
        static string nameorg;
        static int rolid;

        protected void Page_Load(object sender, EventArgs e)
        {
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];
          
            nscno = Request.QueryString["id"];


            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin
                ide = Convert.ToInt64(idperso.Value);
                rolid = Convert.ToInt32(role.Value);

            }
            else Response.Redirect("~/login.aspx");

            objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
            idorg = Convert.ToInt64(memorg.OrganisationId);
            objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
            nameorg = org.Nom;

            if (!IsPostBack)
            {

                ChargerOrganisations();
                ChargerDomainesIntervention();
                InitialiserMessages();

                if (nscno == null)
                {
                    btn_ajouter.InnerText = "Ajouter";
                }
                else
                {
                    btn_ajouter.InnerText = "Modifier";
                    AfficherDetails();
                   
                }
            }
        }

        private void InitialiserMessages()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
        }

        private void ChargerOrganisations()
        {
            try
            {
                objOrganisation.chargerOrganisation(drpdorganisation);
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors du chargement des organisations: " + ex.Message;
            }
        }

        private void ChargerDomainesIntervention()
        {
            try
            {
                objDomaineIntervention.ChargerDomainedisponible(drpddom);
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors du chargement des domaines d'intervention: " + ex.Message;
            }
        }

        protected void btn_ajouter_ServerClick(object sender, EventArgs e)
        {
            if (nscno == null)
            {
                AjouterDomaineInterventionOrganisation();
            }
            else
            {
                ModifierDomaineInterventionOrganisation();
            }
        }

        private void AjouterDomaineInterventionOrganisation()
        {
            try
            {
                if ( drpddom.SelectedValue == "-1" || drpdstatut.SelectedValue == "-1" )
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }

                domaineInterventionOrganisation.OrganisationId = Convert.ToInt64(idorg);
                domaineInterventionOrganisation.DomaineInterventionId = Convert.ToInt32(drpddom.SelectedValue);
                domaineInterventionOrganisation.statut = drpdstatut.SelectedValue;
                domaineInterventionOrganisation.Description = txtDescription.Value;

                domaineInterventionOrganisation.name = co.GenerateSlug(drpddom.SelectedItem.Text+ nameorg);

                info = objDIO.Ajouter(domaineInterventionOrganisation);

                if (info == 1)
                {
                    Response.Redirect("~/file/listdomaineinterventionorganisation.aspx");
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Ce domaine d'intervention existe déjà pour cette organisation";
                }
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }

        private void ModifierDomaineInterventionOrganisation()
        {
            //try
            //{
                if ( drpddom.SelectedValue == "-1" || drpdstatut.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }

                domaineInterventionOrganisation.OrganisationId = Convert.ToInt64(drpdorganisation.SelectedValue);
                domaineInterventionOrganisation.DomaineInterventionId = Convert.ToInt32(drpddom.SelectedValue);
                domaineInterventionOrganisation.statut = drpdstatut.SelectedValue;
                domaineInterventionOrganisation.Description = txtDescription.Value;
               
                domaineInterventionOrganisation.name = co.GenerateSlug(drpddom.SelectedItem.ToString() + drpdorganisation.SelectedItem.ToString());

                info = objDIO.Modifier(Convert.ToInt32(nscno), domaineInterventionOrganisation);

                if (info == 1)
                {
                    Response.Redirect("~/file/listdomaineinterventionorganisation.aspx");
                }
            else
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de la modification";
            }
            //}
            //catch (Exception ex)
            //{
            //    div_msg_error.Visible = true;
            //    msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            //}
        }

        private void AfficherDetails()
        {
            if (nscno != null)
            {
                objDIO.AfficherDetails(Convert.ToInt32(nscno), domaineInterventionOrganisation, Convert.ToInt64(idorg),0);

                    drpdorganisation.SelectedValue = domaineInterventionOrganisation.OrganisationId.ToString();
                    drpddom.SelectedValue = domaineInterventionOrganisation.DomaineInterventionId.ToString();
                    drpdstatut.SelectedValue = domaineInterventionOrganisation.statut;
                    txtDescription.Value = domaineInterventionOrganisation.Description;
               
            }
        }
    }
}