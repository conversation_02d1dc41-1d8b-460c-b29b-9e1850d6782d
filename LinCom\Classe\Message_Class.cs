using System;

namespace LinCom.Class
{
    public class Message_Class
    {
        public long MessageId { get; set; }
        public long? ConversationId { get; set; }
        public Nullable<long> SenderId { get; set; }
        public string Contenu { get; set; }
        public string AttachmentUrl { get; set; }
        public DateTime? DateEnvoi { get; set; }
        public string name { get; set; }


    }
}
