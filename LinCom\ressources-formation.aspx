﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="ressources-formation.aspx.cs" Inherits="LinCom.ressources_formation" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
                <!-- Page Title -->
<div class="page-title">
    <div class="heading">
        <div class="container">
            <div class="row d-flex justify-content-center text-center">
                <div class="col-lg-8">
                   <h2 class="display-5 fw-bold">Bibliothèque- Formation de FNUAP</h2>
                    <p class="lead">Explorez nos guides, rapports et études qui soutiennent les initiatives des jeunes leaders.</p>
   </div>
            </div>
        </div>
    </div>
    <nav class="breadcrumbs">
        <div class="container">
            <ol>
                <li><a href="home.aspx">Home</a></li>
                <li><a href="#">Espace FNUAP</a></li>
                <li><a href="#">Bibliothèque Digitale de FNUAP</a></li>
                <li class="current"><a href="ressources-formation.aspx"></a></li>
            </ol>
        </div>
    </nav>
</div>
<!-- End Page Title -->
   
     <main class="main">
<!-- Filters -->
<section class="filter-bar py-4 bg-white shadow-sm position-sticky top-0 z-2">
  <div class="container">
    <div class="row justify-content-center gx-3 gy-2 align-items-center">

      <div class="col-md-auto">
        <asp:DropDownList CssClass="form-select rounded-pill px-4" ID="drpdtyperessources" runat="server">
          <asp:ListItem Value="-1">Type de ressources</asp:ListItem>
          <asp:ListItem Value="formation">Livre (Guide) de Formation</asp:ListItem>
          <asp:ListItem Value="document officiel">Document Officiel</asp:ListItem>
          <asp:ListItem Value="rapport">Rapport</asp:ListItem>
        </asp:DropDownList>
      </div>

      <div class="col-md-auto">
        <input type="date" class="form-control rounded-pill px-4" style="min-width: 200px;">
      </div>

      <div class="col-md-auto">
        <button class="btn btn-primary rounded-pill px-4 fw-semibold" runat="server" id="btnsearch" onserverclick="btnsearch_ServerClick">
          Filtrer
        </button>
      </div>

    </div>
  </div>
</section>

<!-- Resources Grid -->
<section class="resources py-5 bg-light">
  <div class="container">
    <div class="row g-4">

                 <!-- Carte avec lien PDF -->
         <asp:ListView ID="listresource" runat="server" OnItemCommand="listresource_ItemCommand">
     <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
     <ItemTemplate>
<div class="col-md-4 col-lg-4">
  <div class="resource-card shadow rounded-4 overflow-hidden bg-white h-100 d-flex flex-column">
    <div class="resource-thumb position-relative">
      <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/ressourc/", Eval("photocouverture"))) %>' class="w-100" style="height: 200px; object-fit: cover;" alt='<%# HttpUtility.HtmlEncode(Eval("Titre")) %>'>
      <span class="badge-type position-absolute top-0 end-0 m-3 bg-primary text-white px-3 py-1 rounded-pill"><%# HttpUtility.HtmlEncode(Eval("typeressources")) %></span>
    </div>
    <div class="p-4 d-flex flex-column flex-grow-1">
      <h5 class="fw-bold mb-2">
           <asp:LinkButton runat="server" CommandName="view" CommandArgument='<%# Eval("name") %>'><%# HttpUtility.HtmlEncode( Eval("Titre")) %></asp:LinkButton>


      </h5>
     
                <!-- Lien de consultation -->
<a href='<%# HttpUtility.HtmlEncode(string.Concat("../file/ressourc/", Eval("Fichier"))) %>' target="_blank" class="btn btn-outline-success mt-auto w-100 rounded-pill fw-semibold d-flex align-items-center justify-content-center gap-2">
  <i class="bi bi-file-earmark-pdf-fill fs-5"></i> Telecharger le PDF
</a>
        
      <!-- Auteur -->
      <div class="d-flex align-items-center mt-4 pt-3 border-top">
        <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/organ/", Eval("Photoong"))) %>' class="rounded-circle me-3" width="48" height="48" alt="Auteur">
        <div>
          <p class="mb-0 fw-semibold"><%# HttpUtility.HtmlEncode(Eval("Auteur")) %></p>
          <small class="text-muted">Publié  <%# GetRelativeDate(Convert.ToDateTime(Eval("DatePublication"))) %></small>
        </div>
      </div>
    </div>
  </div>
</div>
             </ItemTemplate>
</asp:ListView>

    </div>
  </div>
</section>
         
<!-- Pagination -->
<section class="pagination-section py-4 bg-white">
  <div class="container d-flex justify-content-center">
    <nav>
      <ul class="pagination pagination-rounded gap-2 mb-0">
        <li class="page-item"><a class="page-link" href="#"><i class="bi bi-chevron-left"></i></a></li>
        <li class="page-item active"><a class="page-link" href="#">1</a></li>
        <li class="page-item"><a class="page-link" href="#">2</a></li>
        <li class="page-item"><a class="page-link" href="#">3</a></li>
        <li class="page-item disabled"><span class="page-link">...</span></li>
        <li class="page-item"><a class="page-link" href="#">5</a></li>
        <li class="page-item"><a class="page-link" href="#"><i class="bi bi-chevron-right"></i></a></li>
      </ul>
    </nav>
  </div>
</section>

</main>
<!-- Custom Styles -->
<style>
  .page-header {
    background: linear-gradient(135deg, #2e8b57, #1e4d2b);
  }

  .badge-type {
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.03em;
  }

  .resource-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .resource-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  }

  .pagination .page-link {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
  }

  .pagination .active .page-link {
    background-color: #2e8b57;
    border-color: #2e8b57;
    color: #fff;
  }

  .filter-bar {
    z-index: 999;
  }
</style>

</asp:Content>
