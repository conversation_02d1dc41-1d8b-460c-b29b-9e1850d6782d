﻿<%@ Page Title="Projets" Language="C#" ValidateRequest="false" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="projet.aspx.cs" Inherits="LinCom.file.projet" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">

                                    <div class="breadcomb-ctn">
                                        <h2>Projets</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">

                                    <a data-toggle="tooltip" data-placement="left" href="listprojet.aspx" title="Clique sur ce button pour visualiser la liste des Projets" class="btn">Liste des Projets</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcomb area End-->
    <!-- Form Element area Start-->
    <div class="form-element-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">

                        <div class="cmp-tb-hd bcs-hd">
                            <p style="color: red">Remplissez les champs obligatoires avec asterix</p>

                        </div>
                        <div class="alert-list">
                            <asp:Panel ID="div_msg_succes" runat="server" CssClass="alert alert-success alert-dismissible" Visible="false">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                                <asp:Label ID="msg_succes" runat="server" Text="Enregistrement réussi"></asp:Label>
                            </asp:Panel>
                            <asp:Panel ID="div_msg_error" runat="server" CssClass="alert alert-danger alert-dismissible" Visible="false">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                                <asp:Label ID="msg_error" runat="server" Text="Enregistrement échoué"></asp:Label>
                            </asp:Panel>
                        </div>
                        <div class="row">
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-house"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Catégorie du Projet *</label>
                                        <asp:DropDownList class="form-control" ID="drpdcateg" runat="server">
                                            <asp:ListItem Value="-1">Selectionner la Categorie du Projet</asp:ListItem>

                                        </asp:DropDownList>
                                        <a href="categoriepost.aspx">Nouvelle categorie</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-house"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Titre du Projet *</label>
                                        <input type="text" runat="server" id="txttitre" class="form-control" placeholder="Titre du Projets *">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-house"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Auteur du Projet *</label>
                                        <input type="text" runat="server" id="txtauteur" class="form-control" placeholder="Auteur *">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-calendar"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Date de Debut du Projet *</label>
                                        <input type="date" runat="server" id="txtdatedebut" class="form-control" placeholder="Date de Publication *">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-calendar"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Date Fin du Projet *</label>
                                        <input type="date" runat="server" id="txtdatefin" class="form-control" placeholder="Date de Publication *">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-calendar"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Montant du Projet *</label>
                                        <input type="number" runat="server" id="txtmontant" class="form-control" placeholder="Montant du Projet *">
                                    </div>
                                </div>
                            </div>
                             <div class="col-lg-2 col-md-2 col-sm-4 col-xs-12">
     <div class="form-group ic-cmp-int">
         <div class="form-ic-cmp">
             <i class="notika-icon notika-calendar"></i>
         </div>
         <div class="nk-int-st">
             <label>Dévise du Montant du Projet (ex. Frbu, USD, Euro) *</label>
             <input type="text" runat="server" id="txtdevise" class="form-control" placeholder="Dévise du Montant du Projet *">
         </div>
     </div>
 </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Statut du Projet *</label>
                                        <asp:DropDownList class="form-control" ID="drpdstatut" runat="server">

                                            <asp:ListItem Value="-1">Selectionner le statut du Projet</asp:ListItem>
                                            <asp:ListItem Value="en attente">En attente (non finalisé)</asp:ListItem>
                                            <asp:ListItem Value="publié">Publié</asp:ListItem>
                                            <asp:ListItem Value="non publié">Non publié</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Statut du Projet *</label>
                                        <asp:DropDownList class="form-control" ID="drpdetat" runat="server">

                                            <asp:ListItem Value="-1">Selectionner l'etat du Projet</asp:ListItem>
                                            <asp:ListItem Value="en cours">En cours</asp:ListItem>
                                            <asp:ListItem Value="a venir">A venir</asp:ListItem>
                                            <asp:ListItem Value="cloturé">Cloturé</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 ">
                                <h6>Information sur les domaines d'interventions</h6>
                                <div class="col-md-12">
                                    <i>Vous pouvez choisir un ou plusieurs domaines d'intervention en selectionnant dans la liste.</i>
                                    <asp:DropDownList ID="drpddomai" runat="server" data-parsley-trigger="change" autocomplete="off" class="form-control" AutoPostBack="true" OnSelectedIndexChanged="drpddomai_SelectedIndexChanged">
                                        <asp:ListItem Value="-1">Selectionner le domaine d'Intervention</asp:ListItem>
                                    </asp:DropDownList>
                                    <a href="srdikis.aspx" runat="server" id="doma" onserverclick="doma_ServerClick" visible="false">Ajouter d'autres domaines</a>
                                    <a href="srdikis.aspx" runat="server" id="ajoudom" onserverclick="ajoudom_ServerClick" visible="false">Mon domaine n'existe pas, je veux creer</a>
                                    <div id="dom" runat="server" visible="false">
                                        <h6 runat="server" visible="false">Veuillez selectionner le domaine et cliquer sur le button Ajouter un domaine, et ajoutez d'autres domaines</h6>
                                        <button type="submit" visible="false" runat="server" id="btnajourdom" onserverclick="btnajourdom_ServerClick">Ajouter à la un domaine</button>
                                        <button type="submit" runat="server" id="btnannuldom" onserverclick="btnannuldom_ServerClick" class="btn btn-danger notika-gp-danger">Recommencer</button>

                                        <asp:GridView ID="GridView1" runat="server"
                                            ShowHeaderWhenEmpty="True"
                                            AutoGenerateColumns="True"
                                            EmptyDataText="Aucune Donnée Trouvée pour votre Rercherche"
                                            ShowFooter="true" FooterStyle-Font-Bold="true"
                                            EditRowStyle-Font-Bold="true"
                                            EmptyDataRowStyle-Font-Names="century"
                                            EmptyDataRowStyle-Font-Size="X-Large"
                                            EmptyDataRowStyle-HorizontalAlign="Center"
                                            GridLines="None" AllowPaging="True"
                                            CellSpacing="0" Width="100%">
                                            <AlternatingRowStyle BackColor="#DCDCDC" />

                                        </asp:GridView>
                                    </div>

                                    <div runat="server" id="ajourdom" visible="false">
                                        <div class="card">
                                            <h5 class="card-header">Informations sur Les Domaines d'Intervention</h5>

                                            <div class="col-md-12 ">
                                                <label>Nom du Domaine d'activité</label>
                                                <input id="txtnmdom" runat="server" type="text" data-parsley-trigger="change" placeholder="Nom du Domaine d'activité" autocomplete="off" class="form-control" />
                                            </div>
                                            <div class="col-md-12 ">
                                                <div class="col-sm-6 pl-0">
                                                    <p class="text-right">
                                                        <button type="submit" class="btn btn-success notika-gp-success" runat="server" id="btnajoutnouvdom" onserverclick="btnajoutnouvdom_ServerClick">Enregistrer</button>

                                                    </p>
                                                </div>
                                            </div>

                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                        </div>
                        <div class="row">

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-picture"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Photo </label>
                                        <asp:FileUpload ID="fileupd" runat="server" class="form-control" />

                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-picture"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Document(pdf) </label>
                                        <asp:FileUpload ID="fileupdoc" runat="server" class="form-control" />

                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-form"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Description du Projet </label>
                                        <div>

                                            <textarea class="html-editor" runat="server" id="txtdescription" rows="10"></textarea>

                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-form"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Resumé du Projet</label>
                                        <div>

                                            <textarea class="html-editor" runat="server" id="txtresume" rows="10"></textarea>

                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <button type="button" runat="server" id="btnEnregistrer" onserverclick="btnEnregistrer_ServerClick" class="btn btn-success notika-gp-success">Ajouter</button>
                            </div>

                        </div>

                    </div>
                </div>
            </div>

        </div>
    </div>
    <!-- Form Element area End-->
</asp:Content>
