﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http.Controllers;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class envoiemail : System.Web.UI.Page
    {
        private int info;
        string membreId, nsco;
        Membre_Class membreClass = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objMembre = new MembreImp();
        IProvince objProvince = new ProvinceImp();
        ICommune objCommune = new CommuneImp();
        ICommonCode co = new CommonCode();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        IPoste obj = new PosteImp();
        Post_Class pos = new Post_Class();
        Post_Class p = new Post_Class();
        DomainePost_Class actco = new DomainePost_Class();
        IDomainePost objactco = new DomainePostImp();
        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        CategoriePost_Class catpost = new CategoriePost_Class();
        ICategoryPost objcatpost = new CategoriePostImp();

        INotification objnotif = new NotificationImp();
        Notification_Class notif = new Notification_Class();
       

        DataTable dat = new DataTable();
        static string imge, imge1, pdfe, nameorg;
        long ide;
        long idorg;
        static int rolid;
        long index;
       
        protected void Page_Load(object sender, EventArgs e)
        {
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            //if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            //{//admin

            //    long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
            //    rolid = Convert.ToInt32(role.Value);//roleconnecte

            //}
            //else Response.Redirect("~/login.aspx");
            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin

                long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
                rolid = Convert.ToInt32(role.Value);//roleconnecte

            }
            else Response.Redirect("~/login.aspx");

            objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
            idorg = Convert.ToInt64(memorg.OrganisationId);
            objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
            nameorg = org.Nom;
            if (!IsPostBack)
        {
                initial_msg();
            }
        }

        void EnvoieNotification()
        {
            if (drpdtype.SelectedValue == "tous")
            {
                // Après avoir sauvé la nouvelle question dans la base
                objMembre.AfficherDetails(ide, mem);
                long auteur = ide;
                string titreSujet = txttitre.Value;
                string contenu = txtDescription.Value;
                string name = nameorg + " vous a envoyé un message : " + txttitre.Value;

                string statut = "envoyé";
                // string liensujet = $"{HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority)}/events.aspx?name=" + co.GenerateSlug(txttitre.Value);
                string liensujet = "";

                Task.Run(() => objnotif.EnvoyerNotificationAction(auteur, titreSujet, contenu, liensujet, name, statut));
            }
            else if (drpdtype.SelectedValue == "organisation")
            {
                // Après avoir sauvé la nouvelle question dans la base
                objmemorg.AfficherDetails(ide, memorga, 1);
                long auteur = ide;
                string titreSujet = txttitre.Value;
                string contenu = txtDescription.Value;
                string name = nameorg + " vous a envoyé un message : " + txttitre.Value;

                string statut = "envoyé";
                // string liensujet = $"{HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority)}/events.aspx?name=" + co.GenerateSlug(txttitre.Value);
                string liensujet = "";

                Task.Run(() => objnotif.EnvoyerNotificationActionOrganisation(auteur, titreSujet, contenu, liensujet, name, statut));


            }

        }
       
        private void EnvoieEmail()
        {
            if (drpdtype.SelectedValue=="tous")
            {
                string messageenvoye = txtDescription.Value;

                string sujet = txttitre.Value;
                long idenvoyeur = ide;

                Task.Run(() => co.EnvoyerEmailTousMembres(idenvoyeur, txtDescription.Value, sujet, 0));
                //EmailService.EnvoyerEmail(user.Email, "Notification Lincom", "<p>Votre message...</p>", fournisseur);

            }
            else if (drpdtype.SelectedValue == "organisation")
            {
                string messageenvoye = txtDescription.Value;

                string sujet = txttitre.Value;
                long idenvoyeur = idorg;

                Task.Run(() => co.EnvoyerEmailTousMembres(idenvoyeur, txtDescription.Value, sujet, 1));
                //EmailService.EnvoyerEmail(user.Email, "Notification Lincom", "<p>Votre message...</p>", fournisseur);

            }
           
        }
        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;

        }
        void Vider()
        {
            txttitre.Value = "";
            txtDescription.Value = "";

        }
        protected void btn_ajouter_ServerClick(object sender, EventArgs e)
        {
            EnvoieEmail();
            EnvoieNotification();

            div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.Text = "Votre message a été bein envoyé";
            Vider();

        }
    }
}