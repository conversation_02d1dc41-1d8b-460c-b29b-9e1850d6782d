﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public interface ISujetForum
    {
        void AfficherDetails(long forumId, long idmem, string name, string statut, int cd, SujetForum_Class forumClass);
        int Ajouter(SujetForum_Class f);
        void ChargerGridView(GridView gdv, long id, long idmem, string name,string statut, int cd);
        void ChargerListview(ListView gdv, long id, long idmem, string name, string statut, string tri /*= "date" "date", "reponses", "vues"*/, int cd);
        void ChargerSujetsRecents(Repeater rpt, long forumId);
        int CompterParticipants(long forumId);
        int Modifier(SujetForum_Class f, long id, int cd);
        int supprimer(long id);
        int ChangerStatut(long forumId, string nouveauStatut);
        void ChargerStatistiques(Repeater rpt, long forumId);
        void ChargerForumsPopulaires(Repeater rpt, int nombreForums = 5);
        int count(int cd, long idorg, string publie, string code);
    }
}
