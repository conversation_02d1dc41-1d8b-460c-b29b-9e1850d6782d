﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="notification.aspx.cs" Inherits="LinCom.notification" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
           <!-- Titre de la page -->
<div class="page-title">
  <div class="heading">
    <div class="container">
      <div class="row d-flex justify-content-center text-center">
        <div class="col-lg-8">
           <h2 class="mb-4"><i class="bi bi-bell-fill"></i> Mes Notifications</h2>
         </div>
      </div>
    </div>
  </div>
  <nav class="breadcrumbs">
    <div class="container">
      <ol>
        <li><a href="home.aspx">Home</a></li>
        <li class="current">Forum</li>
      </ol>
    </div>
  </nav>
</div>
   <main class="main container mt-4">
   
     <div class="notif-toolbar sticky-top bg-white py-2 z-3 border-bottom">
    <div class="d-flex justify-content-between align-items-center mb-2 container">
        <div>
            <label for="ddlFiltreNotif" class="form-label me-2 fw-semibold">Filtrer :</label>
            <asp:DropDownList ID="ddlFiltreNotif" runat="server" CssClass="form-select form-select-sm d-inline-block w-auto" AutoPostBack="true" OnSelectedIndexChanged="ddlFiltreNotif_SelectedIndexChanged">
                <asp:ListItem Text="Toutes les notifications" Value="toutes" />
                <asp:ListItem Text="Non lues uniquement" Value="nonlues" />
            </asp:DropDownList>
        </div>
        <button runat="server" id="btnToutLu" class="btn btn-sm btn-secondary" onserverclick="btnToutLu_ServerClick">Tout marquer comme lu</button>
    </div>
</div>

     
    <asp:Repeater ID="rptNotifications" runat="server" OnItemCommand="rptNotifications_ItemCommand">
        <ItemTemplate>
            <div class='notif-card shadow-sm <%# Convert.ToBoolean(Eval("Lu")) ? "" : "notif-unread" %>'>
                <!-- Avatar ou icône -->
                <div class="notif-avatar">
                    <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/",Eval("Photomembre"))) %>' alt="Avatar" class="rounded-circle" />
                  
                </div>

                <!-- Contenu -->
                <div class="notif-content">
                    <div class="notif-header">
                        <div class="fw-bold text-primary">
                            <%# Eval("name") %>  : <span class="text-dark"><%# Eval("Titre") %></span>
                        </div>
                        <span class="notif-time"><i class="bi bi-clock"></i> <%# Eval("DateNotification") %></span>
                    </div>

                    <div class="notif-body text-muted">
                        <%# Eval("Message") %>
                    </div>

                    <div class="notif-link mt-2">
                         <div class="notif-link">
                
            </div>

                        <asp:LinkButton ID="LinkButton1"  CommandArgument='<%# Eval("id") %>'  CommandName="Voir" class="btn btn-sm btn-outline-success"   runat="server">Lire</asp:LinkButton>
                       
                    </div>
                </div>
            </div>
        </ItemTemplate>
    </asp:Repeater>

</main>

<style>
   .notif-card {
    display: flex;
    gap: 15px;
    background-color: #fff;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-left: 5px solid #0d6efd;
    border-radius: 10px;
    margin-bottom: 15px;
    transition: background-color 0.3s ease;
    align-items: flex-start;
}

.notif-unread {
    background-color: #e9f7f3;
    border-left-color: #198754;
}

.notif-avatar img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 50%;
}

.notif-content {
    flex: 1;
}

.notif-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.95rem;
    margin-bottom: 6px;
}

.notif-time {
    font-size: 0.85rem;
    color: #6c757d;
}

.notif-body {
    font-size: 0.95rem;
}

.notif-link a {
    font-size: 0.85rem;
    text-decoration: none;
}

@media (max-width: 576px) {
    .notif-card {
        flex-direction: column;
        align-items: flex-start;
    }

    .notif-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .notif-avatar img {
        width: 40px;
        height: 40px;
    }
}
.sticky-top {
    position: sticky;
    top: 0;
    z-index: 1030; /* assure que ça reste au-dessus */
}
.sticky-top {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

</style>

</asp:Content>
