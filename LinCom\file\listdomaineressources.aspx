﻿<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="listdomaineressources.aspx.cs" Inherits="LinCom.file.listdomaineressources" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Breadcomb area Start-->
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">
                                    <div class="breadcomb-icon">
                                        <i class="notika-icon notika-form"></i>
                                    </div>
                                    <div class="breadcomb-ctn">
                                        <h2>Liste des Domaines des Ressources</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">

                                    <a data-toggle="tooltip" data-placement="left" href="domaineressources.aspx" title="Clique sur ce button pour creer un nouveau domaine des ressources" class="btn">Nouveau domaine des ressources</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcomb area End-->
    <!-- Data Table area Start-->
    <div class="normal-table-area">
        <div class="container">
            <div class="row">

                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="data-table-list">

                        <div class="table-responsive">
                            <asp:GridView ID="gdv" class="table table-striped" runat="server" CssClass="datatbemp"
                                AutoGenerateColumns="False" EmptyDataText="Aucune Donnée Trouvée pour votre Rercherche"
                                GridLines="None" Width="100%" OnRowCommand="gdv_RowCommand">
                                <AlternatingRowStyle BackColor="#DCDCDC" />

                                <Columns>
                                    <asp:TemplateField HeaderText="Titre" FooterText="Titre">
                                        <ItemTemplate>
                                            <asp:Label ID="lb_pvcb" runat="server" Text='<%# Eval("PostTitre") %>'></asp:Label>
                                        </ItemTemplate>
                                    </asp:TemplateField>



                                    <asp:TemplateField HeaderText="Domaine d'Intervention" FooterText="Domaine d'Intervention">
                                        <ItemTemplate>
                                            <asp:Label ID="lb_pvc" runat="server" Text='<%# Eval("PostDom") %>'></asp:Label>
                                        </ItemTemplate>
                                    </asp:TemplateField>

                                    <asp:TemplateField HeaderText="Catégorie de la Ressource" FooterText="Catégorie du Ressource">
                                        <ItemTemplate>
                                            <asp:Label ID="lb_pvch" runat="server" Text='<%# Eval("PostCateg") %>'></asp:Label>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="Action" FooterText="Action">
                                        <ItemTemplate>
                                            <asp:Button ID="btnEdit" BackColor="Green" class="btn btn-info btn-fix" CommandName="view" CommandArgument='<%# Eval("id") %>' runat="server" Text="Edit" />
                                            <asp:Button class="btn btn-danger notika-btn-danger" BackColor="Red" ID="btn_del" runat="server" Text="Delete" CommandName="delete" CommandArgument='<%# Eval("id") %>' OnClientClick=" return confirm('Voulez-Vous vraiment supprimer??')" />
                                        </ItemTemplate>

                                    </asp:TemplateField>
                                </Columns>

                                <EditRowStyle Font-Bold="True"></EditRowStyle>

                                <EmptyDataRowStyle HorizontalAlign="Center" Font-Names="century" Font-Size="X-Large"></EmptyDataRowStyle>

                            </asp:GridView>
                            <!-- end basic table  -->

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Data Table area End-->

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.js"></script>
    <script>
        $(document).ready(function () {
            $(".datatbemp").prepend($("<thead></thead>").append($(this).find("tr:first"))).DataTable();
        });
    </script>

</asp:Content>
