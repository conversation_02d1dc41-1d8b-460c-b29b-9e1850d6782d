@echo off
echo ========================================
echo    LinCom - Script de Build et Test
echo ========================================
echo.

REM Vérifier si MSBuild est disponible
where msbuild >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERREUR: MSBuild n'est pas trouvé dans le PATH
    echo Veuillez installer Visual Studio ou Build Tools
    pause
    exit /b 1
)

echo [1/5] Nettoyage des fichiers de build précédents...
if exist "LinCom\bin" rmdir /s /q "LinCom\bin"
if exist "LinCom\obj" rmdir /s /q "LinCom\obj"
echo ✓ Nettoyage terminé

echo.
echo [2/5] Restauration des packages NuGet...
nuget restore LinCom.sln
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Erreur lors de la restauration des packages
    pause
    exit /b 1
)
echo ✓ Packages restaurés

echo.
echo [3/5] Compilation du projet en mode Debug...
msbuild LinCom.sln /p:Configuration=Debug /p:Platform="Any CPU" /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Erreur de compilation
    pause
    exit /b 1
)
echo ✓ Compilation Debug réussie

echo.
echo [4/5] Compilation du projet en mode Release...
msbuild LinCom.sln /p:Configuration=Release /p:Platform="Any CPU" /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Erreur de compilation Release
    pause
    exit /b 1
)
echo ✓ Compilation Release réussie

echo.
echo [5/5] Exécution des tests unitaires...
REM Recherche de VSTest
where vstest.console.exe >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  VSTest non trouvé, tests ignorés
    goto :skip_tests
)

vstest.console.exe "LinCom\bin\Debug\LinCom.dll" /Logger:console
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  Certains tests ont échoué
) else (
    echo ✓ Tous les tests sont passés
)

:skip_tests
echo.
echo ========================================
echo        BUILD TERMINÉ AVEC SUCCÈS! 
echo ========================================
echo.
echo Fichiers générés:
echo - LinCom\bin\Debug\LinCom.dll
echo - LinCom\bin\Release\LinCom.dll
echo.
echo Pour déployer l'application:
echo 1. Copiez le contenu du dossier LinCom vers votre serveur IIS
echo 2. Configurez la chaîne de connexion dans Web.config
echo 3. Exécutez le script SQL de restauration: restor.sql
echo.
pause
