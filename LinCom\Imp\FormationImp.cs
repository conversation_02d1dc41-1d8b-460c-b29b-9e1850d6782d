﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.EnterpriseServices;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class FormationImp : IFormation
    {
         Formation formation = new Formation();
         Formation f = new Formation();
        int msg;

        public void AfficherDetails(long formationId,long idorg, Formation_Class formationClass)
        {
            using (var context = new Connection())
            {
                var f = context.Formations.FirstOrDefault(x => x.FormationId == formationId && x.OrganisationId==idorg);
                if (f != null)
                {
                    formationClass.FormationId = f.FormationId;
                    formationClass.Titre = f.Titre;
                    formationClass.Description = f.Description;
                    formationClass.DateFormation = f.DateFormation;
                    formationClass.name = f.name;
                    formationClass.DateCreation = f.DateCreation;
                    formationClass.statut = f.statut;
                    formationClass.DatePublication = f.DatePublication;
                    formationClass.MembreId = f.MembreId;
                    formationClass.OrganisationId = f.OrganisationId;
                    formationClass.MOIS = f.MOIS;
                    formationClass.ANNEE = f.ANNEE;
                    

    }
            }
        }
        public void AfficherDetails(string name,long idorg, Formation_Class formationClass)
        {
            using (var context = new Connection())
            {
                var f = context.Formations.FirstOrDefault(x => x.name == name && x.OrganisationId==idorg);
                if (f != null)
                {
                    formationClass.FormationId = f.FormationId;
                    formationClass.Titre = f.Titre;
                    formationClass.Description = f.Description;
                    formationClass.DateFormation = f.DateFormation;
                    formationClass.name = f.name;
                    formationClass.DateCreation = f.DateCreation;
                    formationClass.statut = f.statut;
                    formationClass.DatePublication = f.DatePublication;
                    formationClass.MembreId = f.MembreId;
                    formationClass.OrganisationId = f.OrganisationId;
                    formationClass.MOIS = f.MOIS;
                    formationClass.ANNEE = f.ANNEE;

                }
            }
        }

        public int Ajouter(Formation_Class formationClass)
        {
            using (Connection con = new Connection())
            {

                f.Titre=formationClass.Titre ;
                f.Description=formationClass.Description;
                f.DateFormation=formationClass.DateFormation;
                f.name=formationClass.name;
                f.DateCreation=formationClass.DateCreation;
                f.statut=formationClass.statut;
                f.DatePublication=formationClass.DatePublication;
                f.MembreId=formationClass.MembreId;

                f.OrganisationId = formationClass.OrganisationId;
                f.MOIS = formationClass.MOIS;
                f.ANNEE = formationClass.ANNEE;

                try
                {
                    con.Formations.Add(f);

                    if (con.SaveChanges() == 1)
                    {
                        con.Formations.Add(f);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerGridView(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var query = from f in con.Formations
                            select new
                            {
                                f.FormationId,
                                f.Titre,
                                f.Description,
                                f.DateFormation,
                                f.name
                            };

                gdv.DataSource = query.OrderByDescending(x => x.DateFormation).ToList();
                gdv.DataBind();
            }
        }

        public int Modifier(Formation_Class formationClass,long id, long idorg)
        {
            using (Connection con = new Connection())
            {
                var f = con.Formations.FirstOrDefault(x => x.FormationId == id && x.OrganisationId==idorg);
                if (f != null)
                {
                    f.Titre = formationClass.Titre;
                    f.Description = formationClass.Description;
                    f.DateFormation = formationClass.DateFormation;
                    f.name = formationClass.name;
                    f.DateCreation = formationClass.DateCreation;
                    f.statut = formationClass.statut;
                    f.DatePublication = formationClass.DatePublication;
                    f.MembreId = formationClass.MembreId;

                    f.OrganisationId = formationClass.OrganisationId;
                    f.MOIS = formationClass.MOIS;
                    f.ANNEE = formationClass.ANNEE;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            con.Formations.Add(f);
                            con.Entry(f).State = EntityState.Modified;
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public int Supprimer(long formationId)
        {
            using (Connection con = new Connection())
            {
                var f = con.Formations.FirstOrDefault(x => x.FormationId == formationId);
                if (f != null)
                {
                    con.Formations.Remove(f);
                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }
    }
}