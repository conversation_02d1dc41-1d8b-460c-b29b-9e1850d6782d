﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class partenaire : System.Web.UI.Page
    {
        private int info;
        string membreId, nsco;
        Membre_Class membreClass = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objMembre = new MembreImp();
        
        ICommonCode co = new CommonCode();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        IPartenaire obj = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();
        Partenaire_Class pa = new Partenaire_Class();
       

        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;

        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];
            if (!IsPostBack)
            {

                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
                    rolid = Convert.ToInt32(role.Value);//roleconnecte

                }
                else Response.Redirect("~/login.aspx");

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;

                initial_msg();
               
                if (nsco == null)
                {
                    btnEnregistrer.InnerText = "Enregistrer";

                }
                else

                {
                    btnEnregistrer.InnerText = "Modifier";
                    AfficherArticle();
                }

            }
        }
  

        public void ViderChamps()
        {
            // Réinitialiser les champs texte
            txtnm.Value = string.Empty;
            txttel.Value = string.Empty;
            txtemail.Value = string.Empty;
            txtlien.Value = string.Empty;
            txttype.Value = string.Empty;
            txtresume.Value = string.Empty;

            drpdstatut.SelectedValue = "-1";

            // Réinitialiser les FileUpload (pas possible directement, nécessiterait un rechargement de la page)
            // fileupd et fileupd1 : rechargement automatique du formulaire requis pour les vider.
            // fileupdoc : même chose.

            // Cacher les messages

        }


        private string UploadImage()
        {
            if (fileupd.HasFile)
            {
                string fileName = Path.GetFileName(fileupd.FileName);
                string extension = Path.GetExtension(fileName).ToLower();

                string[] validExtensions = { ".png", ".jpeg", ".gif", ".jpg", ".jfif" };

                if (!validExtensions.Contains(extension))
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "La photo doit avoir ces extensions : .png, .jpg, .gif, .jpeg, .jfif. ";

                    return null;
                }

                if (fileupd.PostedFile.ContentLength > 104857600)
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Votre photo est trop volumineuse. Elle doit faire moins de 100MB.";

                    return null;
                }

                string path = Server.MapPath("~/file/partner/") + fileName;
                fileupd.SaveAs(path);
                imge = fileName;

            }
            else
            {
                if (nsco == null)
                {
                    div_msg_succes.Visible = false;
                    div_msg_error.Visible = true;
                    msg_error.Text = "Veuillez vous assurez que le logo est chargé";
                   
                }
                else
                {

                    obj.afficherDetails(Convert.ToInt32(nsco),part);
                    imge = part.logo;

                }
            }
            return imge;

        }

        public void AjoutPost()
        {
            try
            { // Vérification des champs requis
                if (string.IsNullOrWhiteSpace(txtnm.Value) ||
                    drpdstatut.SelectedValue == "-1")
                {
                    div_msg_succes.Visible = false;
                    div_msg_error.Visible = true;
                    msg_error.Text = "Veuillez renseigner tous les champs obligatoires.";
                    return;
                }

                pa.Nom = txtnm.Value;
                pa.Contact = txttel.Value;
                pa.logo = UploadImage();
                pa.statut = drpdstatut.SelectedValue;
                pa.etat = txttype.Value;
                pa.lienwebsite = txtlien.Value;
                pa.Email = txtemail.Value;

               
                info = obj.Ajout(pa);
                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    div_msg_error.Visible = false;
                    msg_succes.Text = "Partenaire enregistré avec succès.";

                    ViderChamps();

                }
                else
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.Text = "Ce partenaire Existe deja";

                }


            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.Text = "Ce partenaire Existe deja";

            }

            //LabelMsg.Text = obj.ajouterProvince(prov).ToString();
        }

        public void upte()
        {
            try
            { // Vérification des champs requis
                 if (string.IsNullOrWhiteSpace(txtnm.Value) ||
                    drpdstatut.SelectedValue == "-1")
                {
                    div_msg_succes.Visible = false;
                    div_msg_error.Visible = true;
                    msg_error.Text = "Veuillez renseigner tous les champs obligatoires.";
                    return;
                }
                pa.Nom = txtnm.Value;
                pa.Contact = txttel.Value;
                pa.logo = UploadImage();
                pa.statut = drpdstatut.SelectedValue;
                pa.etat = txttype.Value;
                pa.lienwebsite = txtlien.Value;
                pa.Email = txtemail.Value;

                
                info = obj.edit(pa, Convert.ToInt32(nsco));
                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    div_msg_error.Visible = false;
                    msg_succes.Text = "Partenaire modifié avec succès.";

                    ViderChamps();

                }
            else
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.Text = "Ce Partenaire Existe deja";

            }


        }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.Text = "Ce Post Existe deja";

            }

}
        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;

        }
        public void AfficherArticle()
        {
            if (nsco != null)
            {
                obj.afficherDetails(Convert.ToInt32(nsco), pa);


                txtnm.Value = pa.Nom;
                txttel.Value= pa.Contact;
               
                drpdstatut.SelectedValue= pa.statut;
                txttype.Value= pa.etat;
                txtlien.Value= pa.lienwebsite;
                txtemail.Value= pa.Email;

                // Définir les valeurs des DropDownList si présentes
                if (drpdstatut.Items.FindByValue(pa.statut) != null)
                    drpdstatut.SelectedValue = pa.statut;


            }
        }

        protected void btnEnregistrer_ServerClick(object sender, EventArgs e)
        {
            // Vérifier la validation côté serveur
            if (!Page.IsValid)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Veuillez corriger les erreurs avant de continuer.";
                return;
            }

            if (nsco == null)
            {
                AjoutPost();
            }
            else
            {
                upte();
            }
        }
        protected void btn_enreg_Click(object sender, EventArgs e)
        {
            if (nsco == null)
            {
                AjoutPost();

            }
            else
                upte();

        }
    }
}