﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class FinancementImp : IFinancement
    {
        private Financement financement = new Financement();
        int msg;

        public void AfficherDetails(long financementId,long idorg,long idpos,string name, int cd, Financement_Class financementClass)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var f = con.Financements.FirstOrDefault(x => x.FinancementId == financementId);
                    if (f != null)
                    {
                        financementClass.FinancementId = f.FinancementId;
                        financementClass.PostId = f.PostId;
                        financementClass.Montant = f.Montant;
                        financementClass.Source = f.Source;
                        financementClass.Intitulefinancement = f.Intitulefinancement;
                        financementClass.PartenaireId = f.PartenaireId;
                        financementClass.DateFinancement = f.DateFinancement;
                        financementClass.Dateenreg = f.Dateenreg;
                        financementClass.statut = f.statut;
                        financementClass.MembreId = f.MembreId;
                        financementClass.MOIS = f.MOIS;
                        financementClass.ANNEE = f.ANNEE;
                        financementClass.OrganisationId = f.OrganisationId;
                        financementClass.name = f.name;
                        financementClass.etat = f.etat;
                        financementClass.Devise = f.Devise;
                        financementClass.Description = f.Description;



                    }

                }
             
            }
        }

        public int Ajouter(Financement_Class f)
        {
            using (Connection con = new Connection())
            {
                financement.PostId = f.PostId;
                financement.Montant = f.Montant;
                financement.Source = f.Source;
                financement.Intitulefinancement = f.Intitulefinancement;
                financement.PartenaireId = f.PartenaireId;
                financement.DateFinancement = f.DateFinancement;
                financement.Dateenreg = f.Dateenreg;
                financement.statut = f.statut;
                financement.MembreId = f.MembreId;
                financement.MOIS = f.MOIS;
                financement.ANNEE = f.ANNEE;
                financement.OrganisationId = f.OrganisationId;
                financement.name = f.name;
                financement.etat = f.etat;
                financement.Devise = f.Devise;
                financement.Description = f.Description;

                try
                {
                    con.Financements.Add(financement);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }

        public void ChargerFinancements(GridView gdv, long id,long idorg,string name, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var query = from f in con.Financements
                                join po in con.Posts on f.PostId equals po.PostId
                                join ca in con.CategoriePosts on po.CategoriePostId equals ca.CategoriePostId
                                join o in con.Organisations on f.OrganisationId equals o.OrganisationId
                                join m in con.Membres on f.MembreId equals m.MembreId
                                where f.OrganisationId==idorg
                                select new
                                {
                                    id = f.FinancementId,
                                    Montant = f.Montant,
                                    Source = f.Source,
                                    Titre = f.Intitulefinancement,
                                    PartenaireId = f.PartenaireId,
                                    DateFinancement = f.DateFinancement,
                                    Dateenreg = f.Dateenreg,
                                    statut = f.statut,
                                    MembreId = f.MembreId,
                                    MOIS = f.MOIS,
                                    ANNEE = f.ANNEE,
                                    OrganisationId = f.OrganisationId,
                                    name = f.name,
                                    etat = f.etat,
                                    Titreprojet=po.Titre,
                                    Devise = f.Devise,
                                    Description = f.Description
                }
                ;

                    gdv.DataSource = query.OrderByDescending(x => x.DateFinancement).ToList();
                    gdv.DataBind();
                }
                
                  
            }
        }

        public int Modifier(Financement_Class f, long id, long idorg, string name, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    financement = con.Financements.FirstOrDefault(x => x.FinancementId == id && x.OrganisationId == idorg);
                    if (f != null)
                    {

                        financement.PostId = f.PostId;
                        financement.Montant = f.Montant;
                        financement.Source = f.Source;
                        financement.PartenaireId = f.PartenaireId;
                        financement.Intitulefinancement = f.Intitulefinancement;
                        financement.DateFinancement = f.DateFinancement;
                        financement.statut = f.statut;
                        financement.MOIS = f.MOIS;
                        financement.ANNEE = f.ANNEE;
                        financement.name = f.name;
                        financement.etat = f.etat;
                        financement.Devise = f.Devise;
                        financement.Description = f.Description;

                        try
                        {


                            if (con.SaveChanges() == 1)
                            {
                                con.Financements.Add(financement);
                                con.Entry(financement).State = EntityState.Modified;
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return msg = 0;
                        }
                    }
                  
                }

                return msg;
            }
        }

        public int Supprimer(long financementId,long idorg)
        {
            using (Connection con = new Connection())
            {
                var f = con.Financements.FirstOrDefault(x => x.FinancementId == financementId && x.OrganisationId==idorg);
                if (f != null)
                {
                    con.Financements.Remove(f);
                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }
    
}
}