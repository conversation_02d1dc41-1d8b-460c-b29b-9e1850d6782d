﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class typeorganisation : System.Web.UI.Page
    {
        private int info;
        string nscno;
        TypeOrganisation_Class prov = new TypeOrganisation_Class();
        TypeOrganisation_Class pro = new TypeOrganisation_Class();
        ITypeOrganisation obj = new TypeOrganisationImp();
        ICommonCode co = new CommonCode();

        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        static string typenm; static int id, idpers, rol;
        static string imge, imge1, pdfe, nameorg;
        long ide; long idorg;
        static int rolid;
        long index;

        protected void Page_Load(object sender, EventArgs e)
        {
            nscno = Request.QueryString["name"];
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {
                ide = Convert.ToInt64(idperso.Value);
                rolid = Convert.ToInt32(role.Value);
            }
            else
            {
                Response.Redirect("~/login.aspx");
            }

            if (!IsPostBack)
            {

                initial_msg();
                if (nscno == null)
                {
                    btn_enreg.InnerText = "Enregistrer";
                    // Response.Redirect("~/sima/typeorganisation.aspx/");
                }
                else

                {
                    btn_enreg.InnerText = "Modifier";
                    afficher();
                }

            }
        }
        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;

        }
        public string Generatename(string title)
        {
            return title.ToLower()
                        .Replace("-", "")
                        .Replace(",", "")
                        .Replace("'", "")
                        .Replace("?", "")
                        .Replace("!", "")
                        .Replace(".", "")
                        .Replace(":", "")
                        .Replace(" ", "");
        }
        public void adPrv()
        {
            try
            {
                if (txtnm.Value == "")
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Renseignez tous les champs";
                }
                else
                {
                    prov.name = co.GenerateSlug(txtnm.Value);
                    prov.Libelle = txtnm.Value;
                    info = obj.Ajouter(prov);
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listtypeorganisation.aspx");

                    }
                    else
                    {
                        div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Ce type Existe deja";

                    }


                }

            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "ce type Existe deja";

            }

            //LabelMsg.InnerText = obj.ajouterProvince(prov).ToString();
        }

        public void upte()
        {
            try
            {
                if (txtnm.Value == "")
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Renseignez tous les champs";
                }
                else
                {
                    prov.name = co.GenerateSlug(txtnm.Value);
                    prov.Libelle = txtnm.Value;
                    obj.AfficherDetails(nscno, pro);
                    info = obj.Modifier(Convert.ToInt32(pro.TypeOrganisationId),prov);
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listtypeorganisation.aspx");
                    }
                    else
                    {
                        Response.Redirect("~/file/listtypeorganisation.aspx");

                    }
                }

            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "ce type Existe deja";

            }
        }

        protected void btn_enreg_ServerClick(object sender, EventArgs e)
        {
            if (nscno == null)
            {
                adPrv();

            }
            else
                upte();
        }


        protected void afficher()
        {
            if (nscno != null)
            {
                obj.AfficherDetails(nscno, prov);
                txtnm.Value = prov.Libelle;

            }
        }


    }
}