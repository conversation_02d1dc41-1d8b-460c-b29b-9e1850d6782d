﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="PageMaster.master.cs" Inherits="LinCom.PageMaster" %>

<!DOCTYPE html>

<html>
<head runat="server">
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>LinCom</title>
    <meta name="description" content="">
    <meta name="keywords" content="">

    <!-- Favicons -->
    <link href="assets/img/skills.png" rel="icon">
    <link href="assets/img/skills.png" rel="apple-touch-icon">
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com" rel="preconnect">
    <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

    <!-- Vendor CSS Files -->
    <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/vendor/aos/aos.css" rel="stylesheet">
    <link href="assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
    <link href="assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">

    <!-- Main CSS File -->
    <link href="assets/css/main.css" rel="stylesheet">
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"></script>




    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body>
    <header id="header" class="header fixed-top">

        <div class="topbar d-flex align-items-center">
            <div class="container d-flex justify-content-center justify-content-md-between">
                <div class="contact-info d-flex align-items-center">
                    <i class="bi bi-envelope d-flex align-items-center"><a href="mailto:<EMAIL>"><EMAIL></a></i>
                    <i class="bi bi-phone d-flex align-items-center ms-4"><span>+257 68 18 40 11</span></i>
                </div>
                <div class="social-links d-none d-md-flex align-items-center">
                    <a href="#" class="twitter"><i class="bi bi-twitter-x"></i></a>
                    <a href="#" class="facebook"><i class="bi bi-facebook"></i></a>
                    <a href="#" class="instagram"><i class="bi bi-instagram"></i></a>
                    <a href="#" class="linkedin"><i class="bi bi-linkedin"></i></a>
                </div>

            </div>
        </div>
        <!-- End Top Bar -->

        <div class="branding d-flex align-items-cente">

            <div class="container position-relative d-flex align-items-center justify-content-between">
                <a href="index.html" class="logo d-flex align-items-center">
                    <!-- Uncomment the line below if you also wish to use an image logo -->
                    <!-- <img src="assets/img/logo.png" alt=""> -->
                    <h1 class="sitename">Linked Community Burundi</h1>
                    <span>.</span>
                </a>

                <nav id="navmenu" class="navmenu">
                    <ul>
                        <li><a href="home.aspx" runat="server" class="active">Home</a></li>

                        <li class="dropdown">
                            <a href="#"><span>Organisations</span> <i class="bi bi-chevron-down toggle-dropdown"></i></a>
                            <ul>
                                <li><a href="ong.aspx">Organisations</a></li>
                                <li><a href="blogong.aspx">Activités des Organisations</a></li>

                                <% if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                                    { %>

                                <% }
                                    else
                                    { %>

                                <li><a href="inscriptionmembre.aspx">Devenir Membre</a></li>

                                <% } %>
                                <li><a href="inscriptionorganisation.aspx">Inscire une Organisation</a></li>

                            </ul>
                        </li>

                        <li class="dropdown"><a href="#"><span>Espace FNUAP</span> <i class="bi bi-chevron-down toggle-dropdown"></i></a>
                            <ul>
                                <li><a href="programme.aspx">Programmes de FNUAP</a></li>
                                <li class="dropdown"><a href="#"><span>Bibliothèque Digitale</span> <i class="bi bi-chevron-down toggle-dropdown"></i></a>
                                    <ul>
                                        <li><a href="ressources-formation.aspx">Formation</a></li>
                                        <li><a href="ressources-document.aspx">Document officiel</a></li>
                                        <li><a href="ressources-rapport.aspx">Rapport</a></li>
                                    </ul>
                                </li>
                                <li><a href="evenement.aspx">Evénements de FNUAP</a></li>
                                <li><a href="galeriemedia.aspx">Galerie Multimédia</a></li>
                            </ul>
                        </li>


                        <li class="dropdown">
                            <a href="#"><span>Bibliothèque Digitale</span> <i class="bi bi-chevron-down toggle-dropdown"></i></a>
                            <ul>
                                <li><a href="ressources.aspx">Bibliothèque Digitale</a></li>
                                <li><a href="evenement-ong.aspx">Evénements des ONG locales</a></li>
                                <%--  <li><a href="#">Modules d'Apprentissage</a></li>
                                <li><a href="#">Outils & Materiaux</a></li>--%>
                                <li><a href="programmementorat.aspx">Programmes de Mentorats</a></li>
                            </ul>
                        </li>

                        <li><a href="espaceforum.aspx">Forum</a></li>
                        <li><a href="about.aspx">A Propos Nous</a></li>

                        <!-- Détecte si l'utilisateur est connecté -->
                        <!-- Supposons que vous ayez une variable serveur comme IsUserLoggedIn -->

                        <% if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                            { %>
                        <li class="dropdown">
                            <a href="#"><i class="bi bi-person-circle" style="font-size: 1.5rem;"></i></a>
                            <ul>
                                <li><a href="profil.aspx">Voir Profil</a></li>
                                <li><a href="#" runat="server" id="btndeconnect" onserverclick="btndeconnect_ServerClick">Déconnexion</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
    <a class="nav-link position-relative" href="notification.aspx">
        <i class="bi bi-bell-fill"></i>
        (<asp:Literal ID="litNombreNonLues" runat="server">0</asp:Literal>)
    </a>
</li>

                       <%-- <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">🔔 <span class="badge bg-danger"><%# Eval("NombreNonLues") %></span>
                            </a>
                            <ul class="dropdown-menu">
                                <asp:Repeater ID="rptNotifications" runat="server">
                                    <ItemTemplate>
                                        <li>
                                            <a class="dropdown-item" href='<%# Eval("UrlRedirection") %>'>
                                                <strong><%# Eval("Titre") %></strong><br />
                                                <small><%# Eval("Message") %></small>
                                                <small>date :  <%# GetRelativeDate(Convert.ToDateTime(Eval("DateNotification"))) %></small>
                                            </a>
                                        </li>
                                    </ItemTemplate>
                                </asp:Repeater>
                            </ul>
                        </li>--%>

                        <% }
                            else
                            { %>
                        <li>
                            <a href="login.aspx" class="btn-get-started">Connexion</a>
                        </li>
                        <% } %>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                Langue
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="ChangerLangue.aspx?lang=fr">Français</a></li>
                                <li><a class="dropdown-item" href="ChangerLangue.aspx?lang=en">English</a></li>
                                <li><a class="dropdown-item" href="ChangerLangue.aspx?lang=ki">Kirundi</a></li>
                                <li><a class="dropdown-item" href="ChangerLangue.aspx?lang=sw">Kiswahili</a></li>
                            </ul>
                        </div>

                    </ul>

                    <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
                </nav>

            </div>

        </div>

    </header>
    <main class="main">

        <form id="form1" runat="server">
            <div>
                <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                </asp:ContentPlaceHolder>
            </div>
        </form>
    </main>
    <footer id="footer" class="footer accent-background">

        <div class="container footer-top">
            <div class="row gy-4">
                <div class="col-lg-5 col-md-12 footer-about">
                    <a href="home.aspx" class="logo d-flex align-items-center">
                        <span class="sitename">Linked Community Burundi</span>
                    </a>
                    <p>Cras fermentum odio eu feugiat lide par naso tierra. Justo eget nada terra videa magna derita valies darta donna mare fermentum iaculis eu non diam phasellus.</p>
                    <div class="social-links d-flex mt-4">
                        <a href=""><i class="bi bi-twitter-x"></i></a>
                        <a href=""><i class="bi bi-facebook"></i></a>
                        <a href=""><i class="bi bi-instagram"></i></a>
                        <a href=""><i class="bi bi-linkedin"></i></a>
                    </div>
                </div>

                <div class="col-lg-2 col-6 footer-links">
                    <h4>Useful Links</h4>
                    <ul>
                        <li><a href="home.aspx">Accueil</a></li>
                        <li><a href="about.aspx">A Propos de LinCom</a></li>
                        <li><a href="#">Politique de Confidentialité</a></li>
                        <li><a href="#">Condition d'Utilisation</a></li>
                    </ul>
                </div>


                <div class="col-lg-3 col-md-12 footer-contact text-center text-md-start">
                    <h4>Retrouvez-Nous sur </h4>
                    <p>no.7, Kabondo, </p>
                    <p>Avenue Lac RWERU</p>
                    <p>Bujumbura, Burundi</p>
                    <p class="mt-4"><strong>Phone:</strong> <span>+257 68 18 40 11</span></p>
                    <p><strong>Email:</strong> <span><EMAIL></span></p>
                </div>

            </div>
        </div>

        <div class="container copyright text-center mt-4">
            <p>© <span>Copyright</span> <strong class="px-1 sitename">Linked Community</strong> <span>All Rights Reserved</span></p>
            <div class="credits">
                Conçu par <a href="">KIT Hub</a>
            </div>
        </div>

    </footer>

    <!-- Scroll Top -->
    <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>


    <!-- Vendor JS Files -->
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="assets/vendor/php-email-form/validate.js"></script>
    <script src="assets/vendor/aos/aos.js"></script>
    <script src="assets/vendor/glightbox/js/glightbox.min.js"></script>
    <script src="assets/vendor/swiper/swiper-bundle.min.js"></script>
    <script src="assets/vendor/purecounter/purecounter_vanilla.js"></script>
    <script src="assets/vendor/imagesloaded/imagesloaded.pkgd.min.js"></script>
    <script src="assets/vendor/isotope-layout/isotope.pkgd.min.js"></script>

    <!-- Main JS File -->
    <script src="assets/js/main.js"></script>

    <!-- jquery
============================================ -->
    <script src="file/js/vendor/jquery-1.12.4.min.js"></script>
    <!-- bootstrap JS
============================================ -->
    <script src="file/js/bootstrap.min.js"></script>
    <!-- wow JS
============================================ -->
    <script src="file/js/wow.min.js"></script>
    <!-- price-slider JS
============================================ -->
    <script src="file/js/jquery-price-slider.js"></script>
    <!-- owl.carousel JS
============================================ -->
    <script src="file/js/owl.carousel.min.js"></script>
    <!-- scrollUp JS
============================================ -->
    <script src="file/js/jquery.scrollUp.min.js"></script>
    <!-- meanmenu JS
============================================ -->
    <script src="file/js/meanmenu/jquery.meanmenu.js"></script>
    <!-- counterup JS
============================================ -->
    <script src="file/js/counterup/jquery.counterup.min.js"></script>
    <script src="file/js/counterup/waypoints.min.js"></script>
    <script src="file/js/counterup/counterup-active.js"></script>
    <!-- mCustomScrollbar JS
============================================ -->
    <script src="file/js/scrollbar/jquery.mCustomScrollbar.concat.min.js"></script>
    <!-- sparkline JS
============================================ -->
    <script src="file/js/sparkline/jquery.sparkline.min.js"></script>
    <script src="file/js/sparkline/sparkline-active.js"></script>

    <!-- flot JS
	============================================ -->
    <script src="file/js/flot/flot-widget-anatic-active.js"></script>
    <script src="file/js/flot/chart-tooltips.js"></script>
    <script src="file/js/flot/flot-active.js"></script>

    <!-- knob JS
============================================ -->
    <script src="file/js/knob/jquery.knob.js"></script>
    <script src="file/js/knob/jquery.appear.js"></script>
    <script src="file/js/knob/knob-active.js"></script>
    <!--  wave JS
============================================ -->
    <script src="file/js/wave/waves.min.js"></script>
    <script src="file/js/wave/wave-active.js"></script>
    <!-- plugins JS
	============================================ -->
    <script src="file/js/plugins.js"></script>

    <!-- main JS
============================================ -->
    <script src="file/js/main.js"></script>
    <!-- tawk chat JS
============================================ -->
    <script src="file/js/tawk-chat.js"></script>

    <!--Start of Tawk.to Script-->
    <script type="text/javascript">
        var Tawk_API = Tawk_API || {}, Tawk_LoadStart = new Date();
        (function () {
            var s1 = document.createElement("script"), s0 = document.getElementsByTagName("script")[0];
            s1.async = true;
            s1.src = 'https://embed.tawk.to/68497f0465ad5d190bef8e0e/1itfhgb0g';
            s1.charset = 'UTF-8';
            s1.setAttribute('crossorigin', '*');
            s0.parentNode.insertBefore(s1, s0);
        })();
    </script>
    <!--End of Tawk.to Script-->
    <style>
        .nav-link .badge {
    font-size: 0.75rem;
}
    </style>
</body>
</html>
