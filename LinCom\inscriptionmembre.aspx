﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="inscriptionmembre.aspx.cs" Inherits="LinCom.inscriptionmembre" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h2>Inscription des Jeunes d'impact social</h2>
                       <%-- <p class="mb-0">Odio et unde deleniti. Deserunt numquam exercitationem. Officiis quo odio sint voluptas consequatur ut a odio voluptatem. Sit dolorum debitis veritatis natus dolores. Quasi ratione sint. Sit quaerat ipsum dolorem.</p>

                 --%>   </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">Connection</a></li>
                    <li class="current"><a href="inscriptionmembre.aspx">Inscription des Jeunes</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">
        <!-- Contact Section -->
        <section id="contact" class="contact section">

            <!-- Section Title -->
            <div class="container section-title" data-aos="fade-up">
                <h2>Inscription</h2>
                <p>Si vous etes une organisation des jeunes d'impact social, voici votre partail d'inscrirption pour rejoindre le reseau des autres acteurs</p>
            </div>
            <!-- End Section Title -->

            <div class="container" data-aos="fade-up" data-aos-delay="100">

                <div class="row gx-lg-0 gy-4">

                    <div class="col-lg-2">
                        <div class="info-container d-flex flex-column align-items-center justify-content-center">
                            <%-- <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="200">
                             <i class="bi bi-geo-alt flex-shrink-0"></i>
                             <div>
                                 <h3>Address</h3>
                                 <p>A108 Adam Street, New York, NY 535022</p>
                             </div>
                         </div>
                         <!-- End Info Item -->

                         <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="300">
                             <i class="bi bi-telephone flex-shrink-0"></i>
                             <div>
                                 <h3>Call Us</h3>
                                 <p>******* 55488 55</p>
                             </div>
                         </div>
                         <!-- End Info Item -->

                         <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="400">
                             <i class="bi bi-envelope flex-shrink-0"></i>
                             <div>
                                 <h3>Email Us</h3>
                                 <p><EMAIL></p>
                             </div>
                         </div>
                         <!-- End Info Item -->

                         <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="500">
                             <i class="bi bi-clock flex-shrink-0"></i>
                             <div>
                                 <h3>Open Hours:</h3>
                                 <p>Mon-Sat: 11AM - 23PM</p>
                             </div>
                         </div>
                         <!-- End Info Item -->--%>
                        </div>

                    </div>

                    <div class="col-lg-8">
                        <div class="php-email-form" data-aos="fade" data-aos-delay="100">
                            <div class="row gy-4">
                                <div class="col-md-6 ">
                                    <input type="text" name="name" class="form-control" runat="server" id="txtnm" placeholder="Nom" required="">
                                    <span id="errortxtnm" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>
                                <div class="col-md-6 ">
                                    <input type="text" name="name" runat="server" id="txtprenom" class="form-control" placeholder="Prénom" required="">
                                </div>
                                <div class="col-md-6 ">
                                    <input type="text" name="name" runat="server" id="txtphone" class="form-control" placeholder="Numero de téléphone" required="">
                                    <span id="errorphone" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>
                                <div class="col-md-6 ">
                                    <asp:DropDownList ID="drpdsexe" class="form-control-plaintext" runat="server">
                                        <asp:ListItem Value="Masculin">Masculin</asp:ListItem>
                                        <asp:ListItem Value="Féminin">Féminin</asp:ListItem>
                                    </asp:DropDownList>
                                    <span id="errorsexe" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>
                                <div class="col-md-6 ">
                                    <label>Date de Naissance</label>
                                    <input type="date" runat="server" id="txtdatecreation" name="name" class="form-control" placeholder="Date de Naissance" required="">
                                    <span id="errordatenaiss" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>



                                <div class="col-md-12 ">
                                    <h6>Information sur la localisation</h6>

                                </div>
                                <div class="col-md-6 ">
                                    <asp:DropDownList ID="drpdprov" class="form-control" runat="server" AutoPostBack="true" OnSelectedIndexChanged="drpd_prov_SelectedIndexChanged">
                                        <asp:ListItem>Selection la province</asp:ListItem>
                                    </asp:DropDownList>
                                    <span id="errorprov" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>

                                <div class="col-md-6">
                                    <asp:DropDownList ID="drpdcom" class="form-control" runat="server">
                                        <asp:ListItem>Selection la commune</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                                <div class="col-md-12 ">
                                    <input type="text" name="name" runat="server" id="txt_adress" class="form-control" placeholder="Adresse complète de l'organisation" required="">
                                </div>

                                <div class="col-md-6 ">
                                    <asp:DropDownList ID="drpdexiste" class="form-control-plaintext" runat="server" AutoPostBack="true" OnSelectedIndexChanged="drpdexiste_SelectedIndexChanged">
                                        <asp:ListItem Value="-1">Appartenez-vous dans une organisation?</asp:ListItem>
                                        <asp:ListItem Value="oui">Oui</asp:ListItem>
                                        <asp:ListItem Value="non">Non</asp:ListItem>
                                    </asp:DropDownList>
                                    <span id="errorexiste" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>
                                <div class="col-md-6" runat="server" id="divorg" visible="false">
                                    <asp:DropDownList ID="drpdorg" class="form-control" runat="server">
                                        <asp:ListItem>Selection l'organisation</asp:ListItem>
                                    </asp:DropDownList>
                                </div>

                                <div class="col-md-6 ">
                                    <input type="email" runat="server" id="txtemail" name="name" class="form-control" placeholder="votre Email" required="">
                                    <span id="erroremail" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>
                                <div class="col-md-6 ">
                                    <input type="email" runat="server" id="txtConfirmEmail" name="name" class="form-control" placeholder="Confirmer votre Email" required="">
                                    <span id="erroremailconfirm" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>
                                <div class="col-md-6 ">
                                    <input type="password" runat="server" id="txtpswd" name="name" class="form-control" placeholder="Votre mot de passe" required="">
                                    <span id="errorpswdcom" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>
                                <div class="col-md-6 ">
                                    <input type="password" runat="server" id="txtconfirmpswd" name="name" class="form-control" placeholder="Confirmer votre mot de passe" required="">
                                    <span id="errorpswdconfirm" style="color: red; display: none;">Ce champ nom est requis.</span>
                                </div>

                                <div class="col-md-12">
                                    <asp:CheckBox ID="chkConditions" runat="server" />
                                    <asp:Label runat="server" AssociatedControlID="chkConditions" Text=" J’ai lu et j’accepte les " />
                                    <a href="politique.aspx" target="_blank">politiques et conditions</a><br />

                                </div>
                                <div class="col-md-12 text-center">

                                    <!-- Google reCAPTCHA -->
                                    <div class="g-recaptcha" data-sitekey="6LcJKC0rAAAAADijgx7o3xkGWEQ01zjnphoRKfcJ"></div>
                                    <br />
                                    <div class="loading">Loading</div>
                                    <div class="error-message"></div>
                                    <div class="sent-message">Your message has been sent. Thank you!</div>

                                    <button type="submit" runat="server" id="btnreng" onserverclick="btnreng_ServerClick">Enregistrer</button>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2">
                        <div class="info-container d-flex flex-column align-items-center justify-content-center">
                            <%-- <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="200">
           <i class="bi bi-geo-alt flex-shrink-0"></i>
           <div>
               <h3>Address</h3>
               <p>A108 Adam Street, New York, NY 535022</p>
           </div>
       </div>
       <!-- End Info Item -->

       <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="300">
           <i class="bi bi-telephone flex-shrink-0"></i>
           <div>
               <h3>Call Us</h3>
               <p>******* 55488 55</p>
           </div>
       </div>
       <!-- End Info Item -->

       <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="400">
           <i class="bi bi-envelope flex-shrink-0"></i>
           <div>
               <h3>Email Us</h3>
               <p><EMAIL></p>
           </div>
       </div>
       <!-- End Info Item -->

       <div class="info-item d-flex" data-aos="fade-up" data-aos-delay="500">
           <i class="bi bi-clock flex-shrink-0"></i>
           <div>
               <h3>Open Hours:</h3>
               <p>Mon-Sat: 11AM - 23PM</p>
           </div>
       </div>
       <!-- End Info Item -->--%>
                        </div>

                    </div>


                    <!-- End Contact Form -->

                </div>

            </div>

        </section>
        <!-- /Contact Section -->

    </main>
    <script type="text/javascript">
        function validateForm() {
            var isValid = true;

            // Efface les anciens messages d'erreur
            $('#errortxtnm,#erroremail,#errorphone,#errortxtnm').hide();

            // Vérifie si le champ est vide
            if ($('#<%= txtnm.ClientID %>').val().trim() === "") {
                $('#errortxtnm').show();
                isValid = false;
            }

            return isValid; // Empêche l'envoi si false
        }
    </script>

</asp:Content>
