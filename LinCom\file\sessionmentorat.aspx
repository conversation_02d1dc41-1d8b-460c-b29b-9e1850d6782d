﻿<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" ValidateRequest="false" AutoEventWireup="true" CodeBehind="sessionmentorat.aspx.cs" Inherits="LinCom.file.sessionmentorat" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
      <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">
                                    <div class="breadcomb-ctn">
                                        <h2>Gestion Session Mentorat</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">
                                    <a data-toggle="tooltip" data-placement="left" href="listsessionmentorat.aspx" title="Liste des sessions" class="btn">Liste des Sessions</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    <div id="div_msg_success" runat="server" visible="false" class="alert alert-success alert-dismissible fade show" role="alert">
        <span id="msg_success" runat="server"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <div id="div_msg_error" runat="server" visible="false" class="alert alert-danger alert-dismissible fade show" role="alert">
        <span id="msg_error" runat="server"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- Form Element area Start-->
    <div class="form-element-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">
                        <div class="cmp-tb-hd bcs-hd">
                            <p style="color:red">Remplissez les champs obligatoires avec astérisque (*)</p>
                        </div>

                        <div class="row">
                                <!-- Programme de Mentorat -->
                                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                    <div class="form-group ic-cmp-int form-elet-mg">
                                        <div class="form-ic-cmp">
                                            <i class="notika-icon notika-edit"></i>
                                        </div>
                                        <div class="nk-int-st">
                                            <label>Programme Mentorat *</label>
                                            <asp:DropDownList class="form-control" ID="drpdProgramme" runat="server">
                                                <asp:ListItem Value="-1">Sélectionner le programme</asp:ListItem>
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                </div>

                                <!-- Mentor -->
                                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                    <div class="form-group ic-cmp-int">
                                        <div class="form-ic-cmp">
                                            <i class="notika-icon notika-edit"></i>
                                        </div>
                                        <div class="nk-int-st">
                                            <label>Mentor*</label>
                                            <asp:DropDownList class="form-control" ID="drpdMentor" runat="server">
                                                <asp:ListItem Value="-1">Sélectionner le mentor</asp:ListItem>
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                </div>

                                <!-- Mentoré -->
                                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                    <div class="form-group ic-cmp-int">
                                        <div class="form-ic-cmp">
                                            <i class="notika-icon notika-edit"></i>
                                        </div>
                                        <div class="nk-int-st">
                                            <label>Mentoré *</label>
                                            <asp:DropDownList class="form-control" ID="drpdMentore" runat="server">
                                                <asp:ListItem Value="-1">Sélectionner le mentoré</asp:ListItem>
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                </div>

                                <!-- Date Session -->
                                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                    <div class="form-group ic-cmp-int">
                                        <div class="form-ic-cmp">
                                            <i class="notika-icon notika-edit"></i>
                                        </div>
                                        <label>Date de la Session*</label>
                                        <div class="nk-int-st">
                                            <input type="datetime-local" id="txtDateSession" runat="server" class="form-control" />
                                        </div>
                                    </div>
                                </div>

                                <!-- Sujet -->
                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                    <div class="form-group ic-cmp-int">
                                        <div class="form-ic-cmp">
                                            <i class="notika-icon notika-edit"></i>
                                        </div>
                                        <label>Sujet de la Session*</label>
                                        <div class="nk-int-st">
                                            <input type="text" id="txtSujet" runat="server" class="html-editor" placeholder="Sujet de la session*" />
                                        </div>
                                    </div>
                                </div>

                                <!-- Notes -->
                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                    <div class="form-group ic-cmp-int">
                                        <div class="form-ic-cmp">
                                            <i class="notika-icon notika-form"></i>
                                        </div>
                                        <label>Note de la Session*</label>
                                        <div class="nk-int-st">
                                            <textarea id="txtNotes" runat="server" class="html-editor" rows="4" placeholder="Notes de la session"></textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- Boutons -->
                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                    <div class="payment-adress">
                                        <button type="button" id="btnEnregistrer" runat="server" onserverclick="btnEnregistrer_ServerClick" class="btn btn-success notika-btn-success">
                                            <i class="notika-icon notika-sent"></i> Enregistrer
                                        </button>
                                        <a href="listsessionmentorat.aspx" class="btn btn-secondary">
                                            <i class="notika-icon notika-back"></i> Retour à la liste
                                        </a>
                                    </div>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Form Element area End-->
</asp:Content>
