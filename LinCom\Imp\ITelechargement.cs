﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ITelechargement
    {
        int Ajout(TelechargementRessources_Class add);
        void Chargement_GDV(GridView GV_apv);
        void search(GridView GV_apv, string code);
        void afficherDetails(int code, TelechargementRessources_Class pr);
        void afficherDetails(string code, TelechargementRessources_Class pr);
        int edit(TelechargementRessources_Class cl, int id);
        int supprimer(int id);

        void chargerTelecharg(DropDownList lst);
        int count();
    }
}
