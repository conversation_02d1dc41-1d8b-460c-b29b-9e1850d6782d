﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class listtypeorganisation : System.Web.UI.Page
    {
        TypeOrganisation_Class cat = new TypeOrganisation_Class();
        ITypeOrganisation obj = new TypeOrganisationImp();
        int info;
        static string typenm; static int id, idpers, rol;
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();

        static string imge, imge1, pdfe, nameorg;
        long ide; long idorg;
        static int rolid;
        long index;
        protected void Page_Load(object sender, EventArgs e)
        {
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {
                ide = Convert.ToInt64(idperso.Value);
                rolid = Convert.ToInt32(role.Value);
            }
            else
            {
                Response.Redirect("~/login.aspx");
            }

            objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
            idorg = Convert.ToInt64(memorg.OrganisationId);
            objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
            nameorg = org.Nom;
            if (!IsPostBack)
            {

                getDataGDV();

            }
        }
        public void getDataGDV()
        {
            obj.ChargerTypesOrganisation(gdv);
            //  nbr.Text = obj.count().ToString();

        }
        protected void gdv_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "view")
            {
                Response.Redirect("~/file/typeorganisation.aspx?name=" + index);

            }
            if (e.CommandName == "delete")
            {
                try
                {
                    obj.AfficherDetails(index, cat);
                    info = obj.Supprimer(Convert.ToInt32(cat.TypeOrganisationId));
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listtypeorganisation.aspx");
                    }
                    else
                    {
                        //  msg.Text = "Modification echoue";
                        //msg.Text = id.ToString();
                    }


                }
                catch (SqlException ex)
                {
                    // msg.Text = "Cette Province existe deja";
                }
            }
        }

        protected void btn_srch_Click(object sender, EventArgs e)
        {
            //if (txt_srch.Value == "")
            //    getDataGDV();
            //else obj.search(gdv, txt_srch.Value);
        }
    }
}