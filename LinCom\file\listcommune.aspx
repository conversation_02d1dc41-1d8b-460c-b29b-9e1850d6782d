﻿<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="listcommune.aspx.cs" Inherits="LinCom.file.listcommune" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
            <!-- Breadcomb area Start-->
<div class="breadcomb-area">
	<div class="container">
		<div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
				<div class="breadcomb-list">
					<div class="row">
						<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
							<div class="breadcomb-wp">
								<div class="breadcomb-icon">
									<i class="notika-icon notika-form"></i>
								</div>
								<div class="breadcomb-ctn">
									<h2>Liste des Communes</h2>
								</div>
							</div>
						</div>
						<div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
							<div class="breadcomb-report">

								<a data-toggle="tooltip" data-placement="left"  href="commune.aspx" title="Clique sur ce button pour creer une nouvelle commune" class="btn">Nouvelle commune</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- Breadcomb area End-->
	  	  <!-- Data Table area Start-->
  <div class="normal-table-area">
      <div class="container">
         <div class="row">
            
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
       <div class="data-table-list">
           
           <div class="table-responsive">
                                       <asp:GridView ID="gdv" class="table table-striped"  runat="server" CssClass="datatbemp"
             AutoGenerateColumns="False" EmptyDataText="Aucune Donnée Trouvée pour votre Rercherche"
           GridLines="None" width="100%" OnRowCommand="gdv_RowCommand"
            > 
                <AlternatingRowStyle BackColor="#DCDCDC" />
      
                 <Columns>
     <asp:TemplateField HeaderText="Province" FooterText="Province">
         <ItemTemplate>
             <asp:Label ID="lb_pvc" runat="server" Text='<%# Eval("nomp") %>'></asp:Label>
         </ItemTemplate>
     </asp:TemplateField>


         <asp:TemplateField HeaderText="Nom de la Commune" FooterText="Nom de la Province">
         <ItemTemplate>
             <asp:Label ID="lb_nm" runat="server" Text='<%# Eval("nom") %>'></asp:Label>
         </ItemTemplate>
     </asp:TemplateField>
      <asp:TemplateField HeaderText="Action" FooterText="Action">
         <ItemTemplate>
              <asp:Button class="btn btn-info btn-fix" BackColor="Green" ID="btnEdit" CommandName="view" CommandArgument='<%# Eval("name") %>'  runat="server" Text='Editer'  />
             <asp:Button class="btn btn-danger btn-fix" BackColor="Red" ID="btn_del" runat="server" Text="Delete" CommandName="delete" CommandArgument='<%# Eval("name") %>' OnClientClick=" return confirm('Voulez-Vous vraiment supprimer??')"  />
         </ItemTemplate>
        
     </asp:TemplateField>
 </Columns>


<EditRowStyle Font-Bold="True"></EditRowStyle>

<EmptyDataRowStyle HorizontalAlign="Center" Font-Names="century" Font-Size="X-Large"></EmptyDataRowStyle>
               
            </asp:GridView>
                    <!-- end basic table  -->
                      
           </div>
       </div>
   </div>
         </div>
     </div>
 </div>
 <!-- Data Table area End-->

     <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.js"></script>
      <script>
          $(document).ready(function () {
              $(".datatbemp").prepend($("<thead></thead>").append($(this).find("tr:first"))).DataTable();
          });
      </script>

</asp:Content>
