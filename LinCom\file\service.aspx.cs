﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class service : System.Web.UI.Page
    {

        private int info;
        string membreId, nsco;
        Membre_Class membreClass = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objMembre = new MembreImp();
        IProvince objProvince = new ProvinceImp();
        ICommune objCommune = new CommuneImp();
        ICommonCode co = new CommonCode();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        IPoste obj = new PosteImp();
        Post_Class pos = new Post_Class();
        Post_Class p = new Post_Class();
        DomainePost_Class actco = new DomainePost_Class();
        IDomainePost objactco = new DomainePostImp();
        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        CategoriePost_Class catpost = new CategoriePost_Class();
        ICategoryPost objcatpost = new CategoriePostImp();

        DataTable dat = new DataTable();
        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;

        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];
            if (!IsPostBack)
            {

                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte
                    rolid = Convert.ToInt32(role.Value);//roleconnecte

                }
                else Response.Redirect("~/login.aspx");

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;

                initial_msg();
                objdom.chargerDomaineInterventionOrganisation(drpddomai, idorg);


                objcatpost.chargerCategoriePost(drpdcateg);
                objcatpost.afficherDetails("service", catpost);
                drpdcateg.SelectedValue = catpost.CategoriePostId.ToString();

                if (ViewState["Record"] == null)
                {//depense
                    dat.Columns.Add("#Code");
                    dat.Columns.Add("Domaine d'Intervention");

                    ViewState["Record"] = dat;
                }


                if (nsco == null)
                {
                    btnEnregistrer.InnerText = "Enregistrer";

                }
                else

                {
                    btnEnregistrer.InnerText = "Modifier";
                    AfficherArticle();
                }

            }
        }
        private string Uploadoc(FileUpload fil)
        {
            if (fil.HasFile)
            {
                string fileName = Path.GetFileName(fil.FileName);
                string extension = Path.GetExtension(fileName).ToLower();

                string[] validExtensions = { ".pdf" };

                if (!validExtensions.Contains(extension))
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Le document doit avoir ces extensions : .pdf ";

                    return null;
                }

                if (fil.PostedFile.ContentLength > 104857600)
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Votre document est trop volumineux. Il doit faire moins de 100MB.";

                    return null;
                }

                string path = Server.MapPath("~/file/servic/") + fileName;
                fil.SaveAs(path);
                imge = fileName;

            }
            else
            {
                if (nsco == null)
                {
                    imge = "No Doc";
                }
                else
                {
                    obj.AfficherDetails(Convert.ToInt64(nsco), idorg, 0, pos);

                    imge = pos.pdf;


                }
            }
            return imge;

        }
        protected void doma_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = true;
            ajourdom.Visible = false;
        }
        void ajourdomainepost()
        {
            try
            {
                int dc = GridView1.Rows.Count;
                if (dc > 0)
                {
                    foreach (GridViewRow row in this.GridView1.Rows)
                    {
                        if (row.RowType == DataControlRowType.DataRow)
                        {
                            obj.AfficherDetailsname(co.GenerateSlug(txttitre.Value), idorg, 0, pos);
                            index = (long)pos.PostId;

                            actco.PostId = index;
                            objdom.AfficherDetails(Convert.ToInt32(row.Cells[0].Text), domai, idorg, 0);
                            actco.DomaineInterventionOrganisationId = Convert.ToInt32(domai.DomaineInterventionOrganisationId);
                            actco.DateCreation = DateTime.Now;
                            actco.statut = "actif";
                            actco.MembreId = ide;
                            actco.OrganisationId = idorg;


                            objactco.Ajouter(actco);

                        }
                    }

                }

            }
            catch (Exception ex)
            { // logguer ou afficher les erreurs pour le débogage
                System.Diagnostics.Debug.WriteLine("Erreur dans Domaines d'Intervention de l'organisation : " + ex.Message);

            }

        }
        protected void btnajoutdom_Click(object sender, EventArgs e)
        {
            try
            {
                if (drpddomai.SelectedValue == "-1" || drpddomai.SelectedValue == "0")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez votre domaine ')</script>");
                }
                else
                {
                    dom.Visible = true;
                    int i = GridView1.Rows.Count;
                    dat = (DataTable)ViewState["Record"];
                    dat.Rows.Add(drpddomai.SelectedValue, drpddomai.SelectedItem);
                    GridView1.DataSource = dat;
                    GridView1.DataBind();

                }

            }
            catch (Exception ec)
            {

            }
        }

        protected void btnvider_Click(object sender, EventArgs e)
        {
            dom.Visible = true;
            dat = (DataTable)ViewState["Record"];
            dat.Rows.Clear();
            GridView1.DataSource = dat;
            GridView1.DataBind();
        }
        public void remplissagedomainepost()
        {
            try
            {
                if (drpddomai.SelectedValue == "-1" || drpddomai.SelectedValue == "0")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez votre domaine ')</script>");
                }
                else
                {
                    dom.Visible = true;
                    int i = GridView1.Rows.Count;
                    dat = (DataTable)ViewState["Record"];
                    dat.Rows.Add(drpddomai.SelectedValue, drpddomai.SelectedItem);
                    GridView1.DataSource = dat;
                    GridView1.DataBind();

                }

            }
            catch (Exception ec)
            {

            }
        }
        protected void btnannuldom_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = true;
            dat = (DataTable)ViewState["Record"];
            dat.Rows.Clear();
            GridView1.DataSource = dat;
            GridView1.DataBind();
        }
        protected void ajoudom_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = false;
            ajourdom.Visible = true;
        }

        protected void btnajoutnouvdom_ServerClick(object sender, EventArgs e)
        {
            //  ad();
        }

        protected void drpddomai_SelectedIndexChanged(object sender, EventArgs e)
        {
            dom.Visible = true;
            remplissagedomainepost();
        }
        protected void btnajourdom_ServerClick(object sender, EventArgs e)
        {
            remplissagedomainepost();
        }
        public void vidermessage()
        {
            // Cacher les messages
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
            msg_error.Text = string.Empty;
        }
        public void ViderChamps()
        {
            // Réinitialiser les champs texte
            txttitre.Value = string.Empty;
            txtdescription.Value = string.Empty;
            txtresume.Value = string.Empty;
            txtdatepub.Value = string.Empty;

            drpdcateg.SelectedValue = "-1";
            drpdstatut.SelectedValue = "-1";

            // Réinitialiser les FileUpload (pas possible directement, nécessiterait un rechargement de la page)
            // fileupd et fileupd1 : rechargement automatique du formulaire requis pour les vider.
            // fileupdoc : même chose.

          
        }


        private string UploadImage(FileUpload fil, int cd)
        {
            if (fil.HasFile)
            {
                string fileName = Path.GetFileName(fil.FileName);
                string extension = Path.GetExtension(fileName).ToLower();

                string[] validExtensions = { ".png", ".jpeg", ".gif", ".jpg", ".jfif" };

                if (!validExtensions.Contains(extension))
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "La photo doit avoir ces extensions : .png, .jpg, .gif, .jpeg, .jfif. ";

                    return null;
                }

                if (fil.PostedFile.ContentLength > 104857600)
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Votre photo est trop volumineuse. Elle doit faire moins de 100MB.";

                    return null;
                }

                string path = Server.MapPath("~/file/servic/") + fileName;
                fil.SaveAs(path);
                imge = fileName;

            }
            else
            {
                if (nsco == null)
                {
                    imge = "No Image";
                }
                else
                {

                    obj.AfficherDetails(Convert.ToInt64(nsco), idorg, 0, pos);

                    if (cd == 0)
                    { imge = pos.photo; }
                    else if (cd == 1)
                    { imge = pos.video; }

                }
            }
            return imge;

        }

        public void AjoutPost()
        {
            try
            { // Vérification des champs requis
                if (string.IsNullOrWhiteSpace(txttitre.Value) ||
                    string.IsNullOrWhiteSpace(txtdescription.Value) ||
                    string.IsNullOrWhiteSpace(txtresume.Value) ||
                    string.IsNullOrWhiteSpace(txtdatepub.Value) ||
                    drpdcateg.SelectedValue == "-1" ||
                    drpdetat.SelectedValue == "-1" ||
                    drpdstatut.SelectedValue == "-1")
                {
                    div_msg_succes.Visible = false;
                    div_msg_error.Visible = true;
                    msg_error.Text = "Veuillez renseigner tous les champs obligatoires.";
                    return;
                }


                p.Titre = txttitre.Value;
                p.Contenu = txtdescription.InnerText;
                p.CategoriePostId = Convert.ToInt32(drpdcateg.SelectedValue);
                p.MembreId = ide;
                p.DatePublication = txtdatepub.Value;
                p.summery = txtresume.Value;
                p.author = nameorg;
                p.photo = UploadImage(fileupd, 0);
                p.OrganisationId = idorg;
                p.video = UploadImage(fileupd, 1);
                p.number_of_view = 0;
                p.like = 0;
                p.dislike = 0;
                p.starttime = "";
                p.eventduration = "";
                p.eventplace = "";
                p.whoattend = "";
                p.qualificationattend = "";
                p.langueevent = "";
                p.externevent = "";
                p.MOIS = Convert.ToDateTime(txtdatepub.Value).Month.ToString();
                p.ANNEE = Convert.ToDateTime(txtdatepub.Value).Year.ToString();
                p.lien_isncription = "";
                p.pdf = "No Doc";
                p.name = co.GenerateSlug(txttitre.Value);
                p.DateCreation = DateTime.Now;
                p.DateModification = DateTime.Now;
                p.EstPublie = drpdstatut.SelectedValue;
                p.EstPublieEvent = drpdetat.SelectedValue;
                p.statut = co.GenerateSlug(drpdcateg.SelectedItem.ToString());
                p.etat = "0";

                info = obj.Ajout(p);
                if (info == 1)
                {
                    ajourdomainepost();

                    div_msg_succes.Visible = true;
                    div_msg_error.Visible = false;
                    msg_succes.Text = "Service/Produit enregistré avec succès.";

                    ViderChamps();

                }
                else
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.Text = "Ce Village Existe deja";

                }


            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.Text = "Cette Province Existe deja";

            }

            //LabelMsg.Text = obj.ajouterProvince(prov).ToString();
        }

        public void upte()
        {
            try
            { // Vérification des champs requis
                if (string.IsNullOrWhiteSpace(txttitre.Value) ||
                    string.IsNullOrWhiteSpace(txtdescription.Value) ||
                    string.IsNullOrWhiteSpace(txtresume.Value) ||
                    string.IsNullOrWhiteSpace(txtdatepub.Value) ||
                    drpdcateg.SelectedValue == "-1" ||
                    drpdetat.SelectedValue == "-1" ||
                    drpdstatut.SelectedValue == "-1")
                {
                    div_msg_succes.Visible = false;
                    div_msg_error.Visible = true;
                    msg_error.Text = "Veuillez renseigner tous les champs obligatoires.";
                    return;
                }


                p.Titre = txttitre.Value;
                p.Contenu = txtdescription.Value;
                p.CategoriePostId = Convert.ToInt32(drpdcateg.SelectedValue);

                p.DatePublication = txtdatepub.Value;
                p.summery = txtresume.Value;
                p.author = nameorg;
                p.photo = UploadImage(fileupd, 0);

                p.video = UploadImage(fileupd, 1);

                p.starttime = "";
                p.eventduration = "";
                p.eventplace = "";
                p.whoattend = "";

                p.langueevent = "";

                p.MOIS = Convert.ToDateTime(txtdatepub.Value).Month.ToString();
                p.ANNEE = Convert.ToDateTime(txtdatepub.Value).Year.ToString();
                p.lien_isncription = "";
                p.pdf = "No Doc";
                p.name = co.GenerateSlug(txttitre.Value);

                p.DateModification = DateTime.Now;
                p.EstPublie = drpdstatut.SelectedValue;
                p.EstPublieEvent = drpdetat.SelectedValue;
                p.statut = co.GenerateSlug(drpdcateg.SelectedItem.ToString());
                p.etat = "0";

                obj.AfficherDetails(Convert.ToInt64(nsco), idorg, 0, pos);

                info = obj.edit(p, Convert.ToInt64(pos.PostId), idorg);
                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    div_msg_error.Visible = false;
                    msg_succes.Text = "Service/Produit modifié avec succès.";

                    ViderChamps();
                  
                }
                else
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.Text = "Ce Post Existe deja";

                }


            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.Text = "Ce Post Existe deja";

            }

        }
        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;

        }
        public void AfficherArticle()
        {
            if (nsco != null)
            {
                obj.AfficherDetails(Convert.ToInt64(nsco), idorg, 0, p);


                txttitre.Value = p.Titre;
                txtdescription.Value = p.Contenu;
                drpdcateg.SelectedValue = p.CategoriePostId.ToString();

                txtdatepub.Value = p.DatePublication;
                txtresume.Value = p.summery;
                drpdetat.SelectedValue = p.EstPublieEvent;

                // Définir les valeurs des DropDownList si présentes
                if (drpdstatut.Items.FindByValue(p.EstPublie) != null)
                    drpdstatut.SelectedValue = p.EstPublie;


            }
        }

        protected void btnEnregistrer_ServerClick(object sender, EventArgs e)
        {
            // Vérifier la validation côté serveur
            if (!Page.IsValid)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Veuillez corriger les erreurs avant de continuer.";
                return;
            }

            if (nsco == null)
            {
                AjoutPost();
            }
            else
            {
                upte();
            }
        }
        protected void btn_enreg_Click(object sender, EventArgs e)
        {
            if (nsco == null)
            {
                AjoutPost();

            }
            else
                upte();

        }
    }
}