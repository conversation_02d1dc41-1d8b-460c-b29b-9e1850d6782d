﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.DynamicData;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
 
    public class ProvinceImp : IProvince
    {

        int msg;
        Province p = new Province();
        public int add(Province_Class add)
        {
            using (Connection con = new Connection())
            {

                p.Nom = add.Nom;
                p.name = add.name;
                try
                {
                    con.Provinces.Add(p);

                    if (con.SaveChanges() == 1)
                    {
                        con.Provinces.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }


        public void afficherDetails(int code, Province_Class pr)
        {
            using (Connection con = new Connection())
            {
                p = con.Provinces.Where(x => x.ProvinceId == code).FirstOrDefault();

                if (p != null)
                {
                    pr.ProvinceId=p.ProvinceId;
                    pr.Nom = p.Nom;

                    pr.name = p.name;

                }

            }
        }

        public void afficherDetails(string code, Province_Class pr)
        {
            using (Connection con = new Connection())
            {
                p = con.Provinces.Where(x => x.name == code).FirstOrDefault();

                if (p != null)
                {
                    pr.ProvinceId = p.ProvinceId;
                    pr.Nom = p.Nom;

                    pr.name = p.name;

                }

            }
        }

        public void Chargement_GDV(GridView GV_apv)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.Provinces
                           select new
                           {


                               id = ep.ProvinceId,
                               Nom = ep.Nom,

                               name = ep.name,


                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

     
        public void chargerProvince(DropDownList ddw)
        {
            ddw.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Provinces select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    ddw.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner la Province";
                    ddw.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.ProvinceId.ToString();
                        item.Text = data.Nom;
                        ddw.Items.Add(item);
                    }

                }
                else
                {
                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    ddw.Items.Add(item0);
                }

            }
        }

        public int count()
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                var b = (from l in con.Provinces
                         select l).Count();
                n = b;
            }
            return n;
        }

        public int edit(Province_Class cl, int id)
        {
            using (Connection con = new Connection())
            {

                p.Nom = cl.Nom;
                p.name = cl.name;
                try
                {
                    con.Provinces.Add(p);

                    if (con.SaveChanges() == 1)
                    {

                        con.Provinces.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }

        public void search(GridView GV_apv, string code)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.Provinces
                           where ep.Nom.Contains(code) || ep.Nom.StartsWith(code)
                           select new
                           {


                               id = ep.ProvinceId,
                               Nom = ep.Nom,

                               name = ep.name,


                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public int supprimer(int id)
        {
            using (Connection con = new Connection())
            {

                p = con.Provinces.Where(x => x.ProvinceId == id).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.Provinces.Attach(p);

                con.Provinces.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }
    }
}