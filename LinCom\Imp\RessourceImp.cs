﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class RessourceImp : IRessource
    {
        int msg, n;

        Ressource p = new Ressource();
        public int Ajout(Ressources_Class add)

        {
            using (Connection con = new Connection())
            {

                p.Titre = add.Titre;
                p.Description = add.Description;
                p.DateFormation = add.DateFormation;
                p.<PERSON>chier = add.Fichier;
                p.AuteurId = add.AuteurId;
                p.name = add.name;
                p.photocouverture = add.photocouverture;
                p.nombrepage = add.nombrepage;
                p.typeressources = add.typeressources;
                p.OrganisationId = add.OrganisationId;
                p.nbrevue = add.nbrevue;
                p.nbrelike = add.nbrelike;
                p.DatePublication = add.DatePublication;
                p.DateCreation = add.DateCreation;
                p.statut = add.statut;
                p.MembreId = add.MembreId;
                p.MOIS = add.MOIS;
                p.ANNEE = add.ANNEE;


                try
                {
                    con.Ressources.Add(p);

                    if (con.SaveChanges() == 1)
                    {
                        con.Ressources.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }

        public void AfficherDetails(long id, long idorg,int cd, Ressources_Class pr)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    p = con.Ressources.Where(x => x.RessourceId == id && x.OrganisationId==idorg).FirstOrDefault();

                    if (p != null)
                    {
                        pr.RessourceId = p.RessourceId;
                        pr.Titre = p.Titre;
                        pr.Description = p.Description;
                        pr.DateFormation = p.DateFormation;
                        pr.Fichier = p.Fichier;
                        pr.AuteurId = p.AuteurId;
                        pr.name = p.name;
                        pr.Fichier = p.Fichier;
                        pr.photocouverture = p.photocouverture;
                        pr.nombrepage = p.nombrepage;
                        pr.typeressources = p.typeressources;
                        pr.OrganisationId = p.OrganisationId;
                        pr.nbrevue = p.nbrevue;
                        pr.nbrelike = p.nbrelike;
                        pr.DatePublication = p.DatePublication;
                        pr.DateCreation = p.DateCreation;
                        pr.statut = p.statut;
                        pr.MembreId = p.MembreId;
                        pr.OrganisationId = p.OrganisationId;
                        pr.MOIS = p.MOIS;
                        pr.ANNEE = p.ANNEE;
                    }
                }
              

            }
        }

        public void AfficherDetails(string name, long idorg, int cd, Ressources_Class pr)
        {
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    p = con.Ressources.Where(x => x.name == name && x.OrganisationId == idorg).FirstOrDefault();

                    if (p != null)
                    {
                        pr.RessourceId = p.RessourceId;
                        pr.Titre = p.Titre;
                        pr.Description = p.Description;
                        pr.DateFormation = p.DateFormation;
                        pr.Fichier = p.Fichier;
                        pr.AuteurId = p.AuteurId;
                        pr.name = p.name;
                        pr.Fichier = p.Fichier;
                        pr.photocouverture = p.photocouverture;
                        pr.nombrepage = p.nombrepage;
                        pr.typeressources = p.typeressources;
                        pr.OrganisationId = p.OrganisationId;
                        pr.nbrevue = p.nbrevue;
                        pr.nbrelike = p.nbrelike;
                        pr.DatePublication = p.DatePublication;
                        pr.DateCreation = p.DateCreation;
                        pr.statut = p.statut;
                        pr.MembreId = p.MembreId;
                        pr.OrganisationId = p.OrganisationId;
                        pr.MOIS = p.MOIS;
                        pr.ANNEE = p.ANNEE;
                    }
                }
                else if (cd==1)
                {
                    p = con.Ressources.Where(x => x.typeressources == name && x.OrganisationId == idorg).FirstOrDefault();

                    if (p != null)
                    {
                        pr.RessourceId = p.RessourceId;
                        pr.Titre = p.Titre;
                        pr.Description = p.Description;
                        pr.DateFormation = p.DateFormation;
                        pr.Fichier = p.Fichier;
                        pr.AuteurId = p.AuteurId;
                        pr.name = p.name;
                        pr.Fichier = p.Fichier;
                        pr.photocouverture = p.photocouverture;
                        pr.nombrepage = p.nombrepage;
                        pr.typeressources = p.typeressources;
                        pr.OrganisationId = p.OrganisationId;
                        pr.nbrevue = p.nbrevue;
                        pr.nbrelike = p.nbrelike;
                        pr.DatePublication = p.DatePublication;
                        pr.DateCreation = p.DateCreation;
                        pr.statut = p.statut;
                        pr.MembreId = p.MembreId;
                        pr.OrganisationId = p.OrganisationId;
                        pr.MOIS = p.MOIS;
                        pr.ANNEE = p.ANNEE;
                    }
                }
                else if (cd == 2)
                {
                    p = con.Ressources.Where(x => x.name == name).FirstOrDefault();

                    if (p != null)
                    {
                        pr.RessourceId = p.RessourceId;
                        pr.Titre = p.Titre;
                        pr.Description = p.Description;
                        pr.DateFormation = p.DateFormation;
                        pr.Fichier = p.Fichier;
                        pr.AuteurId = p.AuteurId;
                        pr.name = p.name;
                        pr.Fichier = p.Fichier;
                        pr.photocouverture = p.photocouverture;
                        pr.nombrepage = p.nombrepage;
                        pr.typeressources = p.typeressources;
                        pr.OrganisationId = p.OrganisationId;
                        pr.nbrevue = p.nbrevue;
                        pr.nbrelike = p.nbrelike;
                        pr.DatePublication = p.DatePublication;
                        pr.DateCreation = p.DateCreation;
                        pr.statut = p.statut;
                        pr.MembreId = p.MembreId;
                        pr.OrganisationId = p.OrganisationId;
                        pr.MOIS = p.MOIS;
                        pr.ANNEE = p.ANNEE;
                    }
                }

            }
        }
        public int MiseajourData(Ressources_Class add, long id, long idorg, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    p = con.Ressources.Where(x => x.RessourceId == id).FirstOrDefault();

                    try
                    {
                        p.nbrevue = add.nbrevue;

                        if (con.SaveChanges() == 1)
                        {
                            con.Ressources.Add(p);
                            con.Entry(p).State = EntityState.Modified;
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch (Exception e)
                    {

                    }

                }

                return msg;
            }
        }

        public void Chargement_GDV(GridView GV_apv,long idorg,string name ,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var obj = (from p in con.Ressources
                               join o in con.Organisations on p.OrganisationId equals o.OrganisationId
                               join m in con.Membres on p.MembreId equals m.MembreId
                               where p.OrganisationId== idorg
                               select new
                               {
                                   id = p.RessourceId,
                                   Titre = p.Titre,
                                   Description = p.Description,
                                   DateFormation = p.DateFormation,
                                   Fichier = p.Fichier,
                                   AuteurId = p.AuteurId,
                                   name = p.name,
                                   photocouverture = p.photocouverture,
                                   nombrepage = p.nombrepage,
                                   typeressources = p.typeressources,
                                   OrganisationId = p.OrganisationId,
                                   nbrevue = p.nbrevue,
                                   nbrelike = p.nbrelike,
                                   DatePublication = p.DatePublication,
                                   DateCreation = p.DateCreation,
                                   statut = p.statut,
                                   MembreId = p.MembreId,
                                   MOIS = p.MOIS,
                                   ANNEE = p.ANNEE,


                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();
                }
                
             

            }
        }

        public void Chargement_Listview(ListView GV_apv, long idorg,int iddom,long idres,string name, string statut, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var obj = (from p in con.Ressources
                               join o in con.Organisations on p.OrganisationId equals o.OrganisationId
                               join m in con.Membres on p.MembreId equals m.MembreId
                               where p.statut==statut
                               select new
                               {
                                   id = p.RessourceId,
                                   Titre = p.Titre,
                                   Description = p.Description,
                                   DateFormation = p.DateFormation,
                                   Fichier = p.Fichier,
                                   AuteurId = p.AuteurId,
                                   name = p.name,
                                   photocouverture = p.photocouverture,
                                   nombrepage = p.nombrepage,
                                   typeressources = p.typeressources,
                                   OrganisationId = p.OrganisationId,
                                   nbrevue = p.nbrevue,
                                   nbrelike = p.nbrelike,
                                   DatePublication = p.DatePublication,
                                   DateCreation = p.DateCreation,
                                   statut = p.statut,
                                   MembreId = p.MembreId,
                                   MOIS = p.MOIS,
                                   ANNEE = p.ANNEE,
                                   Auteur=o.Nom,
                                   Photoong=o.Logo,


                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();
                }
                else if (cd==1)
                {
                    var obj = (from p in con.Ressources
                               join o in con.Organisations on p.OrganisationId equals o.OrganisationId
                               join m in con.Membres on p.MembreId equals m.MembreId
                               where p.typeressources == name && p.statut==statut
                               select new
                               {
                                   id = p.RessourceId,
                                   Titre = p.Titre,
                                   Description = p.Description,
                                   DateFormation = p.DateFormation,
                                   Fichier = p.Fichier,
                                   AuteurId = p.AuteurId,
                                   name = p.name,
                                   photocouverture = p.photocouverture,
                                   nombrepage = p.nombrepage,
                                   typeressources = p.typeressources,
                                   OrganisationId = p.OrganisationId,
                                   nbrevue = p.nbrevue,
                                   nbrelike = p.nbrelike,
                                   DatePublication = p.DatePublication,
                                   DateCreation = p.DateCreation,
                                   statut = p.statut,
                                   MembreId = p.MembreId,
                                   MOIS = p.MOIS,
                                   ANNEE = p.ANNEE,
                                   Auteur = o.Nom,
                                   Photoong = o.Logo,


                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();
                }
                else if (cd == 2)
                {//les types de ressources selon les organisations
                    var obj = (from p in con.Ressources
                               join o in con.Organisations on p.OrganisationId equals o.OrganisationId
                               join m in con.Membres on p.MembreId equals m.MembreId
                               where o.OrganisationId==idorg && p.typeressources == name && p.statut == statut
                               select new
                               {
                                   id = p.RessourceId,
                                   Titre = p.Titre,
                                   Description = p.Description,
                                   DateFormation = p.DateFormation,
                                   Fichier = p.Fichier,
                                   AuteurId = p.AuteurId,
                                   name = p.name,
                                   photocouverture = p.photocouverture,
                                   nombrepage = p.nombrepage,
                                   typeressources = p.typeressources,
                                   OrganisationId = p.OrganisationId,
                                   nbrevue = p.nbrevue,
                                   nbrelike = p.nbrelike,
                                   DatePublication = p.DatePublication,
                                   DateCreation = p.DateCreation,
                                   statut = p.statut,
                                   MembreId = p.MembreId,
                                   MOIS = p.MOIS,
                                   ANNEE = p.ANNEE,
                                   Auteur = o.Nom,
                                   Photoong = o.Logo,


                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();
                }
                else if (cd == 3)
                {//les types de ressources selon les domaines d'intervention des organisations
                    var obj = (from p in con.Ressources
                               join o in con.Organisations on p.OrganisationId equals o.OrganisationId
                               join m in con.Membres on p.MembreId equals m.MembreId
                               join domr in con.DomaineRessources on p.RessourceId equals domr.RessourceId
                               join domorg in con.DomaineInterventionOrganisations on domr.DomaineInterventionId equals domorg.DomaineInterventionId
                               join dom in con.DomaineInterventions on domorg.DomaineInterventionId equals dom.DomaineInterventionId
                               where dom.DomaineInterventionId==iddom && o.OrganisationId==idorg && p.typeressources == name && p.statut == statut
                               select new
                               {
                                   id = p.RessourceId,
                                   Titre = p.Titre,
                                   Description = p.Description,
                                   DateFormation = p.DateFormation,
                                   Fichier = p.Fichier,
                                   AuteurId = p.AuteurId,
                                   name = p.name,
                                   photocouverture = p.photocouverture,
                                   nombrepage = p.nombrepage,
                                   typeressources = p.typeressources,
                                   OrganisationId = p.OrganisationId,
                                   nbrevue = p.nbrevue,
                                   nbrelike = p.nbrelike,
                                   DatePublication = p.DatePublication,
                                   DateCreation = p.DateCreation,
                                   statut = p.statut,
                                   MembreId = p.MembreId,
                                   MOIS = p.MOIS,
                                   ANNEE = p.ANNEE,
                                   Auteur = o.Nom,
                                   Photoong = o.Logo,


                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();
                }
                else if (cd == 4)
                {//les types de ressources des organisations
                    var obj = (from p in con.Ressources
                               join o in con.Organisations on p.OrganisationId equals o.OrganisationId
                               join m in con.Membres on p.MembreId equals m.MembreId
                               where p.typeressources == name && p.statut == statut
                               select new
                               {
                                   id = p.RessourceId,
                                   Titre = p.Titre,
                                   Description = p.Description,
                                   DateFormation = p.DateFormation,
                                   Fichier = p.Fichier,
                                   AuteurId = p.AuteurId,
                                   name = p.name,
                                   photocouverture = p.photocouverture,
                                   nombrepage = p.nombrepage,
                                   typeressources = p.typeressources,
                                   OrganisationId = p.OrganisationId,
                                   nbrevue = p.nbrevue,
                                   nbrelike = p.nbrelike,
                                   DatePublication = p.DatePublication,
                                   DateCreation = p.DateCreation,
                                   statut = p.statut,
                                   MembreId = p.MembreId,
                                   MOIS = p.MOIS,
                                   ANNEE = p.ANNEE,
                                   Auteur = o.Nom,
                                   Photoong = o.Logo,


                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();
                }


            }
        }


        public void chargerRessourc(DropDownList ddw, long idorg, string name,string intitule,int cd)
        {
            ddw.Items.Clear();
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var obj = (from p in con.Ressources
                               join o in con.Organisations on p.OrganisationId equals o.OrganisationId
                               join m in con.Membres on p.MembreId equals m.MembreId
                               where p.OrganisationId == idorg && p.typeressources==name
                               select new
                               {
                                   RessourceId = p.RessourceId,
                                   Titre = p.Titre,

                               }).ToList();
                    if (obj != null && obj.Count() > 0)
                    {
                        ListItem item0 = new ListItem();
                        item0.Value = "-1";
                        item0.Text = intitule;
                        ddw.Items.Add(item0);

                        foreach (var data in obj)
                        {
                            ListItem item = new ListItem();
                            item.Value = data.RessourceId.ToString();
                            item.Text = data.Titre;
                            ddw.Items.Add(item);
                        }

                    }
                    else
                    {
                        ListItem item0 = new ListItem();
                        item0.Value = "-1";
                        item0.Text = "Aucune donnée";
                        ddw.Items.Add(item0);
                    }
                }


            }
        }

        public int count()
        {
            using (Connection con = new Connection())
            {
                var b = (from l in con.Ressources
                         select l).Count();
                n = b;
            }
            return n;
        }

        public int edit(Ressources_Class add, long id, long idorg)
        {
            using (Connection con = new Connection())
            {
                p = con.Ressources.Where(x => x.RessourceId == id && x.OrganisationId==idorg).FirstOrDefault();

                try
                {
                    p.Titre = add.Titre;
                    p.Description = add.Description;
                    p.DateFormation = add.DateFormation;
                    p.Fichier = add.Fichier;
                    p.AuteurId = add.AuteurId;
                    p.name = add.name;
                    p.Fichier = add.Fichier;
                    p.photocouverture = add.photocouverture;
                    p.nombrepage = add.nombrepage;
                    p.typeressources = add.typeressources;
                    p.OrganisationId = add.OrganisationId;
                  
                    p.DatePublication = add.DatePublication;
                    p.statut = add.statut;
                 
                    p.MOIS = add.MOIS;
                    p.ANNEE = add.ANNEE;



                    if (con.SaveChanges() == 1)
                    {
                        con.Ressources.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }
                return msg;
            }
        }

        public void search(GridView GV_apv, string code)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.Ressources

                           select new
                           {
                               id = p.RessourceId,
                               Titre = p.Titre,
                               Description = p.Description,
                               DateFormation = p.DateFormation,
                               Fichier = p.Fichier,
                               AuteurId = p.AuteurId,
                               name = p.name,
                               photocouverture = p.photocouverture,
                               nombrepage = p.nombrepage,
                               typeressources = p.typeressources,
                               OrganisationId = p.OrganisationId,
                               nbrevue = p.nbrevue,
                               nbrelike = p.nbrelike,
                               DatePublication = p.DatePublication,
                               DateCreation = p.DateCreation,
                               statut = p.statut,
                               MembreId = p.MembreId,
                               MOIS = p.MOIS,
                               ANNEE = p.ANNEE,



                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public int supprimer(long id,long idorg)
        {
            using (Connection con = new Connection())
            {

                p = con.Ressources.Where(x => x.RessourceId == id && x.OrganisationId==idorg).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.Ressources.Attach(p);

                con.Ressources.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }
    }
}