﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Script.Serialization;

namespace LinCom.Imp
{
    public class APIYoutube_Class
    {
   
    public class YouTubeVideo
    {
        public string title { get; set; }
        public string videoId { get; set; }
        public string description { get; set; }
        public string thumbnail { get; set; }
    }
        public bool IsInternetAvailable()
        {
            try
            {
                using (var client = new WebClient())
                using (client.OpenRead("https://www.google.com"))
                {
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }


        public List<YouTubeVideo> GetVideosFromYouTube(string apikey, string channelID)
{
    List<YouTubeVideo> videos = new List<YouTubeVideo>();

    if (!IsInternetAvailable())
    {
        // Affiche un message dans la console ou sur l'interface
        System.Diagnostics.Debug.WriteLine("Aucune connexion Internet disponible.");
        // Par exemple si vous avez un Label : lblMessage.Text = "Pas de connexion Internet.";
        return videos;
    }

    try
    {
        string apiUrl = $"https://www.googleapis.com/youtube/v3/search?key={apikey}&channelId={channelID}&part=snippet&type=video&maxResults=6&order=date";

        using (WebClient client = new WebClient())
        {
            string json = client.DownloadString(apiUrl);
            var serializer = new JavaScriptSerializer();
            dynamic result = serializer.DeserializeObject(json);

            foreach (var item in result["items"])
            {
                YouTubeVideo video = new YouTubeVideo
                {
                    title = item["snippet"]["title"],
                    videoId = item["id"]["videoId"],
                    description = item["snippet"]["description"],
                    thumbnail = item["snippet"]["thumbnails"]["high"]["url"]
                };
                videos.Add(video);
            }
        }
    }
    catch (WebException webEx)
    {
        System.Diagnostics.Debug.WriteLine("Erreur de connexion : " + webEx.Message);
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine("Erreur lors de la récupération des vidéos : " + ex.Message);
    }

    return videos;
}

    }
} 