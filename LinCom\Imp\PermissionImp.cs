﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class PermissionImp : IPermission
    {
        int msg;
        private permission permission = new permission();

        public void AfficherDetails(int idPermission, Permission_Class permissionClass)
        {
            using (Connection con = new Connection())
            {
                var p = con.permissions.FirstOrDefault(x => x.PermissionID == idPermission);
                if (p != null)
                {
                    permissionClass.PermissionID = p.PermissionID;
                    permissionClass.CodePermission = p.CodePermission;
                    permissionClass.Description = p.Description;
                    permissionClass.Access = p.Access;
                    permissionClass.Code_Menu = p.Code_Menu;
                    permissionClass.RoleMembreID = p.RoleMembreID;
                    permissionClass.OrganisationId = p.OrganisationId;
                    permissionClass.RoleMembreID = p.RoleMembreID;
                }
            }
        }

        public int Ajouter(Permission_Class permissionClass)
        {
            using (Connection con = new Connection())
            {
                permission.Access = permissionClass.Access;
                permission.Code_Menu = permissionClass.Code_Menu;
                permission.CodePermission = permissionClass.CodePermission;
                permission.Description = permissionClass.Description;
                permission.OrganisationId = permissionClass.OrganisationId;
                permission.RoleMembreID = permissionClass.RoleMembreID;
                permission.RoleMembreID = permissionClass.RoleMembreID;

                try
                {
                    con.permissions.Add(permission);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerPermissions(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var query = from p in con.permissions
                            select new
                            {
                                p.PermissionID,
                                p.CodePermission,
                                p.Access,
                                p.RoleMembreID,
                                
                                p.Description,
                                p.OrganisationId,
                                p.Code_Menu,

                            };

                gdv.DataSource = query.OrderBy(x => x.Code_Menu).ThenBy(x => x.Access).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerPermissionsParCode(GridView gdv, string code)
        {
            using (Connection con = new Connection())
            {
                var query = from p in con.permissions
                            where p.CodePermission == code
                            select new
                            {
                                p.PermissionID,
                                p.Code_Menu,
                                p.CodePermission,
                                p.Access,



                            };

                gdv.DataSource = query.OrderBy(x => x.Access).ToList();
                gdv.DataBind();
            }
        }


        public void chargerPermission(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.permissions select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner la Permission";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.PermissionID.ToString();
                        item.Text = data.Access;
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }


        public int Modifier(Permission_Class permissionClass)
        {
            using (Connection con = new Connection())
            {
                var p = con.permissions.FirstOrDefault(x => x.PermissionID == permissionClass.PermissionID);
                if (p != null)
                {
                    p.Code_Menu = permissionClass.Code_Menu;
                    p.CodePermission = permissionClass.CodePermission;
                    p.Access = permissionClass.Access;
                    p.RoleMembreID = permissionClass.RoleMembreID;
                    p.Description = permissionClass.Description;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(int idPermission)
        {
            using (Connection con = new Connection())
            {
                var p = con.permissions.FirstOrDefault(x => x.PermissionID == idPermission);
                if (p != null)
                {

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}