﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.Xml;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class notification : System.Web.UI.Page
    {
        INotification objnotif = new NotificationImp();
        Notification_Class notif = new Notification_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();

        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();

        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;
        protected void Page_Load(object sender, EventArgs e)
        {
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin
                long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte

            }
            else Response.Redirect("login.aspx");

            objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
            idorg = Convert.ToInt64(memorg.OrganisationId);
            objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
            nameorg = org.Nom;

            if (!IsPostBack)
            {
                LoadNotifications();
            }
        }

        protected void ddlFiltreNotif_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlFiltreNotif.SelectedValue== "toutes")
            {
                objnotif.ChargerNotificationsRepeater(rptNotifications, ide, -1, "", 0);
            }
            else
            {
                objnotif.ChargerNotificationsRepeater(rptNotifications, ide, 0, "", 1);

            }
        }

        protected void rptNotifications_ItemCommand(object source, RepeaterCommandEventArgs e)
        {
            if (e.CommandName == "Voir")
            {
                int idNotification = Convert.ToInt32(e.CommandArgument);

                // 1. Marquer la notification comme lue
                objnotif.MarquerCommeLue(idNotification);
                objnotif.AfficherDetails(idNotification, notif);
                // 2. Récupérer l'URL de redirection
                string url = notif.UrlRedirection;

                // 3. Rediriger
                Response.Redirect(url);
            }
        }

        protected void btnToutLu_ServerClick(object sender, EventArgs e)
        {
            if (ide>0)
            {
                MarqueToutesNotificationsCommeLues();
            }
          
        }

        private void MarqueToutesNotificationsCommeLues()
        {
            objnotif.MarquerToutCommeLues(ide);
        }
        private void LoadNotifications()
        {
            objnotif.ChargerNotificationsRepeater(rptNotifications,ide,-1,"",0);
          
        }

        protected void Timer1_Tick(object sender, EventArgs e)
        {
            LoadNotifications();
        }

        protected void btnNotifications_Click(object sender, EventArgs e)
        {
           // pnlNotifications.Visible = !pnlNotifications.Visible;
            MarkNotificationsAsRead();
        }

        private void MarkNotificationsAsRead()
        {
            objnotif.MarquerToutCommeLues(ide);
        }
    }
}