﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class PartenaireOrganisationImp : IPartenaireorganisation
    {
        int msg;
        PartenairesOrganisation p = new PartenairesOrganisation();
        public int Ajout(PartenaireOrganisation_Class add)
        {
            using (Connection con = new Connection())
            {
                p.Nom = add.Nom;
                p.Contact = add.Contact;
                p.logo = add.logo;
                p.statut = add.statut;
                p.etat = add.etat;
                p.Description = add.Description;
                p.lienwebsite = add.lienwebsite;
                p.Email = add.Email;
                p.OrganisationId = add.OrganisationId;

                try
                {
                    con.PartenairesOrganisations.Add(p);
                    if (con.SaveChanges() == 1)
                    {
                        con.PartenairesOrganisations.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }

        public void afficherDetails(long idorg,int code, PartenaireOrganisation_Class pr)
        {
            using (Connection con = new Connection())
            {
                p = con.PartenairesOrganisations.Where(x => x.PartenaireorganisationId == code && x.OrganisationId==idorg).FirstOrDefault();

                pr.PartenaireorganisationId = p.PartenaireorganisationId;
                pr.Nom = p.Nom;
                pr.Contact = p.Contact;
                pr.logo = p.logo;
                pr.statut = p.statut;
                pr.etat = p.etat;
                pr.Description = p.Description;
                pr.lienwebsite = p.lienwebsite;
                pr.Email = p.Email;
                pr.OrganisationId = p.OrganisationId;

            }
        }

        public void Chargement_GDV(GridView GV_apv,long idorg, string statu)
        {
            using (Connection con = new Connection())
            {

                var obj = (from p in con.PartenairesOrganisations
                           where p.OrganisationId==idorg && p.statut==statu
                           select new
                           {
                               id = p.PartenaireorganisationId,
                               Nom = p.Nom,
                               Contact = p.Contact,
                               logo = p.logo,
                               statut = p.statut,
                               etat = p.etat,
                               Description = p.Description,
                               lienwebsite = p.lienwebsite,
                               Email = p.Email,
                               OrganisationId = p.OrganisationId,



                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }

        }

        public void Chargement_GDV(ListView GV_apv,long idorg, string statu)
        {
            using (Connection con = new Connection())
            {

                var obj = (from p in con.PartenairesOrganisations
                           where p.OrganisationId==idorg && p.statut == statu
                           select new
                           {
                               id = p.PartenaireorganisationId,
                               Nom = p.Nom,
                               Contact = p.Contact,
                               logo = p.logo,
                               statut = p.statut,
                               etat = p.etat,
                               Description = p.Description,
                               lienwebsite = p.lienwebsite,
                               email = p.Email,
                               OrganisationId = p.OrganisationId,


                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }

        }

        public int count(long idorg,string statu)
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                var b = (from l in con.PartenairesOrganisations
                         where l.OrganisationId==idorg && l.statut == statu
                         select l).Count();
                n = b;
            }
            return n;
        }

        public int edit(PartenaireOrganisation_Class add,long idorg, int id)
        {
            using (Connection con = new Connection())
            {
                p = con.PartenairesOrganisations.Where(x => x.PartenaireorganisationId == id && x.OrganisationId==idorg).FirstOrDefault();

                try
                {
                    p.Nom = add.Nom;
                    p.Contact = add.Contact;
                    p.logo = add.logo;
                    p.statut = add.statut;
                    p.etat = add.etat;
                    p.Description = add.Description;
                    p.lienwebsite = add.lienwebsite;
                    p.Email = add.Email;

                    if (con.SaveChanges() == 1)
                    {
                        con.PartenairesOrganisations.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;

                }
                catch (Exception e)
                {

                }
                return msg;
            }

        }


        public void search(GridView GV_apv, string code,long idorg, string statu)
        {
            using (Connection con = new Connection())
            {

                var obj = (from p in con.PartenairesOrganisations
                           where (p.Nom.Contains(code)) && p.statut == statu && p.OrganisationId==idorg
                           select new
                           {
                               id = p.PartenaireorganisationId,
                               Nom = p.Nom,
                               Contact = p.Contact,
                               logo = p.logo,
                               statut = p.statut,
                               etat = p.etat,
                               Description = p.Description,
                               lienwebsite = p.lienwebsite,
                               email = p.Email,
                               OrganisationId = p.OrganisationId,

                           }).ToList();


                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }

        }

        public int supprimer(long idorg,int id)
        {
            using (Connection con = new Connection())
            {

                p = con.PartenairesOrganisations.Where(x => x.PartenaireorganisationId == id && x.OrganisationId==idorg).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.PartenairesOrganisations.Attach(p);

                con.PartenairesOrganisations.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }
    }
}