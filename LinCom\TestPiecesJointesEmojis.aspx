<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="TestPiecesJointesEmojis.aspx.cs" Inherits="LinCom.TestPiecesJointesEmojis" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Test Pièces Jointes et Émojis - LinCom</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #fafafa; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #17a2b8; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; background: #007bff; color: white; }
        .result-box { margin: 10px 0; padding: 15px; border-radius: 5px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .emoji-demo { font-size: 24px; margin: 10px 0; padding: 10px; background: white; border-radius: 5px; }
        .file-demo { margin: 10px 0; padding: 10px; background: white; border-radius: 5px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h1>🧪 Test Pièces Jointes et Émojis - LinCom</h1>
            <p class="info">Cette page teste spécifiquement les fonctionnalités de pièces jointes et d'émojis.</p>
            
            <!-- Test des Émojis -->
            <div class="test-section">
                <h3>😊 Test des Émojis</h3>
                <p>Testez la conversion des codes d'émojis en symboles.</p>
                
                <div>
                    <label>Saisissez du texte avec des codes d'émojis :</label><br/>
                    <asp:TextBox ID="txtEmojiInput" runat="server" Width="400px" 
                        placeholder="Exemple: Bonjour :smile: comment allez-vous? :heart: :thumbs_up:"></asp:TextBox>
                    <asp:Button ID="btnConvertirEmojis" runat="server" Text="Convertir" OnClick="btnConvertirEmojis_Click" CssClass="btn" />
                </div>
                
                <div class="emoji-demo">
                    <strong>Résultat :</strong><br/>
                    <asp:Label ID="lblEmojiResult" runat="server" Text="Saisissez du texte et cliquez sur Convertir."></asp:Label>
                </div>
                
                <div>
                    <strong>Codes d'émojis disponibles :</strong><br/>
                    :smile: :heart: :thumbs_up: :sad: :wink: :fire: :party: :coffee: :sun: :cat:
                </div>
            </div>

            <!-- Test des Pièces Jointes -->
            <div class="test-section">
                <h3>📎 Test des Pièces Jointes</h3>
                <p>Testez l'upload et la validation des fichiers.</p>
                
                <div>
                    <label>Sélectionnez un fichier :</label><br/>
                    <asp:FileUpload ID="fileUploadTest" runat="server" />
                    <asp:Button ID="btnTesterFichier" runat="server" Text="Tester Fichier" OnClick="btnTesterFichier_Click" CssClass="btn" />
                </div>
                
                <div class="file-demo">
                    <asp:Label ID="lblFileResult" runat="server" Text="Sélectionnez un fichier et cliquez sur Tester."></asp:Label>
                </div>
                
                <div>
                    <strong>Extensions autorisées :</strong><br/>
                    Images: .jpg, .jpeg, .png, .gif<br/>
                    Documents: .pdf, .doc, .docx, .txt<br/>
                    Archives: .zip, .rar<br/>
                    Multimédia: .mp3, .mp4, .avi
                </div>
            </div>

            <!-- Test Intégré -->
            <div class="test-section">
                <h3>🎯 Test Intégré Message avec Émojis et Pièce Jointe</h3>
                <p>Simulez l'envoi d'un message complet.</p>
                
                <div>
                    <label>Message avec émojis :</label><br/>
                    <asp:TextBox ID="txtMessageComplet" runat="server" TextMode="MultiLine" Rows="3" Width="400px"
                        placeholder="Tapez votre message avec des émojis :smile: :heart:"></asp:TextBox>
                </div>
                
                <div style="margin: 10px 0;">
                    <label>Pièce jointe (optionnelle) :</label><br/>
                    <asp:FileUpload ID="fileUploadMessage" runat="server" />
                </div>
                
                <div>
                    <asp:Button ID="btnTesterMessageComplet" runat="server" Text="Tester Message Complet" 
                        OnClick="btnTesterMessageComplet_Click" CssClass="btn" />
                </div>
                
                <div class="result-box">
                    <asp:Label ID="lblMessageCompletResult" runat="server" Text="Préparez votre message et cliquez sur Tester."></asp:Label>
                </div>
            </div>

            <!-- Résumé -->
            <div class="test-section">
                <h3>📊 Résumé des Tests</h3>
                <asp:Label ID="lblResume" runat="server" Text="Aucun test effectué." CssClass="info"></asp:Label>
            </div>
        </div>
    </form>
</body>
</html>
