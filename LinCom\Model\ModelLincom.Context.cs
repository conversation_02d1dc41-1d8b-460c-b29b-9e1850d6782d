﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace LinCom.Model
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public partial class Connection : DbContext
    {
        public Connection()
            : base("name=Connection")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<ActiviteProjet> ActiviteProjets { get; set; }
        public virtual DbSet<AvisRessource> AvisRessources { get; set; }
        public virtual DbSet<budgets_activite> budgets_activite { get; set; }
        public virtual DbSet<CategoriePost> CategoriePosts { get; set; }
        public virtual DbSet<CommentPost> CommentPosts { get; set; }
        public virtual DbSet<Commune> Communes { get; set; }
        public virtual DbSet<ConditionUtilisation> ConditionUtilisations { get; set; }
        public virtual DbSet<Conversation> Conversations { get; set; }
        public virtual DbSet<ConversationParticipant> ConversationParticipants { get; set; }
        public virtual DbSet<DomaineFormation> DomaineFormations { get; set; }
        public virtual DbSet<DomaineIntervention> DomaineInterventions { get; set; }
        public virtual DbSet<DomaineInterventionOrganisation> DomaineInterventionOrganisations { get; set; }
        public virtual DbSet<DomainePost> DomainePosts { get; set; }
        public virtual DbSet<DomaineProjet> DomaineProjets { get; set; }
        public virtual DbSet<DomaineRessource> DomaineRessources { get; set; }
        public virtual DbSet<FAQ> FAQs { get; set; }
        public virtual DbSet<FeedbackMentor> FeedbackMentors { get; set; }
        public virtual DbSet<FeedbackMentoree> FeedbackMentorees { get; set; }
        public virtual DbSet<FichierMessage> FichierMessages { get; set; }
        public virtual DbSet<Financement> Financements { get; set; }
        public virtual DbSet<Formation> Formations { get; set; }
        public virtual DbSet<Forum> Forums { get; set; }
        public virtual DbSet<Langue> Langues { get; set; }
        public virtual DbSet<MembreProfil> MembreProfils { get; set; }
        public virtual DbSet<Membre> Membres { get; set; }
        public virtual DbSet<MembresOrganisation> MembresOrganisations { get; set; }
        public virtual DbSet<Mentor> Mentors { get; set; }
        public virtual DbSet<Mentore> Mentores { get; set; }
        public virtual DbSet<menu> menus { get; set; }
        public virtual DbSet<menupermission> menupermissions { get; set; }
        public virtual DbSet<Message> Messages { get; set; }
        public virtual DbSet<MessageStatu> MessageStatus { get; set; }
        public virtual DbSet<Notification> Notifications { get; set; }
        public virtual DbSet<Organisation> Organisations { get; set; }
        public virtual DbSet<ParamettreApplication> ParamettreApplications { get; set; }
        public virtual DbSet<Partenaire> Partenaires { get; set; }
        public virtual DbSet<PartenairesOrganisation> PartenairesOrganisations { get; set; }
        public virtual DbSet<ParticipantConversation> ParticipantConversations { get; set; }
        public virtual DbSet<permission> permissions { get; set; }
        public virtual DbSet<PolitiqueConfidentialite> PolitiqueConfidentialites { get; set; }
        public virtual DbSet<Post> Posts { get; set; }
        public virtual DbSet<PreferenceCooky> PreferenceCookies { get; set; }
        public virtual DbSet<Programmementorat> Programmementorats { get; set; }
        public virtual DbSet<ProgrammesEtInitiative> ProgrammesEtInitiatives { get; set; }
        public virtual DbSet<Projet> Projets { get; set; }
        public virtual DbSet<Province> Provinces { get; set; }
        public virtual DbSet<RepliesForum> RepliesForums { get; set; }
        public virtual DbSet<Ressource> Ressources { get; set; }
        public virtual DbSet<rolemembre> rolemembres { get; set; }
        public virtual DbSet<SessionMentorat> SessionMentorats { get; set; }
        public virtual DbSet<StatutUtilisateur> StatutUtilisateurs { get; set; }
        public virtual DbSet<Subscription> Subscriptions { get; set; }
        public virtual DbSet<SujetForum> SujetForums { get; set; }
        public virtual DbSet<SupportFormation> SupportFormations { get; set; }
        public virtual DbSet<TelechargementRessource> TelechargementRessources { get; set; }
        public virtual DbSet<Traduction> Traductions { get; set; }
        public virtual DbSet<TypeOrganisation> TypeOrganisations { get; set; }
    }
}
