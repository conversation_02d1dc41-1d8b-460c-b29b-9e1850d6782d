﻿<%@ Page Title=""  ValidateRequest="false" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="domainepost.aspx.cs" Inherits="LinCom.file.domainepost" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">

                                    <div class="breadcomb-ctn">
                                        <h2>Domaine des Posts</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">

                                    <a data-toggle="tooltip" data-placement="left" href="listdomainepost.aspx" title="Clique sur ce button pour visualiser la liste des Domaines des Posts" class="btn">Liste des Domaines des Posts</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcomb area End-->
    <!-- Form Element area Start-->
    <div class="form-element-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">

                        <div class="cmp-tb-hd bcs-hd">
                            <p style="color: red">Remplissez les champs obligatoires avec asterix</p>
                        </div>
                        <div class="alert-list">
                            <div class="alert alert-success alert-dismissible" role="alert" runat="server" id="div_msg_succes">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                                <span runat="server" id="msg_succes">Enregistrement réussi</span>
                            </div>
                            <div class="alert alert-danger alert-dismissible" role="alert" runat="server" id="div_msg_error">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                                <span runat="server" id="msg_error">Enregistrement échoué</span>
                            </div>
                        </div>
                        <div class="row">
                             <div class="col-lg-6 col-md-6 col-sm-4 col-xs-12">
     <div class="form-group ic-cmp-int form-elet-mg">
         <div class="form-ic-cmp">
             <i class="notika-icon notika-edit"></i>
         </div>
         <div class="nk-int-st">
             <label>Catégorie du Post *</label>
             <asp:DropDownList class="form-control" ID="drpdcateg" runat="server" AutoPostBack="true" OnSelectedIndexChanged="drpdcateg_SelectedIndexChanged" >

                 <asp:ListItem Value="-1">Selectionner le type</asp:ListItem>

             </asp:DropDownList>
         </div>

     </div>
 </div>
                            <div class="col-lg-6 col-md-6 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int form-elet-mg">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label runat="server" id="lblpost">Post *</label>
                                        <asp:DropDownList class="form-control" ID="drpdservice" runat="server">

                                            <asp:ListItem Value="-1">Selectionner le service/Projet</asp:ListItem>

                                        </asp:DropDownList>
                                    </div>

                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int form-elet-mg">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Domaine d'Intervention *</label>
                                        <asp:DropDownList class="form-control" ID="drpddomai" runat="server">

                                            <asp:ListItem Value="-1">Selectionner le domaine d'intervention</asp:ListItem>

                                        </asp:DropDownList>
                                    </div>

                                </div>
                            </div>

                            <div class="col-lg-6 col-md-6 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Statut de l'Activité  *</label>
                                        <asp:DropDownList class="form-control" ID="drpdstatut" runat="server">

                                            <asp:ListItem Value="-1">Selectionner le statut</asp:ListItem>
                                            <asp:ListItem Value="actif">Actif</asp:ListItem>
                                            <asp:ListItem Value="inactif">Inactif</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">

                        <div class="row">

                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <button type="button" runat="server" id="btnEnregistrer" onserverclick="btnEnregistrer_ServerClick" class="btn btn-success notika-gp-success">Ajouter</button>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Form Element area End-->
</asp:Content>
