﻿using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class listorganisation : System.Web.UI.Page
    {
        private OrganisationImp organisationImp = new OrganisationImp();
        private Organisation_Class organisationClass = new Organisation_Class();

        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();

        long ide; long idorg;
        static int rolid;
        static string nameorg;

        protected void Page_Load(object sender, EventArgs e)
        {
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {
                ide = Convert.ToInt64(idperso.Value);
                rolid = Convert.ToInt32(role.Value);
            }
            else
            {
                Response.Redirect("~/login.aspx");
            }

            objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
            idorg = Convert.ToInt64(memorg.OrganisationId);
            objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
            nameorg = org.Nom;
            if (!IsPostBack)
            {
                ChargerOrganisations();
            }
        }

        private void ChargerOrganisations()
        {
            organisationImp.ChargerGridView(gdv);
        }

        protected void gdv_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            string id = e.CommandArgument.ToString();

            if (e.CommandName == "view")
            {
                Response.Redirect("organisation.aspx?name=" + id);
            }
            else if (e.CommandName == "delete")
            {
                organisationImp.AfficherDetails(id,organisationClass);
                int result = organisationImp.Supprimer(organisationClass.OrganisationId);
                if (result > 0)
                {
                    // Recharger la liste après suppression
                    ChargerOrganisations();
                   
                }
                else
                {
                    // Vous pourriez ajouter un message d'erreur ici si nécessaire
                }
            }
        }
    }
}