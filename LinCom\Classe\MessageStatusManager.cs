using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;

namespace LinCom.Classe
{
    /// <summary>
    /// Gestionnaire avancé des statuts de messages
    /// </summary>
    public class MessageStatusManager
    {
        /// <summary>
        /// Énumération des statuts de message
        /// </summary>
        public enum StatutMessage
        {
            Envoye = 0,      // Message envoyé mais pas encore livré
            Livre = 1,       // Message livré (reçu par le serveur du destinataire)
            Lu = 2,          // Message lu par le destinataire
            Recu = 3         // Message reçu et affiché sur l'appareil du destinataire
        }

        /// <summary>
        /// Obtient le statut d'un message pour un utilisateur spécifique
        /// </summary>
        public static StatutMessage ObtenirStatutMessage(long messageId, long userId)
        {
            using (var con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(ms => 
                    ms.MessageId == messageId && ms.UserId == userId);

                if (status == null)
                    return StatutMessage.Envoye;

                return status.IsRead == 1 ? StatutMessage.Lu : StatutMessage.Livre;
            }
        }

        /// <summary>
        /// Met à jour le statut d'un message
        /// </summary>
        public static bool MettreAJourStatut(long messageId, long userId, StatutMessage nouveauStatut)
        {
            using (var con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(ms => 
                    ms.MessageId == messageId && ms.UserId == userId);

                if (status != null)
                {
                    status.IsRead = nouveauStatut == StatutMessage.Lu ? 1 : 0;
                    if (nouveauStatut == StatutMessage.Lu)
                    {
                        status.ReadAt = DateTime.Now;
                    }

                    try
                    {
                        con.SaveChanges();
                        return true;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Erreur mise à jour statut: {ex.Message}");
                        return false;
                    }
                }
                return false;
            }
        }

        /// <summary>
        /// Obtient tous les statuts d'un message (pour tous les participants)
        /// </summary>
        public static Dictionary<long, StatutMessage> ObtenirTousLesStatuts(long messageId)
        {
            var statuts = new Dictionary<long, StatutMessage>();

            using (var con = new Connection())
            {
                var statusList = con.MessageStatus.Where(ms => ms.MessageId == messageId).ToList();

                foreach (var status in statusList)
                {
                    statuts[status.UserId] = status.IsRead == 1 ? StatutMessage.Lu : StatutMessage.Livre;
                }
            }

            return statuts;
        }

        /// <summary>
        /// Marque tous les messages d'une conversation comme lus pour un utilisateur
        /// </summary>
        public static int MarquerConversationCommeLue(long conversationId, long userId)
        {
            using (var con = new Connection())
            {
                try
                {
                    var messagesNonLus = from ms in con.MessageStatus
                                        join m in con.Messages on ms.MessageId equals m.MessageId
                                        where m.ConversationId == conversationId 
                                              && ms.UserId == userId 
                                              && ms.IsRead == 0
                                        select ms;

                    int count = 0;
                    foreach (var status in messagesNonLus)
                    {
                        status.IsRead = 1;
                        status.ReadAt = DateTime.Now;
                        count++;
                    }

                    con.SaveChanges();
                    return count;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur marquage conversation: {ex.Message}");
                    return 0;
                }
            }
        }

        /// <summary>
        /// Obtient le nombre de messages non lus pour un utilisateur
        /// </summary>
        public static int CompterMessagesNonLus(long userId)
        {
            using (var con = new Connection())
            {
                try
                {
                    return con.MessageStatus.Count(ms => ms.UserId == userId && ms.IsRead == 0);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur comptage non lus: {ex.Message}");
                    return 0;
                }
            }
        }

        /// <summary>
        /// Obtient les conversations avec des messages non lus
        /// </summary>
        public static List<ConversationNonLue> ObtenirConversationsNonLues(long userId)
        {
            using (var con = new Connection())
            {
                try
                {
                    var conversationsNonLues = (from ms in con.MessageStatus
                                               join m in con.Messages on ms.MessageId equals m.MessageId
                                               join c in con.Conversations on m.ConversationId equals c.ConversationId
                                               where ms.UserId == userId && ms.IsRead == 0
                                               group ms by new { c.ConversationId, c.Sujet } into g
                                               select new ConversationNonLue
                                               {
                                                   ConversationId = g.Key.ConversationId,
                                                   Sujet = g.Key.Sujet,
                                                   NombreMessagesNonLus = g.Count()
                                               }).ToList();

                    return conversationsNonLues;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur conversations non lues: {ex.Message}");
                    return new List<ConversationNonLue>();
                }
            }
        }

        /// <summary>
        /// Génère l'icône de statut pour l'affichage
        /// </summary>
        public static string ObtenirIconeStatut(StatutMessage statut)
        {
            switch (statut)
            {
                case StatutMessage.Envoye:
                    return "📤"; // ou une classe CSS pour une icône
                case StatutMessage.Livre:
                    return "✓";
                case StatutMessage.Lu:
                    return "✓✓";
                case StatutMessage.Recu:
                    return "📱";
                default:
                    return "⏳";
            }
        }

        /// <summary>
        /// Obtient la couleur du statut pour l'affichage
        /// </summary>
        public static string ObtenirCouleurStatut(StatutMessage statut)
        {
            switch (statut)
            {
                case StatutMessage.Envoye:
                    return "#gray";
                case StatutMessage.Livre:
                    return "#blue";
                case StatutMessage.Lu:
                    return "#green";
                case StatutMessage.Recu:
                    return "#orange";
                default:
                    return "#gray";
            }
        }
    }

    /// <summary>
    /// Classe pour représenter une conversation avec des messages non lus
    /// </summary>
    public class ConversationNonLue
    {
        public long ConversationId { get; set; }
        public string Sujet { get; set; }
        public int NombreMessagesNonLus { get; set; }
    }
}
