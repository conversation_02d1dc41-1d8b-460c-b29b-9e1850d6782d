﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IMentor
    {
        void AfficherDetails(int mentorId, Mentor_Class mentor);
        int Ajouter(Mentor_Class mentor);
        int Modifier(Mentor_Class mentor,int id, long idorg,int cd);
        int Supprimer(int mentorId);
        int count(int id, long idprog, long idmem, long idorg, int cd);
        void ChargerMentorsParProgramme(GridView gdv, int programmeMentoratId);
        void chargerMentors(DropDownList lst);
        void ChargerMentorsGridview(GridView gdv, int id, long idprog, long idorg, string name, string status, int cd);
        void ChargerMentorsListview(ListView gdv, int id, long idprog, long idorg, string name, string status, int cd);


    }
}
