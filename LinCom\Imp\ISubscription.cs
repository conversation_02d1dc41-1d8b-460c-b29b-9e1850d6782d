﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ISubscription
    {
        void AfficherDetails(long subscriptionId, Subscription_Class subscriptionClass);
        int Ajouter(Subscription_Class subscriptionClass);
        void ChargerSubscriptions(GridView gdv);
        void ChargerSubscriptionsParMembre(GridView gdv, long membreId);
        void ChargerStatistiques(Repeater rpt);
        void chargerSubscription(DropDownList lst);
        int Modifier(Subscription_Class subscriptionClass);
        int Supprimer(long subscriptionId);
    }
}
