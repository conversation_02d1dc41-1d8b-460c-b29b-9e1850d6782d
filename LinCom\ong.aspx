﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="ong.aspx.cs" Inherits="LinCom.ong" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h2>Organisations des Jeunes d'impact social</h2>
                        <%--<p class="mb-0">Odio et unde deleniti. Deserunt numquam exercitationem. Officiis quo odio sint voluptas consequatur ut a odio voluptatem. Sit dolorum debitis veritatis natus dolores. Quasi ratione sint. Sit quaerat ipsum dolorem.</p>
                   --%>
                        </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">ONG</a></li>
                    <li class="current"><a href="ong.aspx">Liste des Organisations</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">
        <section class="py-4 bg-light border-top border-bottom">
            <div class="container">
                <div class="d-flex flex-column flex-md-row align-items-md-end justify-content-between gap-3">

                    <div class="search-group w-100">
                        <label class="form-label">Nom de l'organisation</label>
                        <input type="text" id="txtnom" runat="server" placeholder="Ex: Jeunes Leaders" class="form-control">
                    </div>

                    <div class="search-group w-100">
                        <label class="form-label">Domaine d'Intervention</label>
                        <asp:DropDownList ID="drpddomai" runat="server" data-parsley-trigger="change" autocomplete="off" class="form-select">
    <asp:ListItem Value="-1">Selectionner le domaine d'Intervention</asp:ListItem>
</asp:DropDownList>
                      
                    </div>

                    <div class="search-group w-100">
                        <label class="form-label">Ville (Province ou Commune)</label>
                        <input type="text" id="txtville" runat="server" placeholder="Ex: Bujumbura" class="form-control">
                    </div>

                    <div class="search-group">
                        <label class="d-none d-md-block invisible">Bouton</label>
                        <button class="btn btn-primary px-4 w-100" runat="server" id="btnsearch" onserverclick="btnsearch_ServerClick">Rechercher</button>
                    </div>

                </div>
            </div>
        </section>


        <section class="py-5 bg-white">
            <div class="container">
                <div class="row g-4">

                    <!-- Exemple de carte -->
                     <!-- Exemple de carte -->
                    <asp:ListView ID="listinst" runat="server" OnItemDataBound="listinst_ItemDataBound" OnItemCommand="listinst_ItemCommand">
                    
                        <ItemTemplate>
                            <div class="col-lg-4 col-md-6">
                                <div class="card h-100 shadow-sm">
                                    <img  src='<%# HttpUtility.HtmlEncode( string.Concat("../file/organ/",Eval("logo"))) %>' class="card-img-top" alt="Logo ONG">
                                    <div class="card-body">
                                        <h5 class="card-title"><%# Eval("nom") %></h5>
                                       <hr />
                                        <!-- Liste des domaines d’intervention -->
                                        <ul class="list-unstyled small text-muted">
                                            <asp:Repeater ID="rptDomaines" runat="server">
                                                <ItemTemplate>
                                                    <li><i class="bi bi-check-circle-fill"></i><%# Eval("libelle") %></li>
                                                </ItemTemplate>
                                            </asp:Repeater>
                                        </ul>
                                    </div>
                                    <div class="card-footer bg-transparent border-0 text-end">
                                         <asp:LinkButton class="btn btn-outline-primary btn-sm" runat="server" CommandName="viewong" CommandArgument='<%# Eval("name") %>'>Lire Plus<i class="bi bi-arrow-right"></i></asp:LinkButton>
                       
                                    </div>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:ListView>

                </div>
            </div>
        </section>


        <!-- Pagination du Blog -->
        <section id="blog-pagination" class="blog-pagination section">
            <div class="container">
                <div class="d-flex justify-content-center">
                    <ul>
                        <li><a href="#"><i class="bi bi-chevron-left"></i></a></li>
                        <li><a href="#">1</a></li>
                        <li><a href="#" class="active">2</a></li>
                        <li><a href="#">3</a></li>
                        <li><a href="#">4</a></li>
                        <li>...</li>
                        <li><a href="#">10</a></li>
                        <li><a href="#"><i class="bi bi-chevron-right"></i></a></li>
                    </ul>
                </div>
            </div>
        </section>
        <!-- Fin Pagination du Blog -->

    </main>
    <style>
        .form-label {
            font-weight: 500;
            color: #008374;
        }

        .btn-primary {
            background-color: #008374;
            border: none;
        }

        .btn-outline-primary {
            border-color: #008374;
            color: #008374;
        }

            .btn-outline-primary:hover {
                background-color: #008374;
                color: #fff;
            }

        .card {
            border: 1px solid #e1e1e1;
            border-radius: 10px;
            overflow: hidden;
            transition: transform 0.2s ease-in-out;
        }

            .card:hover {
                transform: scale(1.02);
            }

        .card-body h5 {
            color: #008374;
            font-weight: 600;
        }

        .card-text {
            font-size: 0.95rem;
            color: #555;
        }

        .list-unstyled li i {
            color: #008374;
            margin-right: 5px;
        }

        .card-img-top {
            height: 300px; /* hauteur fixe */
            width: 100%; /* prend toute la largeur de la carte */
            object-fit: cover; /* conserve le ratio sans déformation */
            object-position: center; /* centre l'image dans le cadre */
            border-top-left-radius: 0.5rem;
            border-top-right-radius: 0.5rem;
        }
    </style>
</asp:Content>
