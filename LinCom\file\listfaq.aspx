﻿<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="listfaq.aspx.cs" Inherits="LinCom.file.listfaq" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
           <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">
                                    <div class="breadcomb-icon">
                                        <i class="notika-icon notika-form"></i>
                                    </div>
                                    <div class="breadcomb-ctn">
                                        <h2>Liste des FAQ</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">
                                    <a data-toggle="tooltip" data-placement="left" href="faq.aspx" title="Ajouter une nouvelle FAQ" class="btn">Nouvelle FAQ</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    <div id="div_msg_success" runat="server" visible="false" class="alert alert-success alert-dismissible fade show" role="alert">
        <span id="msg_success" runat="server"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <div id="div_msg_error" runat="server" visible="false" class="alert alert-danger alert-dismissible fade show" role="alert">
        <span id="msg_error" runat="server"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
	  <!-- Data Table area Start-->
 <div class="normal-table-area">
      <div class="container">
         <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
               <div class="data-table-list">
                   <div class="table-responsive">
                       <asp:GridView ID="gdv" class="table table-striped" runat="server" CssClass="datatbemp"
                         AutoGenerateColumns="False" EmptyDataText="Aucune FAQ trouvée"
                         GridLines="None" width="100%" OnRowCommand="gdv_RowCommand">
                            <AlternatingRowStyle BackColor="#DCDCDC" />
                            <Columns>
                                <asp:TemplateField HeaderText="Question" FooterText="Question">
                                    <ItemTemplate>
                                        <asp:Label ID="lb_question" runat="server" Text='<%# Eval("Question") %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Réponse" FooterText="Réponse">
                                    <ItemTemplate>
                                        <asp:Label ID="lb_reponse" runat="server" Text='<%# Eval("Reponse").ToString().Length > 100 ? Eval("Reponse").ToString().Substring(0, 100) + "..." : Eval("Reponse") %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Statut" FooterText="Statut">
                                    <ItemTemplate>
                                        <span class="badge badge-<%# Eval("statut").ToString() == "publié" ? "success" : (Eval("statut").ToString() == "brouillon" ? "warning" : "secondary") %>">
                                            <%# Eval("statut") %>
                                        </span>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Date Publication" FooterText="Date Publication">
                                    <ItemTemplate>
                                        <asp:Label ID="lb_date" runat="server" Text='<%# Eval("DatePublication", "{0:dd/MM/yyyy}") %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Action" FooterText="Action">
                                    <ItemTemplate>
                                        <asp:Button ID="btnEdit" Height="100px" BackColor="Green" class="btn btn-info btn-fix" CommandName="view" CommandArgument='<%# Eval("id") %>' runat="server" Text="Edit" />
                                        <asp:Button class="btn btn-danger notika-btn-danger" BackColor="Red" ID="btn_del" runat="server" Text="Delete" CommandName="delete" CommandArgument='<%# Eval("id") %>' OnClientClick=" return confirm('Voulez-Vous vraiment supprimer??')" />
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <EditRowStyle Font-Bold="True"></EditRowStyle>
                            <EmptyDataRowStyle HorizontalAlign="Center" Font-Names="century" Font-Size="X-Large"></EmptyDataRowStyle>
                       </asp:GridView>
                   </div>
               </div>
           </div>
         </div>
     </div>
 </div>
 <!-- Data Table area End-->

      <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
 <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.js"></script>
   <script>
       $(document).ready(function () {
           $(".datatbemp").prepend($("<thead></thead>").append($(this).find("tr:first"))).DataTable();
       });
   </script>
</asp:Content>
