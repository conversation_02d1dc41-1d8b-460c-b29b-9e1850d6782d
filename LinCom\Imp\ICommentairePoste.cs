﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ICommentairePoste
    {
        int Ajout(CommentairePoste_Class add);
        void Chargement_GDV(GridView GV_apv);
        void search(GridView GV_apv, string code);
        void afficherDetails(int code, CommentairePoste_Class pr);
        void afficherDetails(string code, CommentairePoste_Class pr);
        int edit(CommentairePoste_Class cl, int id);
        int supprimer(int id);
        void ChargementListview(ListView GV_apv, long id, long idorg, long idmem, string statut, int cd);
        void chargerCommentairePost(DropDownList lst);
        int count(long id, long idpos, int cd);
    }
}
