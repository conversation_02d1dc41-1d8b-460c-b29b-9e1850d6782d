﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class MenuPermissionImp:IMenuPermission
    {
        private menupermission menuPermission = new menupermission();
        int msg;

        public void AfficherDetails(int menuPermissionID, MenuPermission_Class menuPermissionClass)
        {
            using (Connection con = new Connection())
            {
                var mp = con.menupermissions.FirstOrDefault(x => x.MenuPermissionID == menuPermissionID);
                if (mp != null)
                {
                    menuPermissionClass.MenuPermissionID = mp.MenuPermissionID;
                    menuPermissionClass.MenuID = mp.MenuID;
                    menuPermissionClass.PermissionID = mp.PermissionID;
                }
            }
        }

        public int Ajouter(MenuPermission_Class menuPermissionClass)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si l'association existe déjà
                var existant = con.menupermissions.Any(mp =>
                    mp.MenuID == menuPermissionClass.MenuID &&
                    mp.PermissionID == menuPermissionClass.PermissionID);

                if (existant)
                {
                    return -1; // Association déjà existante
                }

                menuPermission.MenuID = menuPermissionClass.MenuID;
                menuPermission.PermissionID = menuPermissionClass.PermissionID;

                try
                {
                    con.menupermissions.Add(menuPermission);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerPermissionsMenu(GridView gdv, int menuID)
        {
            using (Connection con = new Connection())
            {
                var query = from mp in con.menupermissions
                            join m in con.menus on mp.MenuID equals m.MenuID
                            join p in con.permissions on mp.PermissionID equals p.PermissionID
                            join r in con.rolemembres on p.RoleMembreID equals r.RoleMembreID
                            where mp.MenuID == menuID
                            select new
                            {
                                mp.MenuPermissionID,
                                Menu = m.NomMenu,
                                Permission = p.CodePermission,
                                Role = r.NomRole
                            };

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }

        public void chargermenus(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.menupermissions select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner le Menus de la Permissin";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.MenuID.ToString();
                       
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }

        public int Supprimer(int menuPermissionID)
        {
            using (Connection con = new Connection())
            {
                var mp = con.menupermissions.FirstOrDefault(x => x.MenuPermissionID == menuPermissionID);
                if (mp != null)
                {
                    con.menupermissions.Remove(mp);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}