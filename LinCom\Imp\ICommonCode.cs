﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LinCom.Imp
{
    internal interface ICommonCode
    {
        string GenerateSlug(string input);
        string RemoveDiacritics(string text);
        string HasherMotDePasse(string motDePasse);
        bool VerifierMotDePasse(string motDePasseEntree, string motDePasseHache);
        string GenererToken();
        string GetRelativeDate(DateTime date);
        string TronquerTexte(object texteObj, int longueurMax);
        void EnvoyerEmail(string destinataire, string sujet, string message, string fournisseur);
        void EnvoyerEmailTousMembres(long idmem, string messageenvoye, string sujet, int cd);

    }
}
