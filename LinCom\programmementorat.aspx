﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="programmementorat.aspx.cs" Inherits="LinCom.programmementorat" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Titre de la page -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h2 class="display-5 fw-bold">Programmes de Mentorat</h2>
                        <%-- <p class="lead">Rejoignez notre communauté en tant que mentor ou mentoré et développez vos compétences et votre réseau.</p>
                        --%>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Accueil</a></li>
                    <li class="current">Mentorat</li>
                </ol>
            </div>
        </nav>
    </div>

    <main class="main">
        <section class="events py-5 bg-light">
            <div class="container">
                <div class="row g-5">

                    <!-- Colonne gauche : Programmes -->
                    <div class="col-lg-8">
                        <div class="row g-4">

                            <!-- Exemple de carte de mentorat -->
                            <asp:ListView ID="listprogrammentorat" runat="server" OnItemCommand="listprogrammentorat_ItemCommand">
                                <EmptyDataTemplate>Aucune donnée pour le moment.</EmptyDataTemplate>
                                <ItemTemplate>
                                    <div class="col-lg-12">
                                        <div class="card border-0 shadow rounded-4 overflow-hidden">
                                            <div class="row g-0">
                                                <!-- Colonne image -->
                                                <div class="col-md-5">
                                                    <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/programentorat/", Eval("photo"))) %>' alt="Programme de mentorat" class="img-fluid h-100 w-100 object-fit-cover">
                                                </div>

                                                <!-- Colonne contenu -->
                                                <div class="col-md-7">
                                                    <div class="card-body p-4">
                                                        <asp:LinkButton runat="server" class="card-title text-dark fw-bold mb-1" CommandName="view" CommandArgument='<%# Eval("name") %>'>
                                                           <%# HttpUtility.HtmlEncode(Eval("Titre")) %>
                                                           
                                                        </asp:LinkButton>

                                                        <p class="text-muted small fst-italic mb-3">
                                                            Publié 
                                                            <span>
                                                                <asp:Label ID="lbldate" runat="server" Text=' <%# GetRelativeDate(Convert.ToDateTime(Eval("DatePublication"))) %>'></asp:Label></span>

                                                        </p>


                                                        <p class="mb-2">
                                                            <strong class="text-muted">Organisation :</strong>
                                                            <span class="text-dark"><%# Eval("Nomorg") %></span>
                                                        </p>

                                                        <p class="mb-2">
                                                            <strong class="text-muted">Date limite d'inscription :</strong>
                                                            <span class="text-dark"><%# Convert.ToDateTime( Eval("DateModification")).ToString("dd/MM/yyyy") %></span>
                                                        </p>

                                                        <p class="mb-3">
                                                            <strong class="text-muted">Statut :</strong>
                                                            <span class="badge bg-success px-3 py-2 rounded-pill"><%# Eval("EstPublieEvent") %></span>
                                                        </p>

                                                        <p class="text-muted">
                                                            <%# Eval("summery") %>
                                                        </p>

                                                        <div class="d-flex gap-2 mt-3">
                                                            <asp:LinkButton runat="server" ID="brndevenirmentor" class="btn text-white fw-semibold rounded-pill" Style="background-color: #008374;" CommandName="mento" CommandArgument='<%# Eval("name") %>'>
                                                             <i class="bi bi-person-plus me-1"></i>Devenir Mentor
                                                            </asp:LinkButton>
                                                            <asp:LinkButton runat="server" ID="btndevenirmentore" class="btn text-white fw-semibold rounded-pill" Style="background-color: #008374;" CommandName="mentore" CommandArgument='<%# Eval("name") %>'>
                                                               <i class="bi bi-person-plus me-1"></i>Devenir Mentoré
                                                            </asp:LinkButton>
                                                            <asp:LinkButton runat="server" ID="LinkButton1" class="btn text-white fw-semibold rounded-pill" Style="background-color: #008374;" CommandName="lireplu" CommandArgument='<%# Eval("name") %>'>
     <i class="bi bi-person-plus me-1"></i>Lire Plus
                                                            </asp:LinkButton>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </ItemTemplate>
                            </asp:ListView>


                            <!-- Dupliquer d'autres cartes ici selon besoin -->

                        </div>

                        <!-- Pagination -->
                        <nav class="mt-5 d-flex justify-content-center">
                            <ul class="pagination pagination-rounded gap-2 mb-0">
                                <li class="page-item"><a class="page-link" href="#"><i class="bi bi-chevron-left"></i></a></li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#"><i class="bi bi-chevron-right"></i></a></li>
                            </ul>
                        </nav>
                    </div>

                    <!-- Colonne droite : Recherche + récents -->
                    <div class="col-lg-4">
                        <!-- Filtres -->
                        <div class="card mb-4 shadow-sm rounded-4 p-3">
                            <h5 class="fw-bold mb-3">Filtrer les programmes</h5>
                            <div class="d-grid gap-3">
                                <asp:DropDownList ID="drpdstatut" runat="server" class="form-select rounded-pill">
                                    <asp:ListItem Value="-1">Statut du Programme de Mentorat</asp:ListItem>
                                    <asp:ListItem Value="ouvert">Ouvert</asp:ListItem>
                                    <asp:ListItem Value="a venir">A venir</asp:ListItem>
                                    <asp:ListItem Value="cloturé">Cloturé</asp:ListItem>
                                </asp:DropDownList>

                                <input type="text" runat="server" id="txtsearch" class="form-control rounded-pill" placeholder="Chercher par nom du programme">
                                <input type="month" runat="server" id="txtmoisannee" class="form-control rounded-pill">
                                <button class="btn btn-primary rounded-pill fw-semibold">Rechercher</button>
                            </div>
                        </div>

                        <!-- Programmes récents -->
                        <div class="card mb-4 shadow-sm rounded-4 p-3">
                            <h5 class="fw-bold mb-3">Nouveaux programmes</h5>
                            <!-- Categories Widget -->
                            <div class="categories-widget widget-item">

                                <ul class="mt-3">
                                    <asp:ListView ID="listcategorie" runat="server" OnItemCommand="listcategorie_ItemCommand">
                                        <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                                        <ItemTemplate>
                                            <li>
                                                <asp:LinkButton runat="server" CommandName="viewdom" CommandArgument='<%# Eval("Id") %>'>
                        <%#  HttpUtility.HtmlEncode(Eval("Libelle")) %>
                        <span>(<b><%#  HttpUtility.HtmlEncode(Eval("NombrePosts")) %> </b>)</span>

                                                </asp:LinkButton>

                                            </li>

                                        </ItemTemplate>
                                    </asp:ListView>

                                </ul>

                            </div>
                            <!--/Categories Widget -->
                        </div>

                        <!-- Publicité -->
                        <div class="bg-white shadow-sm p-4 rounded-4">
                            <h5 class="fw-bold mb-3 text-center">Publicité</h5>
                            <div id="carouselPubMentorat" class="carousel slide" data-bs-ride="carousel">
                                <div class="carousel-inner rounded">
                                    <div class="carousel-item active">
                                        <img src="assets/img/mentorat/skills.png" class="d-block w-100" alt="Publicité 1">
                                    </div>
                                    <div class="carousel-item">
                                        <img src="assets/img/mentorat/skills.png" class="d-block w-100" alt="Publicité 2">
                                    </div>
                                </div>
                                <button class="carousel-control-prev" type="button" data-bs-target="#carouselPubMentorat" data-bs-slide="prev">
                                    <span class="carousel-control-prev-icon"></span>
                                </button>
                                <button class="carousel-control-next" type="button" data-bs-target="#carouselPubMentorat" data-bs-slide="next">
                                    <span class="carousel-control-next-icon"></span>
                                </button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </section>
    </main>

</asp:Content>
