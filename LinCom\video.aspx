﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="video.aspx.cs" Inherits="LinCom.video" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
                    <!-- Page Title -->
<div class="page-title">
    <div class="heading">
        <div class="container">
            <div class="row d-flex justify-content-center text-center">
                <div class="col-lg-8">
                  <h1 class="display-4 fw-bold">Contenus Multimédia</h1>
<p class="lead mt-3">Sélection de vidéos inspirantes et éducatives pour les jeunes leaders.</p>
   </div>
            </div>
        </div>
    </div>
    <nav class="breadcrumbs">
        <div class="container">
            <ol>
                <li><a href="home.aspx">Home</a></li>
                <li><a href="#">Espace FNUAP</a></li>
                <li class="current"><a href="ong.aspx">Communiqués</a></li>
            </ol>
        </div>
    </nav>
</div>
<!-- End Page Title -->
     <main class="main">

<!-- FILTRES -->
<section class="filters bg-light py-4 sticky-top shadow-sm z-2">
  <div class="container d-flex flex-wrap gap-3 justify-content-center align-items-center">
    <div class="d-flex align-items-center gap-2">
      <i class="bi bi-funnel-fill text-primary"></i>
      <select class="form-select rounded-pill px-4" style="max-width: 220px;">
        <option>Toutes catégories</option>
        <option>Éducation</option>
        <option>Motivation</option>
        <option>Ateliers</option>
      </select>
    </div>
    <div class="d-flex align-items-center gap-2">
      <i class="bi bi-calendar-date text-primary"></i>
      <input type="date" class="form-control rounded-pill px-4" style="max-width: 220px;">
    </div>
    <button class="btn btn-outline-primary rounded-pill px-4 fw-bold"><i class="bi bi-search"></i> Filtrer</button>
  </div>
</section>

<!-- GRILLE DE VIDÉOS YOUTUBE -->
<section class="videos-grid py-5 bg-white">
  <div class="container">
    <div class="row g-4">

      
      <!-- VIDÉO 1 - FNUAP Burundi -->
            <asp:ListView ID="lvVideos" runat="server">
<ItemTemplate>
      <div class="col-md-6 col-lg-4">
        <div class="card border-0 shadow-lg rounded-4 overflow-hidden h-100 d-flex flex-column">
          <div class="ratio ratio-16x9">
           <iframe src='https://www.youtube.com/embed/<%# Eval("videoId") %>' allowfullscreen></iframe>
               </div>
          <div class="card-body d-flex flex-column">
     <h5 class="card-title fw-bold"><%# Eval("title") %></h5>
     <p class="card-text text-muted small"><%# Eval("description") %></p>
     <a class="btn btn-primary w-100 rounded-pill fw-semibold" href='https://www.youtube.com/watch?v=<%# Eval("videoId") %>' target="_blank">
         <i class="bi bi-box-arrow-up-right me-1"></i> Voir sur YouTube
     </a>
 </div>
        </div>
      </div>    </ItemTemplate>
</asp:ListView>


    </div>
  </div>
</section>

<!-- PAGINATION -->
<section class="pagination py-4 bg-light">
  <div class="container d-flex justify-content-center">
    <nav>
      <ul class="pagination gap-2">
        <li class="page-item"><a class="page-link shadow-sm" href="#"><i class="bi bi-chevron-left"></i></a></li>
        <li class="page-item active"><a class="page-link shadow-sm" href="#">1</a></li>
        <li class="page-item"><a class="page-link shadow-sm" href="#">2</a></li>
        <li class="page-item"><a class="page-link shadow-sm" href="#">3</a></li>
        <li class="page-item"><a class="page-link shadow-sm" href="#"><i class="bi bi-chevron-right"></i></a></li>
      </ul>
    </nav>
  </div>
</section>
</main>
<!-- STYLES SUPPLÉMENTAIRES -->
<style>
  .hero-section {
    min-height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .card:hover {
    transform: translateY(-6px);
    transition: all 0.3s ease;
  }

  .pagination .page-link {
    border-radius: 50%;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .pagination .active .page-link {
    background-color: #0d6efd;
    color: white;
    border-color: transparent;
  }
</style>

</asp:Content>
