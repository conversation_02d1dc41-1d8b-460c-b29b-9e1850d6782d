﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IPorgrammeInitiative
    {
        int add(ProgrammesEtInitiative_Class add);
        void Chargement_GDV(GridView GV_apv);
        void search(GridView GV_apv, string code);
        void afficherDetails(int code, ProgrammesEtInitiative_Class pr);
        void afficherDetails(string code, ProgrammesEtInitiative_Class pr);
        int edit(ProgrammesEtInitiative_Class cl, int id);
        int supprimer(int id);

        void chargerProgrammInitiative(DropDownList lst);
        int count();
    }
}
