﻿using LinCom.Class;
using LinCom.file;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class MentoreImp:IMentore
    {
        int msg,n;
        private Mentore mentore = new Mentore();

        public void AfficherDetails(int mentoreId, Mentore_Class mentoreClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Mentores.FirstOrDefault(x => x.MentoreId == mentoreId);
                if (m != null)
                {
                    mentoreClass.MentoreId = m.MentoreId;
                    mentoreClass.MembreId = m.MembreId;
                    mentoreClass.ProgrammeMentoratId = m.ProgrammeMentoratId;
                    mentoreClass.status = m.status;
                    mentoreClass.OrganisationId = m.OrganisationId;
                    mentoreClass.DateAccept = m.DateAccept;
                    mentoreClass.DateInscription = m.DateInscription;
                    mentoreClass.rate = m.rate;
                }
            }
        }
        public int count(int id, long idprog, long idmem, long idorg, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var b = con.Mentores.Count(l => l.ProgrammeMentoratId == idprog);

                    n = b;
                }


            }
            return n;
        }


        public int Ajouter(Mentore_Class mentoreClass)
        {
            using (Connection con = new Connection())
            {
                mentore.MembreId = mentoreClass.MembreId;
                mentore.ProgrammeMentoratId = mentoreClass.ProgrammeMentoratId;
                mentore.status = mentoreClass.status ?? "actif";
                mentore.DateInscription = mentoreClass.DateInscription;
                mentore.DateAccept = mentoreClass.DateAccept;
                mentore.OrganisationId = mentoreClass.OrganisationId;
                mentore.rate = mentoreClass.rate;


                try
                {
                    con.Mentores.Add(mentore);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerMentoreGridview(GridView gdv, int id, long idprog, long idorg, string name, string status, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var query = from m in con.Mentores
                                join mb in con.Membres on m.MembreId equals mb.MembreId into membres
                                from membre in membres.DefaultIfEmpty()
                                join p in con.Programmementorats on m.ProgrammeMentoratId equals p.ProgrammeMentoratId into programmes
                                from programme in programmes.DefaultIfEmpty()
                                where m.ProgrammeMentoratId == idprog && m.OrganisationId == idorg && m.status == status
                                select new
                                {
                                    id = m.MentoreId,
                                    idmembre = m.MembreId,
                                    Membre = membre != null ? membre.Nom + " " + membre.Prenom : "Inconnu",

                                    PostId = m.ProgrammeMentoratId,
                                    Programme = programme != null ? programme.Titre : "Aucun",
                                    statut = m.status,
                                    DateInscription = m.DateInscription,
                                    DateAccept = m.DateAccept,
                                    OrganisationId = m.OrganisationId,
                                    rate = m.rate,

                                };

                    gdv.DataSource = query.OrderBy(x => x.Membre).ToList();
                    gdv.DataBind();
                }
              
            }
        }

        public void ChargerMentoreListview(ListView gdv, int id, long idprog, long idorg, string name, string status, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var query = from m in con.Mentores
                                join mb in con.Membres on m.MembreId equals mb.MembreId into membres
                                from membre in membres.DefaultIfEmpty()
                                join p in con.Programmementorats on m.ProgrammeMentoratId equals p.ProgrammeMentoratId into programmes
                                from programme in programmes.DefaultIfEmpty()
                                where m.ProgrammeMentoratId == idprog && m.status == status
                                select new
                                {
                                    id = m.MentoreId,
                                    idmembre = m.MembreId,
                                    Membre = membre != null ? membre.Nom + " " + membre.Prenom : "Inconnu",
                                    Photomembre=membre.PhotoProfil,
                                    PostId = m.ProgrammeMentoratId,
                                    Programme = programme != null ? programme.Titre : "Aucun",
                                    statut = m.status,
                                    DateInscription = m.DateInscription,
                                    DateAccept = m.DateAccept,
                                    OrganisationId = m.OrganisationId,
                                    rate = m.rate,

                                };

                    gdv.DataSource = query.OrderBy(x => x.Membre).ToList();
                    gdv.DataBind();
                }

            }
        }

        public void ChargerMentoresParProgramme(GridView gdv, int programmeMentoratId)
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Mentores
                            join mb in con.Membres on m.MembreId equals mb.MembreId into membres
                            from membre in membres.DefaultIfEmpty()
                            join p in con.Programmementorats on m.ProgrammeMentoratId equals p.ProgrammeMentoratId into programmes
                            from programme in programmes.DefaultIfEmpty()
                            where m.ProgrammeMentoratId == programmeMentoratId
                            select new
                            {
                                id = m.MentoreId,
                                idmembre = m.MembreId,
                                Membre = membre != null ? membre.Nom + " " + membre.Prenom : "Inconnu",

                                PostId = m.ProgrammeMentoratId,
                                Programme = programme != null ? programme.Titre : "Aucun",
                                statut = m.status,
                                DateInscription = m.DateInscription,
                                DateAccept = m.DateAccept,
                                OrganisationId = m.OrganisationId,
                                rate = m.rate,
                            };

                gdv.DataSource = query.OrderBy(x => x.Membre).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerMentoresParMembre(GridView gdv, long membreId)
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Mentores
                            join mb in con.Membres on m.MembreId equals mb.MembreId into membres
                            from membre in membres.DefaultIfEmpty()
                            join p in con.Programmementorats on m.ProgrammeMentoratId equals p.ProgrammeMentoratId into programmes
                            from programme in programmes.DefaultIfEmpty()
                            where m.MembreId == membreId
                            select new
                            {
                                id = m.MentoreId,
                                idmembre = m.MembreId,
                                Membre = membre != null ? membre.Nom + " " + membre.Prenom : "Inconnu",

                                PostId = m.ProgrammeMentoratId,
                                Programme = programme != null ? programme.Titre : "Aucun",
                                statut = m.status,
                                DateInscription = m.DateInscription,
                                DateAccept = m.DateAccept,
                                OrganisationId = m.OrganisationId,
                                rate = m.rate,

                            };

                gdv.DataSource = query.OrderBy(x => x.Programme).ToList();
                gdv.DataBind();
            }
        }

        public void chargerMentore(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Mentores select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner le Mentore";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.MentoreId.ToString();
                        
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }
        public int count(long id, long idorg, long idmem, string code, string statut,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var b = (from l in con.Mentores
                             where l.ProgrammeMentoratId == id
                             select l).Count();
                    n = b;
                }
                else if (cd==1)
                {
                    var b = (from l in con.Mentores
                             where l.ProgrammeMentoratId == id && l.OrganisationId == idorg && l.MembreId == idmem
                             select l).Count();
                    n = b;
                }
                   
            }
            return n;
        }


        public int Modifier(Mentore_Class mentoreClass,int id, long idorg, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var m = con.Mentores.FirstOrDefault(x => x.MentoreId == id);
                    if (m != null)
                    {
                        m.MembreId = mentoreClass.MembreId;
                        m.ProgrammeMentoratId = mentoreClass.ProgrammeMentoratId;
                        m.status = mentoreClass.status;

                        try
                        {
                            if (con.SaveChanges() == 1)
                            {
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return msg = 0;
                        }
                    }
                }
               else if (cd==1)
                {
                    var m = con.Mentores.FirstOrDefault(x => x.MentoreId == id);
                    if (m != null)
                    {
                        m.DateAccept = mentoreClass.DateAccept;
                        
                        try
                        {
                            if (con.SaveChanges() == 1)
                            {
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return msg = 0;
                        }
                    }
                }
                    return msg;
            }
        }

        public int Supprimer(int mentoreId)
        {
            using (Connection con = new Connection())
            {
                var m = con.Mentores.FirstOrDefault(x => x.MentoreId == mentoreId);
                if (m != null)
                {
                    con.Mentores.Remove(m);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}