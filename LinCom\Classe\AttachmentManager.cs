using System;
using System.Collections.Generic;
using System.IO;
using System.Web;
using System.Drawing;
using System.Drawing.Imaging;

namespace LinCom.Classe
{
    /// <summary>
    /// Gestionnaire des pièces jointes pour les messages
    /// </summary>
    public class AttachmentManager
    {
        private static readonly string UPLOAD_FOLDER = "~/Uploads/Messagerie/";
        private static readonly string THUMBNAILS_FOLDER = "~/Uploads/Messagerie/Thumbnails/";

        /// <summary>
        /// Types de fichiers supportés
        /// </summary>
        public enum TypeFichier
        {
            Image,
            Document,
            Audio,
            Video,
            Archive,
            Autre
        }

        /// <summary>
        /// Informations sur un fichier uploadé
        /// </summary>
        public class FichierUploade
        {
            public string NomOriginal { get; set; }
            public string NomSauvegarde { get; set; }
            public string CheminComplet { get; set; }
            public string UrlAcces { get; set; }
            public long TailleOctets { get; set; }
            public TypeFichier Type { get; set; }
            public string Extension { get; set; }
            public string CheminThumbnail { get; set; }
            public bool EstImage { get; set; }
        }

        /// <summary>
        /// Upload un fichier et retourne les informations
        /// </summary>
        public static FichierUploade UploadFichier(HttpPostedFile fichier, long membreId)
        {
            if (fichier == null || fichier.ContentLength == 0)
                throw new ArgumentException("Aucun fichier fourni");

            // Validation de la taille
            if (!MessagerieConfig.EstTailleFichierValide(fichier.ContentLength))
            {
                throw new InvalidOperationException(
                    MessagerieConfig.FormatMessage(MessagerieConfig.Messages.FichierTropGros,
                    MessagerieConfig.MaxPieceJointeTailleMo));
            }

            // Validation de l'extension
            string extension = Path.GetExtension(fichier.FileName).ToLower();
            if (!MessagerieConfig.EstExtensionAutorisee(extension))
            {
                throw new InvalidOperationException(MessagerieConfig.Messages.ExtensionNonAutorisee);
            }

            // Créer les dossiers si nécessaire
            CreerDossiersUpload();

            // Générer un nom unique
            string nomUnique = $"{membreId}_{DateTime.Now:yyyyMMdd_HHmmss}_{Guid.NewGuid().ToString("N").Substring(0, 8)}{extension}";
            string uploadPath = HttpContext.Current.Server.MapPath(UPLOAD_FOLDER);
            string cheminComplet = Path.Combine(uploadPath, nomUnique);

            // Sauvegarder le fichier
            fichier.SaveAs(cheminComplet);

            var fichierInfo = new FichierUploade
            {
                NomOriginal = fichier.FileName,
                NomSauvegarde = nomUnique,
                CheminComplet = cheminComplet,
                UrlAcces = VirtualPathUtility.ToAbsolute(UPLOAD_FOLDER + nomUnique),
                TailleOctets = fichier.ContentLength,
                Extension = extension,
                Type = DeterminerTypeFichier(extension),
                EstImage = EstImage(extension)
            };

            // Créer une miniature si c'est une image
            if (fichierInfo.EstImage)
            {
                try
                {
                    string thumbnailPath = HttpContext.Current.Server.MapPath(THUMBNAILS_FOLDER);
                    string cheminThumbnail = CreerThumbnail(cheminComplet, thumbnailPath, nomUnique);
                    fichierInfo.CheminThumbnail = VirtualPathUtility.ToAbsolute(THUMBNAILS_FOLDER + Path.GetFileName(cheminThumbnail));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erreur création thumbnail: {ex.Message}");
                }
            }

            return fichierInfo;
        }

        /// <summary>
        /// Détermine le type de fichier basé sur l'extension
        /// </summary>
        public static TypeFichier DeterminerTypeFichier(string extension)
        {
            var images = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
            var documents = new[] { ".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt" };
            var audio = new[] { ".mp3", ".wav", ".ogg", ".m4a", ".aac" };
            var video = new[] { ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm" };
            var archives = new[] { ".zip", ".rar", ".7z", ".tar", ".gz" };

            if (Array.Exists(images, ext => ext.Equals(extension, StringComparison.OrdinalIgnoreCase)))
                return TypeFichier.Image;
            if (Array.Exists(documents, ext => ext.Equals(extension, StringComparison.OrdinalIgnoreCase)))
                return TypeFichier.Document;
            if (Array.Exists(audio, ext => ext.Equals(extension, StringComparison.OrdinalIgnoreCase)))
                return TypeFichier.Audio;
            if (Array.Exists(video, ext => ext.Equals(extension, StringComparison.OrdinalIgnoreCase)))
                return TypeFichier.Video;
            if (Array.Exists(archives, ext => ext.Equals(extension, StringComparison.OrdinalIgnoreCase)))
                return TypeFichier.Archive;

            return TypeFichier.Autre;
        }

        /// <summary>
        /// Vérifie si le fichier est une image
        /// </summary>
        public static bool EstImage(string extension)
        {
            var images = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
            return Array.Exists(images, ext => ext.Equals(extension, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Crée une miniature pour une image
        /// </summary>
        private static string CreerThumbnail(string cheminOriginal, string dossierThumbnail, string nomFichier)
        {
            const int TAILLE_THUMBNAIL = 150;
            
            string nomThumbnail = "thumb_" + nomFichier;
            string cheminThumbnail = Path.Combine(dossierThumbnail, nomThumbnail);

            using (var imageOriginale = Image.FromFile(cheminOriginal))
            {
                // Calculer les nouvelles dimensions en gardant le ratio
                int nouvelleWidth, nouvelleHeight;
                if (imageOriginale.Width > imageOriginale.Height)
                {
                    nouvelleWidth = TAILLE_THUMBNAIL;
                    nouvelleHeight = (int)(imageOriginale.Height * (double)TAILLE_THUMBNAIL / imageOriginale.Width);
                }
                else
                {
                    nouvelleHeight = TAILLE_THUMBNAIL;
                    nouvelleWidth = (int)(imageOriginale.Width * (double)TAILLE_THUMBNAIL / imageOriginale.Height);
                }

                using (var thumbnail = new Bitmap(nouvelleWidth, nouvelleHeight))
                using (var graphics = Graphics.FromImage(thumbnail))
                {
                    graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                    graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                    graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

                    graphics.DrawImage(imageOriginale, 0, 0, nouvelleWidth, nouvelleHeight);
                    thumbnail.Save(cheminThumbnail, ImageFormat.Jpeg);
                }
            }

            return cheminThumbnail;
        }

        /// <summary>
        /// Supprime un fichier et sa miniature
        /// </summary>
        public static bool SupprimerFichier(string nomFichier)
        {
            try
            {
                string cheminFichier = HttpContext.Current.Server.MapPath(UPLOAD_FOLDER + nomFichier);
                string cheminThumbnail = HttpContext.Current.Server.MapPath(THUMBNAILS_FOLDER + "thumb_" + nomFichier);

                if (File.Exists(cheminFichier))
                    File.Delete(cheminFichier);

                if (File.Exists(cheminThumbnail))
                    File.Delete(cheminThumbnail);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur suppression fichier: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Obtient l'icône appropriée pour le type de fichier
        /// </summary>
        public static string ObtenirIconeFichier(TypeFichier type)
        {
            switch (type)
            {
                case TypeFichier.Image:
                    return "🖼️";
                case TypeFichier.Document:
                    return "📄";
                case TypeFichier.Audio:
                    return "🎵";
                case TypeFichier.Video:
                    return "🎥";
                case TypeFichier.Archive:
                    return "📦";
                default:
                    return "📎";
            }
        }

        /// <summary>
        /// Formate la taille du fichier pour l'affichage
        /// </summary>
        public static string FormaterTaille(long tailleOctets)
        {
            string[] suffixes = { "B", "KB", "MB", "GB" };
            int suffixIndex = 0;
            double taille = tailleOctets;

            while (taille >= 1024 && suffixIndex < suffixes.Length - 1)
            {
                taille /= 1024;
                suffixIndex++;
            }

            return $"{taille:F1} {suffixes[suffixIndex]}";
        }

        /// <summary>
        /// Valide si un fichier peut être uploadé
        /// </summary>
        public static ValidationResult ValiderFichier(HttpPostedFile fichier)
        {
            if (fichier == null || fichier.ContentLength == 0)
                return new ValidationResult(false, "Aucun fichier sélectionné");

            if (!MessagerieConfig.EstTailleFichierValide(fichier.ContentLength))
                return new ValidationResult(false,
                    MessagerieConfig.FormatMessage(MessagerieConfig.Messages.FichierTropGros,
                    MessagerieConfig.MaxPieceJointeTailleMo));

            string extension = Path.GetExtension(fichier.FileName).ToLower();
            if (!MessagerieConfig.EstExtensionAutorisee(extension))
                return new ValidationResult(false, MessagerieConfig.Messages.ExtensionNonAutorisee);

            return new ValidationResult(true, "Fichier valide");
        }

        /// <summary>
        /// Crée les dossiers d'upload s'ils n'existent pas
        /// </summary>
        private static void CreerDossiersUpload()
        {
            try
            {
                string uploadPath = HttpContext.Current.Server.MapPath(UPLOAD_FOLDER);
                string thumbnailPath = HttpContext.Current.Server.MapPath(THUMBNAILS_FOLDER);

                if (!Directory.Exists(uploadPath))
                {
                    Directory.CreateDirectory(uploadPath);
                    System.Diagnostics.Debug.WriteLine($"Dossier créé: {uploadPath}");
                }

                if (!Directory.Exists(thumbnailPath))
                {
                    Directory.CreateDirectory(thumbnailPath);
                    System.Diagnostics.Debug.WriteLine($"Dossier créé: {thumbnailPath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur création dossiers: {ex.Message}");
                throw new InvalidOperationException($"Impossible de créer les dossiers d'upload: {ex.Message}");
            }
        }
    }
}
