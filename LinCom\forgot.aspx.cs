﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Security.Cryptography;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class forgot : System.Web.UI.Page
    {
        Membre_Class mem = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        IMembre obj = new MembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IRoleMembre objrl = new RoleMembreImp();
        RoleMembre_Class rl = new RoleMembre_Class();
        CommonCode co = new CommonCode();
        int info;
        protected void Page_Load(object sender, EventArgs e)
        {
            initial_msg();
        }
        public void DemanderReinitialisationMotDePasse()
        {
            try
            {

          
            if (txt_usernm.Value=="") {
                div_msg_error.Visible = true; msg_error.InnerText = "Renseignez tous les champs";

            }
            else
            {
                // Vérifier si l'utilisateur existe
                info = obj.Connect(mem, txt_usernm.Value, "", 0);

                if (info <=0)
                {
                        div_msg_error.Visible = true; msg_error.InnerText = "Aucun utilisateur trouvé avec cet email.";
                        return;
                }

                // Génération du token sécurisé
                string token = co.GenererToken();
                DateTime expiration = DateTime.Now.AddHours(1); // 1 heure de validité

                // Mise à jour du membre
                mem.ResetToken = token;
                mem.ResetTokenExpiry = expiration;

                obj.Modifier(mem,txt_usernm.Value,-1,1);

                // Création du lien de réinitialisation
                string lienReinitialisation = $"{HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority)}/reset.aspx?token={token}";

                // Envoi de l'email
                EnvoyerEmailReinitialisation(txt_usernm.Value, lienReinitialisation);

                div_msg_succes.Visible = true; msg_succes.InnerText = "Veuillez trouver le lien de reinitialisation dans votre boite email.";

            }
            }  
           
            catch (Exception ex)
            {
                // Log l'erreur pour analyse
                System.Diagnostics.Debug.WriteLine("Erreur lors de l'envoi de l'email : " + ex.Message);

            }
        }
        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;

        }

        private void EnvoyerEmailReinitialisation(string email, string lienReinitialisation)
        {
            try
            {
                SmtpClient smtpClient = new SmtpClient("send.one.com", 587);
                smtpClient.UseDefaultCredentials = false;
                smtpClient.Credentials = new System.Net.NetworkCredential("<EMAIL>", "Newpass1980");
                smtpClient.EnableSsl = true;

                MailMessage mail = new MailMessage();
                mail.From = new MailAddress("<EMAIL>");
                mail.To.Add(email);
                mail.Subject = "Réinitialisation de votre mot de passe";
                mail.Body = $"Bonjour, <br/><br/>Cliquez sur le lien suivant pour réinitialiser votre mot de passe :<br/><a href='{lienReinitialisation}'>{lienReinitialisation}</a><br/><br/>Le lien expirera dans 1 heure.<br/><br/>Cordialement,<br/>Votre équipe de support.";
                mail.IsBodyHtml = true;

                smtpClient.Send(mail);
            }
            catch (Exception ex)
            {
                // Log l'erreur pour analyse
                System.Diagnostics.Debug.WriteLine("Erreur lors de l'envoi de l'email : " + ex.Message);
            }
        }

        protected void btnreng_ServerClick(object sender, EventArgs e)
        {
            DemanderReinitialisationMotDePasse();
        }
    }
}