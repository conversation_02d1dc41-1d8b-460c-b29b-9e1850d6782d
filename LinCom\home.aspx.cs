﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class home : System.Web.UI.Page
    {

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IPoste objpost = new PosteImp();
        Post_Class post = new Post_Class();
        Post_Class pos = new Post_Class();
        IDomainePost objdompost=new DomainePostImp();
        DomainePost_Class dompost=new DomainePost_Class();
        Membre_Class mem=new Membre_Class();
        IMembre objmem=new MembreImp();
        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part=new Partenaire_Class();

        ICommonCode co = new CommonCode();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                affich();

            }
        }
        public string TronquerTexte(object texteObj, int longueurMax)
        {
            return co.TronquerTexte(texteObj, longueurMax);
        }

        void affich()
        {
            objorg.Chargement_GDVL(listinst,"organisation", 2);
            objpost.Chargement_GDV(listpost,-1,-1,"article","publié",1);
          objpost.Chargement_GDV(listevent, -1,-1, "evenement", "publié",1);
            objpart.Chargement_GDV(listpartenaire,"publié");

            //organisation
            nbreorg.Attributes["data-purecounter-start"] = "0";
            nbreorg.Attributes["data-purecounter-end"] = objorg.count(1, -1, "actif", "organisation").ToString(); // Par exemple un nombre dynamique
            nbreorg.Attributes["data-purecounter-duration"] = "1";

            //projet
            nbreprojet.Attributes["data-purecounter-start"] = "0";
            nbreprojet.Attributes["data-purecounter-end"] = objpost.count(1, -1, -1, "publié", "projet").ToString() ; // Par exemple un nombre dynamique
            nbreprojet.Attributes["data-purecounter-duration"] = "1";

            //membre
            nbrememb.Attributes["data-purecounter-start"] = "0";
            nbrememb.Attributes["data-purecounter-end"] = objmem.count(1, "actif", "").ToString(); // Par exemple un nombre dynamique
            nbrememb.Attributes["data-purecounter-duration"] = "1";

            //partenaire
            nbreparte.Attributes["data-purecounter-start"] = "0";
            nbreparte.Attributes["data-purecounter-end"] = objorg.count(1, -1, "actif", "partenaire").ToString(); // Par exemple un nombre dynamique
            nbreparte.Attributes["data-purecounter-duration"] = "1";
            lblTotalOrgs.Text = objorg.count(1,-1,"actif","organisation").ToString();

            // objc.Chargement_GDVL(listinst, 2);
            hiddenOrgsData.Value = objorg.GetOrganisationsJson();
        }
        protected void lvArticles_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                // Récupérer la ligne de données
                var row = (DataRowView)e.Item.DataItem;

                HiddenField hf = (HiddenField)e.Item.FindControl("hfPostId");
                long postId = Convert.ToInt64(hf.Value);
               
                // Trouver le contrôle enfant listdomai
                var listdomai = (ListView)e.Item.FindControl("listdomai");

                objdompost.ChargerListView(listdomai,postId,-1,1,"");
            }
        }

        protected void listv_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            long index = Convert.ToInt64(e.CommandArgument);
            if (e.CommandName == "view")
            {
                Response.Redirect("~/detailassociation.aspx?IDCOOP=" + index);

            }
        }

        protected void listpost_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "view")
            {
               
                objpost.AfficherDetailsname(index, -1,1, pos);
                post.number_of_view = pos.number_of_view + 1;
                objpost.MiseajourData(post,pos.PostId,-1,0);

                Response.Redirect("~/details.aspx?name=" + index);



            }
        }

        protected void listevent_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "viewevent" || e.CommandName == "viewevent1")
            {

                objpost.AfficherDetailsname(index, -1, 1, pos);
                post.number_of_view = pos.number_of_view + 1;
                objpost.MiseajourData(post, pos.PostId, -1, 0);

                Response.Redirect("~/events.aspx?name=" + index);



            }
        }
    }
}