﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IFAQ
    {
        void AfficherDetails(int faqId, FAQ_Class faq);
        void AfficherDetails(string name, FAQ_Class faq);
        int Ajouter(FAQ_Class faq);
        int Modifier(FAQ_Class faq, int id);
        int Supprimer(int faqId);
        void ChargerGridView(GridView gdv, string filtre = "");
        void ChargerFAQPubliques(ListView lv, string statut = "publié");
        int count(string statut = "");
    }
}
