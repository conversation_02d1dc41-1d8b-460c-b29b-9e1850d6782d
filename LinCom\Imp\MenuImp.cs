﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class MenuImp : IMenu
    {
        private menu menuItem = new menu();
        int msg;

        public void AfficherDetails(int menuID, Menu_Class menuClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.menus.FirstOrDefault(x => x.MenuID == menuID);
                if (m != null)
                {
                    menuClass.MenuID = m.MenuID;
                    menuClass.NomMenu = m.NomMenu;
                    menuClass.UrlMenu = m.UrlMenu;
                    menuClass.Icone = m.Icone;
                    menuClass.ParentID = m.ParentID;
                    menuClass.Ordre = m.Ordre;
                }
            }
        }

        public int Ajouter(Menu_Class menuClass)
        {
            using (Connection con = new Connection())
            {
                menuItem.NomMenu = menuClass.NomMenu;
                menuItem.UrlMenu = menuClass.UrlMenu;
                menuItem.Icone = menuClass.Icone;
                menuItem.ParentID = menuClass.ParentID;
                menuItem.Ordre = menuClass.Ordre;

                try
                {
                    con.menus.Add(menuItem);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerMenus(GridView gdv, int? parentID = null)
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.menus
                            where parentID.HasValue ? m.ParentID == parentID : m.ParentID == null
                            select new
                            {
                                m.MenuID,
                                m.NomMenu,
                                m.UrlMenu,
                                m.Icone,
                                Parent = m.ParentID.HasValue ?
                                    con.menus.FirstOrDefault(p => p.MenuID == m.ParentID).NomMenu :
                                    "Menu Principal",
                                m.Ordre,
                                SousMenus = con.menus.Count(sm => sm.ParentID == m.MenuID)
                            };

                gdv.DataSource = query.OrderBy(x => x.Ordre).ToList();
                gdv.DataBind();
            }
        }

        public List<Menu_Class> ObtenirMenusParRole(int roleID)
        {
            using (Connection con = new Connection())
            {
                var query = from mp in con.menupermissions
                            join m in con.menus on mp.MenuID equals m.MenuID
                            join p in con.permissions on mp.PermissionID equals p.PermissionID
                            where p.RoleMembreID == roleID
                            select new Menu_Class
                            {
                                MenuID = m.MenuID,
                                NomMenu = m.NomMenu,
                                UrlMenu = m.UrlMenu,
                                Icone = m.Icone,
                                ParentID = m.ParentID,
                                Ordre = m.Ordre
                            };

                return query.OrderBy(x => x.ParentID).ThenBy(x => x.Ordre).ToList();
            }
        }

        public void chargermenu(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.menus select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner le Menu";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.MenuID.ToString();
                        item.Text = data.NomMenu;
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }


        public int Modifier(Menu_Class menuClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.menus.FirstOrDefault(x => x.MenuID == menuClass.MenuID);
                if (m != null)
                {
                    m.NomMenu = menuClass.NomMenu;
                    m.UrlMenu = menuClass.UrlMenu;
                    m.Icone = menuClass.Icone;
                    m.ParentID = menuClass.ParentID;
                    m.Ordre = menuClass.Ordre;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(int menuID)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si le menu a des sous-menus
                var sousMenus = con.menus.Any(a => a.ParentID == menuID);
                if (sousMenus)
                {
                    return -1; // Impossible de supprimer car il a des sous-menus
                }

                // Vérifier si le menu est associé à des permissions
                var menuPermissions = con.menupermissions.Any(mp => mp.MenuID == menuID);
                var menuPermission = con.menupermissions.Any(mp => mp.MenuID == menuID);
                if (menuPermissions)
                {
                    // Supprimer d'abord les associations avec les permissions
                    var permissionsASupprimer = con.menupermissions.Where(mp => mp.MenuID == menuID).ToList();
                    foreach (var permission in permissionsASupprimer)
                    {
                        con.menupermissions.Remove(permission);
                    }
                }

                var m = con.menus.FirstOrDefault(x => x.MenuID == menuID);
                if (m != null)
                {
                    con.menus.Remove(m);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}